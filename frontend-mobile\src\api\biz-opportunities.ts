import request from "@/utils/request";
import { IOpportunityFollowDetail } from "./biz-opportunity-follow-up";

/** 模块API接口前缀 */
const MODULE_API_PREFIX = "/api/v1/opportunities";

/**
 * 商机模块的API
 */
const BizOpportunitiesAPI = {
  /** 分页获取商机列表 */
  getOpportunitiesPage(params: {
    pageNum: number;
    pageSize: number;
    opportunityStatus?: string;
    [property: string]: any;
  }) {
    return request<typeof params, { list: IOpportunity[]; total: number }>({
      url: `${MODULE_API_PREFIX}/page`,
      method: "get",
      params,
    });
  },

  /** 分页获取商机列表（带最新一次的跟进记录） */
  getOpportunitiesWithFollowPage(params: {
    pageNum: number;
    pageSize: number;
    opportunityStatus?: string;
    [property: string]: any;
  }) {
    return request<
      typeof params,
      { list: Array<IOpportunityWithFollow>; total: number }
    >({
      url: `${MODULE_API_PREFIX}/withFollowPage`,
      method: "get",
      params,
    });
  },

  /** 获取商机详情 */
  getOpportunitiesDetail(id: number) {
    return request<number, IOpportunity>({
      url: `${MODULE_API_PREFIX}/${id}`,
      method: "get",
    });
  },

  /** 获取商机统计信息 */
  getOpportunityStatistic() {
    return request<null, IOpportunityStatistic>({
      url: `${MODULE_API_PREFIX}/statistic`,
      method: "get",
    });
  },
};

export default BizOpportunitiesAPI;

/** 商机详情数据类型 */
export interface IOpportunity {
  /**
   * 实际成交日期
   */
  actualCloseDate?: string;
  /**
   * 竞争对手信息
   */
  competitionInfo?: string;
  /**
   * 联系邮箱
   */
  contactEmail?: string;
  /**
   * 联系人
   */
  contactPerson?: string;
  /**
   * 联系电话
   */
  contactPhone?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 所属部门ID
   */
  deptId?: number;
  /**
   * 部门名称
   */
  deptName?: string;
  /**
   * 预估金额
   */
  estimatedAmount?: number;
  /**
   * 预计成交日期
   */
  estimatedCloseDate?: string;
  /**
   * 跟进记录数量
   */
  followCount?: number;
  /**
   * 主键ID
   */
  id?: number;
  /**
   * 最后跟进时间
   */
  lastFollowDate?: string;
  /**
   * 失败原因
   */
  lostReason?: string;
  /**
   * 失败原因标签
   */
  lostReasonLabel?: string;
  /**
   * 下一步行动计划
   */
  nextAction?: string;
  /**
   * 下次跟进日期
   */
  nextFollowDate?: string;
  /**
   * 商机编码
   */
  opportunityCode?: string;
  /**
   * 商机名称
   */
  opportunityName?: string;
  /**
   * 商机来源
   */
  opportunitySource?: string;
  /**
   * 商机来源标签
   */
  opportunitySourceLabel?: string;
  /**
   * 商机阶段
   */
  opportunityStage?: string;
  /**
   * 商机阶段标签
   */
  opportunityStageLabel?: string;
  /**
   * 商机状态
   */
  opportunityStatus?: string;
  /**
   * 商机状态标签
   */
  opportunityStatusLabel?: string;
  /**
   * 商机类型
   */
  opportunityType?: string;
  /**
   * 商机类型标签
   */
  opportunityTypeLabel?: string;
  /**
   * 关联客户ID
   */
  partnerId?: number;
  /**
   * 客户名称
   */
  partnerName?: string;
  /**
   * 优先级
   */
  priority?: string;
  /**
   * 优先级标签
   */
  priorityLabel?: string;
  /**
   * 感兴趣的产品/服务
   */
  productInterest?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 客户需求描述
   */
  requirements?: string;
  /**
   * 负责人ID
   */
  responsibleUserId?: number;
  /**
   * 负责人姓名
   */
  responsibleUserName?: string;
  /**
   * 标签(多个标签用逗号分隔)
   */
  tags?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 成单概率(%)
   */
  winProbability?: number;
}

/** 带最新一次跟进记录详情的商机详情数据类型 */
export interface IOpportunityWithFollow extends IOpportunity {
  lastFollow?: IOpportunityFollowDetail;
}

/** 商机统计信息VO */
export interface IOpportunityStatistic {
  /**
   * 跟进中商机总数
   */
  activeCount?: string;
  /**
   * 失败商机总数
   */
  lostCount?: string;
  /**
   * 商机总数
   */
  totalCount?: string;
  /**
   * 成功商机总数
   */
  wonCount?: string;
  /**
   * 已归档商机总数
   */
  archivedCount?: string;
  /**
   * 已取消商机总数
   */
  cancelledCount?: string;
}
