import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { MobileHeader } from "@/components/mobile/MobileHeader";
import { BottomNavigation } from "@/components/mobile/BottomNavigation";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Filter, 
  Plus,
  Building,
  Phone,
  Mail,
  MapPin,
  Star,
  Loader2
} from "lucide-react";
import PartnerAPI, { PartnerVO, PartnerQuery } from "@/api/partner";

export default function PartnersPage() {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [partners, setPartners] = useState<PartnerVO[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [pageNum, setPageNum] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // 获取业务伙伴列表
  const fetchPartners = async (isRefresh = false) => {
    if (loading) return;
    
    setLoading(true);
    try {
      const queryParams: PartnerQuery = {
        pageNum: isRefresh ? 1 : pageNum,
        pageSize: 10,
        keywords: searchQuery || undefined,
        partnerType: filterType === "all" ? undefined : filterType,
      };

      const response = await PartnerAPI.getPartnerPage(queryParams);
      
      if (isRefresh) {
        setPartners(response.list);
        setPageNum(2);
      } else {
        setPartners(prev => [...prev, ...response.list]);
        setPageNum(prev => prev + 1);
      }
      
      setTotal(response.total);
      setHasMore(response.list.length === queryParams.pageSize);
    } catch (error) {
      console.error('获取业务伙伴列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 滚动加载更多
  const handleScroll = useCallback(() => {
    if (loading || !hasMore) return;
    
    const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
    const scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight;
    const clientHeight = document.documentElement.clientHeight || window.innerHeight;
    
    if (scrollTop + clientHeight >= scrollHeight - 100) {
      fetchPartners(false);
    }
  }, [loading, hasMore, pageNum]);

  // 初始化加载
  useEffect(() => {
    fetchPartners(true);
  }, []);

  // 搜索和筛选变化时重新加载
  useEffect(() => {
    setPageNum(1);
    fetchPartners(true);
  }, [searchQuery, filterType]);

  // 添加滚动监听
  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  const getTypeColor = (type: string) => {
    switch (type) {
      case "company": return "success";
      case "personal": return "primary";
      case "government": return "accent";
      default: return "secondary";
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case "company": return "企业";
      case "personal": return "个人";
      case "government": return "政府机构";
      default: return "其他";
    }
  };

  return (
    <div className="min-h-screen bg-background pb-20">
      <MobileHeader 
        title="伙伴管理" 
        showSearch={true}
        searchPlaceholder="搜索伙伴..."
        onSearch={(query) => setSearchQuery(query)}
        searchValue={searchQuery}
      />
      
      <div className="p-4 space-y-4">
        {/* 筛选器 */}
        <div className="flex gap-2 overflow-x-auto pb-2">
          {[
            { value: "all", label: "全部" },
            { value: "company", label: "企业" },
            { value: "personal", label: "个人" },
            { value: "government", label: "政府机构" },
            { value: "other", label: "其他" },
          ].map((filter) => (
            <Button
              key={filter.value}
              variant={filterType === filter.value ? "default" : "outline"}
              size="sm"
              className="whitespace-nowrap"
              onClick={() => setFilterType(filter.value)}
            >
              {filter.label}
            </Button>
          ))}
        </div>

        {/* 伙伴列表 */}
        <div className="space-y-3">
          {partners.map((partner) => (
            <div 
              key={partner.id} 
              className="bg-card rounded-lg p-4 shadow-soft cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => navigate(`/partners/${partner.id}`)}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <Building className="h-4 w-4 text-primary" />
                    <h3 className="font-semibold text-sm">{partner.partnerName}</h3>
                  </div>
                  {partner.partnerCode && (
                    <p className="text-xs text-muted-foreground mb-1">
                      编号: {partner.partnerCode}
                    </p>
                  )}
                </div>
                <div className="flex flex-col gap-1">
                  <Badge variant={getTypeColor(partner.partnerType) as any} className="text-xs flex items-center justify-center">
                    {partner.partnerTypeLabel || getTypeText(partner.partnerType)}
                  </Badge>
                  <Badge 
                    variant={partner.status === "active" ? "default" : "secondary"} 
                    className="text-xs flex items-center justify-center"
                  >
                    {partner.status === "active" ? "正常" : "禁用"}
                  </Badge>
                </div>
              </div>

              <div className="space-y-2">
                {partner.contactPerson && (
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-3 w-3 text-muted-foreground" />
                    <span className="text-muted-foreground">{partner.contactPerson}</span>
                    {partner.contactPhone && (
                      <span className="text-muted-foreground">{partner.contactPhone}</span>
                    )}
                  </div>
                )}
                
                {partner.contactEmail && (
                  <div className="flex items-center gap-2 text-sm">
                    <Mail className="h-3 w-3 text-muted-foreground" />
                    <span className="text-muted-foreground">{partner.contactEmail}</span>
                  </div>
                )}

                {partner.address && (
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="h-3 w-3 text-muted-foreground" />
                    <span className="text-muted-foreground">{partner.address}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* 加载状态指示器 */}
        {loading && partners.length > 0 && (
          <div className="flex justify-center items-center py-4">
            <Loader2 className="h-5 w-5 animate-spin mr-2" />
            <span className="text-muted-foreground text-sm">加载中...</span>
          </div>
        )}

        {/* 没有更多数据提示 */}
        {!hasMore && partners.length > 0 && (
          <div className="text-center py-4">
            <p className="text-muted-foreground text-sm">已加载全部数据</p>
          </div>
        )}

        {/* 空状态 */}
        {partners.length === 0 && !loading && (
          <div className="text-center py-8">
            <Building className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">未找到相关伙伴</p>
          </div>
        )}

        {/* 首次加载状态 */}
        {loading && partners.length === 0 && (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span className="text-muted-foreground">加载中...</span>
          </div>
        )}
      </div>

      <BottomNavigation />
    </div>
  );
}