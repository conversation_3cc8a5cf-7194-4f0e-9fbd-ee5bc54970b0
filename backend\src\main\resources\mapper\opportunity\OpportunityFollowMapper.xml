<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.modules.opportunity.mapper.OpportunityFollowMapper">

    <!-- 基础字段映射 -->
    <resultMap id="OpportunityFollowVOMap" type="com.hntsz.boot.modules.opportunity.model.vo.OpportunityFollowVO">
        <id column="id" property="id" />
        <result column="opportunity_id" property="opportunityId" />
        <result column="opportunity_name" property="opportunityName" />
        <result column="opportunity_code" property="opportunityCode" />
        <result column="follow_type" property="followType" />
        <result column="follow_date" property="followDate" />
        <result column="follow_duration" property="followDuration" />
        <result column="contact_person" property="contact<PERSON>erson" />
        <result column="follow_content" property="followContent" />
        <result column="follow_result" property="followResult" />
        <result column="next_action" property="nextAction" />
        <result column="next_follow_date" property="nextFollowDate" />
        <result column="attachment_id" property="attachmentId" />
        <result column="follow_user_id" property="followUserId" />
        <result column="follow_user_name" property="followUserName" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="baseColumns">
        f.id,
        f.opportunity_id,
        f.follow_type,
        f.follow_date,
        f.follow_duration,
        f.contact_person,
        f.follow_content,
        f.follow_result,
        f.next_action,
        f.next_follow_date,
        f.attachment_id,
        f.follow_user_id,
        f.remark,
        f.create_time,
        f.update_time
    </sql>

    <!-- 关联查询字段 -->
    <sql id="joinColumns">
        <include refid="baseColumns"/>,
        o.opportunity_name,
        o.opportunity_code,
        u.nickname as follow_user_name
    </sql>

    <!-- 基础查询表 -->
    <sql id="baseTables">
        opportunity_follow f
        LEFT JOIN opportunity o ON f.opportunity_id = o.id AND o.is_deleted = 0
        LEFT JOIN sys_user u ON f.follow_user_id = u.id AND u.is_deleted = 0
    </sql>

    <!-- 查询条件 -->
    <sql id="whereConditions">
        <where>
            1 = 1
            <if test="query.opportunityId != null">
                AND f.opportunity_id = #{query.opportunityId}
            </if>
            <if test="query.followType != null and query.followType != ''">
                AND f.follow_type = #{query.followType}
            </if>
            <if test="query.followResult != null and query.followResult != ''">
                AND f.follow_result = #{query.followResult}
            </if>
            <if test="query.followUserId != null">
                AND f.follow_user_id = #{query.followUserId}
            </if>
            <if test="query.contactPerson != null and query.contactPerson != ''">
                AND f.contact_person LIKE CONCAT('%', #{query.contactPerson}, '%')
            </if>
            <if test="query.followStartDate != null">
                AND DATE(f.follow_date) >= #{query.followStartDate}
            </if>
            <if test="query.followEndDate != null">
                AND DATE(f.follow_date) &lt;= #{query.followEndDate}
            </if>
            <if test="query.createStartTime != null">
                AND DATE(f.create_time) >= #{query.createStartTime}
            </if>
            <if test="query.createEndTime != null">
                AND DATE(f.create_time) &lt;= #{query.createEndTime}
            </if>
            <if test="query.keywords != null and query.keywords != ''">
                AND (
                    f.follow_content LIKE CONCAT('%', #{query.keywords}, '%')
                    OR f.contact_person LIKE CONCAT('%', #{query.keywords}, '%')
                    OR f.next_action LIKE CONCAT('%', #{query.keywords}, '%')
                    OR o.opportunity_name LIKE CONCAT('%', #{query.keywords}, '%')
                    OR o.opportunity_code LIKE CONCAT('%', #{query.keywords}, '%')
                )
            </if>
        </where>
    </sql>

    <!-- 分页查询商机跟进记录 -->
    <select id="selectFollowPage" resultMap="OpportunityFollowVOMap">
        SELECT <include refid="joinColumns"/>
        FROM <include refid="baseTables"/>
        <include refid="whereConditions"/>
        ORDER BY f.follow_date DESC, f.create_time DESC
    </select>

    <!-- 根据ID查询跟进记录详情 -->
    <select id="selectFollowById" resultMap="OpportunityFollowVOMap">
        SELECT <include refid="joinColumns"/>
        FROM <include refid="baseTables"/>
        WHERE f.id = #{id}
    </select>

    <!-- 根据商机ID查询跟进记录列表 -->
    <select id="selectFollowsByOpportunityId" resultMap="OpportunityFollowVOMap">
        SELECT <include refid="joinColumns"/>
        FROM <include refid="baseTables"/>
        WHERE f.opportunity_id = #{opportunityId}
        ORDER BY f.follow_date DESC, f.create_time DESC
    </select>

    <!-- 根据商机ID删除跟进记录 -->
    <delete id="deleteByOpportunityId">
        DELETE FROM opportunity_follow 
        WHERE opportunity_id = #{opportunityId}
    </delete>

    <!-- 根据跟进人ID查询跟进记录数量 -->
    <select id="countByFollowUserId" resultType="int">
        SELECT COUNT(*) FROM opportunity_follow 
        WHERE follow_user_id = #{followUserId}
    </select>

</mapper>