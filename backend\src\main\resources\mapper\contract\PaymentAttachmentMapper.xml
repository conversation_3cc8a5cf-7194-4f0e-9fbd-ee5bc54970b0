<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.modules.contract.mapper.PaymentAttachmentMapper">

    <!-- 根据付款ID获取文件列表 -->
    <select id="getByPaymentId" resultType="com.hntsz.boot.modules.contract.model.vo.PaymentAttachmentVO">
        SELECT
            pa.id,
            pa.payment_id,
            pa.attachment_id,
            pa.sort,
            a.file_name,
            a.file_path,
            a.file_type,
            a.file_size
        FROM payment_attachment pa
        LEFT JOIN tsz_attachment a ON pa.attachment_id = a.id
        WHERE pa.payment_id = #{paymentId}
        AND pa.is_deleted = 0
        AND a.is_deleted = 0
        ORDER BY pa.sort ASC, pa.create_time ASC
    </select>

    <!-- 根据付款ID删除文件关联 -->
    <update id="deleteByPaymentId">
        UPDATE payment_attachment 
        SET is_deleted = 1, update_time = NOW()
        WHERE payment_id = #{paymentId}
        AND is_deleted = 0
    </update>

    <!-- 根据附件ID删除文件关联 -->
    <update id="deleteByAttachmentId">
        UPDATE payment_attachment 
        SET is_deleted = 1, update_time = NOW()
        WHERE attachment_id = #{attachmentId}
        AND is_deleted = 0
    </update>

    <!-- 删除特定的付款文件关联 -->
    <update id="deletePaymentAttachment">
        UPDATE payment_attachment 
        SET is_deleted = 1, update_time = NOW()
        WHERE payment_id = #{paymentId}
        AND attachment_id = #{attachmentId}
        AND is_deleted = 0
    </update>

</mapper>