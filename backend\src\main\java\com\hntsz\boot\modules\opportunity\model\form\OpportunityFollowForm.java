package com.hntsz.boot.modules.opportunity.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.*;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 商机跟进记录表单对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Schema(description = "商机跟进记录表单对象")
public class OpportunityFollowForm {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "商机ID")
    @NotNull(message = "商机ID不能为空")
    private Long opportunityId;

    @Schema(description = "跟进方式")
    @NotBlank(message = "跟进方式不能为空")
    private String followType;

    @Schema(description = "跟进时间")
    @NotNull(message = "跟进时间不能为空")
    private LocalDateTime followDate;

    @Schema(description = "跟进时长(分钟)")
    @Min(value = 0, message = "跟进时长不能小于0")
    private Integer followDuration;

    @Schema(description = "联系人")
    @Size(max = 100, message = "联系人长度不能超过100个字符")
    private String contactPerson;

    @Schema(description = "跟进内容")
    @NotBlank(message = "跟进内容不能为空")
    private String followContent;

    @Schema(description = "跟进结果")
    private String followResult;

    @Schema(description = "下一步行动计划")
    @Size(max = 500, message = "下一步行动计划长度不能超过500个字符")
    private String nextAction;

    @Schema(description = "下次跟进日期")
    private LocalDate nextFollowDate;

    @Schema(description = "附件ID")
    private String attachmentId;

    @Schema(description = "跟进人ID")
    private Long followUserId;

    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}