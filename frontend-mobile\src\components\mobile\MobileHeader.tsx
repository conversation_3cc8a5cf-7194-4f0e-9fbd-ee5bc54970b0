import { useState } from "react";
import { Bell, Search, Menu, X, ArrowLef<PERSON>, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface MobileHeaderProps {
  title: string;
  showBack?: boolean;
  onBack?: () => void;
  showSearch?: boolean;
  searchPlaceholder?: string;
  onSearch?: (query: string) => void;
  searchValue?: string;
  showLogo?: boolean;
  showRefresh?: boolean;
  onRefresh?: () => void;
  refreshing?: boolean;
}

export function MobileHeader({ 
  title, 
  showBack, 
  onBack,
  showSearch = false,
  searchPlaceholder = "搜索...",
  onSearch,
  searchValue = "",
  showLogo = true,
  showRefresh = false,
  onRefresh,
  refreshing = false
}: MobileHeaderProps) {
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchQuery, setSearchQuery] = useState(searchValue);

  const handleSearchToggle = () => {
    setIsSearchActive(!isSearchActive);
    if (isSearchActive) {
      setSearchQuery("");
      onSearch?.("");
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    onSearch?.(value);
  };

  const handleBackClick = () => {
    if (onBack) {
      onBack();
    } else {
      window.history.back();
    }
  };

  return (
    <header className="sticky top-0 z-40 bg-gradient-primary text-primary-foreground">
      <div className="flex items-center justify-between px-4 py-3">
        {!isSearchActive ? (
          <>
            <div className="flex items-center gap-3">
              {showBack && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackClick}
                  className="text-primary-foreground hover:bg-white/10 h-8 w-8 p-0"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              )}
              {showLogo && (
                <img 
                  src="/logo.png" 
                  alt="Logo" 
                  className="h-8 w-8"
                />
              )}
              <h1 className="text-lg font-semibold truncate">{title}</h1>
            </div>
            
            <div className="flex items-center gap-2">
              {showRefresh && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onRefresh}
                  disabled={refreshing}
                  className="text-primary-foreground hover:bg-white/10 h-8 w-8 p-0"
                >
                  <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                </Button>
              )}
              {showSearch && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSearchToggle}
                  className="text-primary-foreground hover:bg-white/10 h-8 w-8 p-0"
                >
                  <Search className="h-4 w-4" />
                </Button>
              )}
            </div>
          </>
        ) : (
          <div className="flex items-center gap-3 flex-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSearchToggle}
              className="text-primary-foreground hover:bg-white/10 h-8 w-8 p-0"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div className="flex-1">
              <Input
                value={searchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
                placeholder={searchPlaceholder}
                className="bg-white/10 border-white/20 text-primary-foreground placeholder:text-primary-foreground/70 focus:bg-white/20 focus:border-white/40"
                autoFocus
              />
            </div>
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleSearchChange("")}
                className="text-primary-foreground hover:bg-white/10 h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        )}
      </div>
    </header>
  );
}