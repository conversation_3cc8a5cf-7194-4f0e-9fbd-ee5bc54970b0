import request from "@/utils/request";
import { useRqHttp } from "@/hooks/useHttp";

const AUTH_BASE_URL = "/api/v1/auth";

/**
 * 权限相关的API（Axios API）
 */

const AuthAPI = {
  /** 登录接口*/
  login(data: LoginFormData) {
    const formData = new FormData();
    formData.append("username", data.username);
    formData.append("password", data.password);
    formData.append("captchaKey", data.captchaKey);
    formData.append("captchaCode", data.captchaCode);
    return request<FormData, LoginResult>({
      url: `${AUTH_BASE_URL}/login`,
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  /** 刷新 token 接口*/
  refreshToken(refreshToken: string) {
    return request<{ refreshToken: string }, LoginResult>({
      url: `${AUTH_BASE_URL}/refresh-token`,
      method: "post",
      params: { refreshToken: refreshToken },
      headers: {
        Authorization: "no-auth",
      },
    });
  },

  /** 退出登录接口 */
  logout() {
    return request({
      url: `${AUTH_BASE_URL}/logout`,
      method: "delete",
    });
  },

  /** 获取验证码接口*/
  getCaptcha() {
    return request<null, CaptchaInfo>({
      url: `${AUTH_BASE_URL}/captcha`,
      method: "get",
    });
  },

  /** OAuth 登录接口*/
  oauthLogin(provider: string, code: string) {
    const formData = new FormData();
    formData.append("provider", provider);
    formData.append("code", code);
    return request<FormData, LoginResult>({
      url: `${AUTH_BASE_URL}/oauth/login`,
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  /** 获取 OAuth 配置*/
  getOAuthConfig() {
    return request<null, OAuthConfig>({
      url: `${AUTH_BASE_URL}/oauth/config`,
      method: "get",
      headers: {
        Authorization: "no-auth",
      },
    });
  },
};

export default AuthAPI;

/** 使用 React Query 相关的 API */
export const UseAuthAPI = {
  /** 登录 */
  useAuthLogin: (data: LoginFormData) => {
    return useRqHttp(["auth", "login"], () => AuthAPI.login(data));
  },

  /** 刷新 token */
  useAuthRefreshToken: (refreshToken: string) => {
    return useRqHttp(["auth", "refresh-token"], () =>
      AuthAPI.refreshToken(refreshToken)
    );
  },

  /** 退出登录 */
  useAuthLogout: () => {
    return useRqHttp(["auth", "logout"], () => AuthAPI.logout());
  },

  /** 获取验证码 */
  useAuthCaptcha: () => {
    return useRqHttp(["auth", "captcha"], () => AuthAPI.getCaptcha());
  },

  /** OAuth 登录 */
  useAuthOAuthLogin: (provider: string, code: string) => {
    return useRqHttp(["auth", "oauth-login"], () =>
      AuthAPI.oauthLogin(provider, code)
    );
  },

  /** 获取 OAuth 配置 */
  useAuthOAuthConfig: () => {
    return useRqHttp(["auth", "oauth-config"], () => AuthAPI.getOAuthConfig());
  },
};

/** 登录表单数据 */
export interface LoginFormData {
  /** 用户名 */
  username: string;
  /** 密码 */
  password: string;
  /** 验证码缓存key */
  captchaKey: string;
  /** 验证码 */
  captchaCode: string;
  /** 记住我 */
  rememberMe: boolean;
}

/** 登录响应 */
export interface LoginResult {
  /** 访问令牌 */
  accessToken: string;
  /** 刷新令牌 */
  refreshToken: string;
  /** 令牌类型 */
  tokenType: string;
  /** 过期时间(秒) */
  expiresIn: number;
}

/** 验证码信息 */
export interface CaptchaInfo {
  /** 验证码缓存key */
  captchaKey: string;
  /** 验证码图片Base64字符串 */
  captchaBase64: string;
}

/** OAuth 配置信息 */
export interface OAuthConfig {
  /** OAuth 客户端 ID */
  clientId: string;
  /** OAuth 授权端点 URL */
  authorizationUrl: string;
  /** OAuth 回调地址 */
  redirectUri: string;
  /** OAuth 权限范围 */
  scopes: string;
  /** OAuth 登出 URL */
  logoutUrl: string;
  /** 是否启用 OAuth 登录 */
  enabled: boolean;
}
