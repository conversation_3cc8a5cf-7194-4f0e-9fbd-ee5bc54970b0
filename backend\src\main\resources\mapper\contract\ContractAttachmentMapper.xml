<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.modules.contract.mapper.ContractAttachmentMapper">

    <!-- 根据合同ID获取文件列表 -->
    <select id="getByContractId" resultType="com.hntsz.boot.modules.contract.model.vo.ContractAttachmentVO">
        SELECT
            ca.id,
            ca.contract_id,
            ca.attachment_id,
            ca.sort,
            a.file_name,
            a.file_path,
            a.file_type,
            a.file_size
        FROM contract_attachment ca
        LEFT JOIN tsz_attachment a ON ca.attachment_id = a.id
        WHERE ca.contract_id = #{contractId}
        AND ca.is_deleted = 0
        AND a.is_deleted = 0
        ORDER BY ca.sort ASC, ca.create_time ASC
    </select>

    <!-- 根据合同ID删除文件关联 -->
    <update id="deleteByContractId">
        UPDATE contract_attachment 
        SET is_deleted = 1, update_time = NOW()
        WHERE contract_id = #{contractId}
        AND is_deleted = 0
    </update>

    <!-- 根据附件ID删除文件关联 -->
    <update id="deleteByAttachmentId">
        UPDATE contract_attachment 
        SET is_deleted = 1, update_time = NOW()
        WHERE attachment_id = #{attachmentId}
        AND is_deleted = 0
    </update>

</mapper>
