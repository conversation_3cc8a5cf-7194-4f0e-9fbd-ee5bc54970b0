package com.hntsz.boot.modules.contract.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hntsz.boot.modules.contract.model.entity.PaymentAttachment;
import com.hntsz.boot.modules.contract.model.vo.PaymentAttachmentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 付款文件关联Mapper
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Mapper
public interface PaymentAttachmentMapper extends BaseMapper<PaymentAttachment> {

    /**
     * 根据付款ID获取文件列表
     *
     * @param paymentId 付款ID
     * @return 文件列表
     */
    List<PaymentAttachmentVO> getByPaymentId(@Param("paymentId") Long paymentId);

    /**
     * 根据付款ID删除文件关联
     *
     * @param paymentId 付款ID
     * @return 删除数量
     */
    int deleteByPaymentId(@Param("paymentId") Long paymentId);

    /**
     * 根据附件ID删除文件关联
     *
     * @param attachmentId 附件ID
     * @return 删除数量
     */
    int deleteByAttachmentId(@Param("attachmentId") Long attachmentId);

    /**
     * 删除特定的付款文件关联
     *
     * @param paymentId    付款ID
     * @param attachmentId 附件ID
     * @return 删除数量
     */
    int deletePaymentAttachment(@Param("paymentId") Long paymentId, @Param("attachmentId") Long attachmentId);
}