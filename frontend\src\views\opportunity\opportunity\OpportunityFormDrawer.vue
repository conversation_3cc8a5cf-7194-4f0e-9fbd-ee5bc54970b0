<template>
  <el-drawer
    v-model="visible"
    :title="drawerTitle"
    direction="rtl"
    size="900px"
    @close="handleClose"
  >
    <el-form
      ref="dataFormRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="opportunity-form"
    >
      <!-- 基本信息 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>基本信息</h3>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商机名称" prop="opportunityName">
              <el-input v-model="formData.opportunityName" placeholder="请输入商机名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商机编码" prop="opportunityCode">
              <el-input v-model="formData.opportunityCode" placeholder="自动生成" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商机类型" prop="opportunityType">
              <el-select v-model="formData.opportunityType" placeholder="请选择商机类型" style="width: 100%">
                <el-option
                  v-for="item in opportunityTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="商机来源" prop="opportunitySource">
              <el-select v-model="formData.opportunitySource" placeholder="请选择商机来源" style="width: 100%">
                <el-option
                  v-for="item in opportunitySourceOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关联客户" prop="partnerId">
              <el-select
                v-model="formData.partnerId"
                placeholder="请选择客户"
                style="width: 100%"
                filterable
                remote
                :remote-method="searchPartners"
                :loading="partnerLoading"
                clearable
                @focus="handlePartnerFocus"
              >
                <el-option
                  v-for="item in partnerOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="responsibleUserId">
              <el-select
                v-model="formData.responsibleUserId"
                placeholder="请选择负责人"
                style="width: 100%"
                filterable
                remote
                :remote-method="searchUsers"
                :loading="userLoading"
                clearable
                @focus="handleUserFocus"
              >
                <el-option
                  v-for="item in userOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 联系信息 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>联系信息</h3>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="联系人">
              <el-input v-model="formData.contactPerson" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话">
              <el-input v-model="formData.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系邮箱">
              <el-input v-model="formData.contactEmail" placeholder="请输入联系邮箱" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 商机详情 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>商机详情</h3>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="商机阶段">
              <el-select v-model="formData.opportunityStage" placeholder="请选择商机阶段" style="width: 100%">
                <el-option
                  v-for="item in opportunityStageOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="成单概率(%)">
              <el-input-number
                v-model="formData.winProbability"
                :min="0"
                :max="100"
                placeholder="成单概率"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="优先级">
              <el-select v-model="formData.priority" placeholder="请选择优先级" style="width: 100%">
                <el-option label="高" value="high" />
                <el-option label="中" value="medium" />
                <el-option label="低" value="low" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预估金额">
              <el-input-number
                v-model="formData.estimatedAmount"
                :min="0"
                :precision="2"
                placeholder="预估金额"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预计成交日期">
              <el-date-picker
                v-model="formData.estimatedCloseDate"
                type="date"
                placeholder="选择预计成交日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="感兴趣的产品/服务">
          <el-input
            v-model="formData.productInterest"
            type="textarea"
            :rows="2"
            placeholder="请输入感兴趣的产品/服务"
          />
        </el-form-item>
        <el-form-item label="客户需求描述">
          <el-input
            v-model="formData.requirements"
            type="textarea"
            :rows="3"
            placeholder="请输入客户需求描述"
          />
        </el-form-item>
      </el-card>

      <!-- 跟进信息 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>跟进信息</h3>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="竞争对手信息">
              <el-input
                v-model="formData.competitionInfo"
                type="textarea"
                :rows="2"
                placeholder="请输入竞争对手信息"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下一步行动计划">
              <el-input
                v-model="formData.nextAction"
                type="textarea"
                :rows="2"
                placeholder="请输入下一步行动计划"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="下次跟进日期">
              <el-date-picker
                v-model="formData.nextFollowDate"
                type="date"
                placeholder="选择下次跟进日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标签">
              <el-input v-model="formData.tags" placeholder="多个标签用逗号分隔" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-card>
    </el-form>

    <template #footer>
      <div class="action-buttons">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { reactive, ref, computed, watch, onMounted } from "vue";
import {
  OpportunityForm,
} from "@/api/opportunity/opportunity.api";
import OpportunityAPI from "@/api/opportunity/opportunity.api";
import PartnerAPI from "@/api/contract/partner.api";
import UserAPI from "@/api/system/user.api";
import { useDictStore } from "@/store/modules/dict.store";

defineOptions({
  name: "OpportunityFormDrawer",
  inheritAttrs: false,
});

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  opportunityId: {
    type: [String, Number],
    default: undefined,
  },
});

const emit = defineEmits(["update:modelValue", "success"]);

const visible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit("update:modelValue", val);
  },
});

const dictStore = useDictStore();

const dataFormRef = ref(ElForm);

const isEditMode = computed(() => !!formData.id);

const drawerTitle = computed(() => {
  return isEditMode.value ? '修改商机线索' : '新增商机线索';
});

const formData = reactive<OpportunityForm>({
  opportunityName: "",
  opportunityCode: "",
});

const rules = reactive({
  opportunityName: [{ required: true, message: "请输入商机名称", trigger: "blur" }],
});

const opportunityTypeOptions = ref<OptionType[]>([]);
const opportunitySourceOptions = ref<OptionType[]>([]);
const opportunityStageOptions = ref<OptionType[]>([]);
const partnerOptions = ref<OptionType[]>([]);
const userOptions = ref<OptionType[]>([]);
const partnerLoading = ref(false);
const userLoading = ref(false);

function searchPartners(query: string) {
  if (query && query.trim()) {
    partnerLoading.value = true;
    // 这里应该调用支持搜索的API，如果没有则先加载所有选项再过滤
    PartnerAPI.getOptions().then((response: any) => {
      console.log('客户选项API响应:', response);
      let options: OptionType[] = [];

      // 处理不同的响应结构
      if (Array.isArray(response)) {
        options = response;
      } else if (response.data && Array.isArray(response.data)) {
        options = response.data;
      } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
        options = response.data.data;
      }

      // 根据查询条件过滤
      partnerOptions.value = options.filter(option =>
        option.label.toLowerCase().includes(query.toLowerCase())
      );
      console.log('过滤后的客户选项:', partnerOptions.value);
    }).catch((error) => {
      console.error('获取客户选项失败:', error);
      partnerOptions.value = [];
    }).finally(() => {
      partnerLoading.value = false;
    });
  } else {
    // 如果查询为空，加载默认选项
    loadPartnerOptions();
  }
}

function handlePartnerFocus() {
  // 当下拉框获得焦点时，如果没有选项则加载默认选项
  if (partnerOptions.value.length === 0) {
    loadPartnerOptions();
  }
}

function loadPartnerOptions() {
  partnerLoading.value = true;
  PartnerAPI.getOptions().then((response: any) => {
    console.log('加载客户选项API响应:', response);
    let options: OptionType[] = [];

    // 处理不同的响应结构
    if (Array.isArray(response)) {
      options = response;
    } else if (response.data && Array.isArray(response.data)) {
      options = response.data;
    } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
      options = response.data.data;
    }

    partnerOptions.value = options;
    console.log('加载的客户选项:', partnerOptions.value);
  }).catch((error) => {
    console.error('加载客户选项失败:', error);
    partnerOptions.value = [];
  }).finally(() => {
    partnerLoading.value = false;
  });
}

function searchUsers(query: string) {
  if (query && query.trim()) {
    userLoading.value = true;
    // 使用分页接口进行搜索
    UserAPI.getPage({
      pageNum: 1,
      pageSize: 50, // 限制返回数量
      keywords: query.trim()
    }).then((response: any) => {
      console.log('搜索用户API响应:', response);
      let users: any[] = [];

      // 处理分页响应结构 PageResult<UserPageVO[]>
      if (response.data && response.data.list && Array.isArray(response.data.list)) {
        users = response.data.list;
      } else if (response.list && Array.isArray(response.list)) {
        users = response.list;
      } else if (Array.isArray(response)) {
        users = response;
      }

      // 转换为选项格式
      userOptions.value = users.map(user => ({
        value: user.id,
        label: `${user.nickname || user.username} (${user.username})`
      }));
      console.log('搜索后的用户选项:', userOptions.value);
    }).catch((error) => {
      console.error('搜索用户失败:', error);
      userOptions.value = [];
    }).finally(() => {
      userLoading.value = false;
    });
  } else {
    // 如果查询为空，加载默认用户选项
    loadUserOptions();
  }
}

function handleUserFocus() {
  // 当下拉框获得焦点时，如果没有选项则加载默认选项
  if (userOptions.value.length === 0) {
    loadUserOptions();
  }
}

function loadUserOptions() {
  userLoading.value = true;
  // 使用分页接口加载默认用户列表
  UserAPI.getPage({
    pageNum: 1,
    pageSize: 50 // 限制返回数量
  }).then((response: any) => {
    console.log('加载用户选项API响应:', response);
    let users: any[] = [];

    // 处理分页响应结构 PageResult<UserPageVO[]>
    if (response.data && response.data.list && Array.isArray(response.data.list)) {
      users = response.data.list;
    } else if (response.list && Array.isArray(response.list)) {
      users = response.list;
    } else if (Array.isArray(response)) {
      users = response;
    }

    // 转换为选项格式
    userOptions.value = users.map(user => ({
      value: user.id,
      label: `${user.nickname || user.username} (${user.username})`
    }));
    console.log('加载的用户选项:', userOptions.value);
  }).catch((error) => {
    console.error('加载用户选项失败:', error);
    userOptions.value = [];
  }).finally(() => {
    userLoading.value = false;
  });
}

function handleSubmit() {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      const id = formData.id;
      if (id) {
        OpportunityAPI.update(Number(id), formData).then(() => {
          ElMessage.success("商机修改成功");
          emit("success");
          handleClose();
        });
      } else {
        OpportunityAPI.create(formData).then(() => {
          ElMessage.success("商机创建成功");
          emit("success");
          handleClose();
        });
      }
    }
  });
}

function loadFormData(id: string | number) {
  if (id) {
    OpportunityAPI.getFormData(Number(id)).then((data) => {
      Object.assign(formData, data);
    });
  }
}

function handleClose() {
  visible.value = false;
  dataFormRef.value.resetFields();
  Object.assign(formData, {
    id: undefined,
    opportunityName: "",
    opportunityCode: "",
    opportunityType: undefined,
    opportunitySource: undefined,
    partnerId: undefined,
    responsibleUserId: undefined,
    contactPerson: undefined,
    contactPhone: undefined,
    contactEmail: undefined,
    opportunityStage: undefined,
    winProbability: undefined,
    priority: undefined,
    estimatedAmount: undefined,
    estimatedCloseDate: undefined,
    productInterest: undefined,
    requirements: undefined,
    competitionInfo: undefined,
    nextAction: undefined,
    nextFollowDate: undefined,
    tags: undefined,
    remark: undefined,
  });
}

watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      if (props.opportunityId) {
        loadFormData(props.opportunityId);
      } else {
        // 新增时自动生成商机编码
        OpportunityAPI.generateCode().then((response: any) => {
          console.log('生成编码API响应:', response);
          // 根据实际API响应结构处理数据
          if (response && typeof response === 'string') {
            // 直接返回字符串
            formData.opportunityCode = response;
          } else if (response.data && typeof response.data === 'string') {
            // 包装在data中的字符串
            formData.opportunityCode = response.data;
          } else if (response.data && response.data.data) {
            // 双层包装
            formData.opportunityCode = response.data.data;
          } else {
            console.warn('未识别的编码生成响应结构:', response);
            formData.opportunityCode = 'AUTO_GENERATED';
          }
          console.log('设置商机编码:', formData.opportunityCode);
        }).catch((error) => {
          console.error('生成商机编码失败:', error);
          formData.opportunityCode = 'AUTO_GENERATED';
        });
      }
    }
  }
);

onMounted(async () => {
  // 加载字典数据
  await dictStore.loadDictItems("opportunity_type");
  opportunityTypeOptions.value = dictStore.getDictItems("opportunity_type");

  await dictStore.loadDictItems("opportunity_source");
  opportunitySourceOptions.value = dictStore.getDictItems("opportunity_source");

  await dictStore.loadDictItems("opportunity_stage");
  opportunityStageOptions.value = dictStore.getDictItems("opportunity_stage");

  // 初始化加载客户选项
  loadPartnerOptions();

  // 加载用户选项
  loadUserOptions();
});
</script>

<style scoped>
.opportunity-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0 20px;
}

.section-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
