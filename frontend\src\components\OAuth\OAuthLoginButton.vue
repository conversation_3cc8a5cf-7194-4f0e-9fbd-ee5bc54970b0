<template>
  <div class="oauth-login-section">
    <el-button
      class="oauth-login-btn"
      @click="handleOAuthLogin"
    >
      <template #icon>
        <el-icon class="oauth-icon">
          <svg viewBox="0 0 1024 1024" width="16" height="16">
            <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z" fill="currentColor"/>
            <path d="M512 140c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm0 684c-172.2 0-312-139.8-312-312s139.8-312 312-312 312 139.8 312 312-139.8 312-312 312z" fill="currentColor"/>
            <path d="M464 336a48 48 0 1 0 96 0 48 48 0 1 0-96 0zm72 112c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V448z" fill="currentColor"/>
          </svg>
        </el-icon>
      </template>
      <span class="oauth-btn-text">{{ t("login.oauthLogin") }}</span>
    </el-button>
    
    <div class="oauth-description">
      <el-text size="small" class="text-gray-400">
        {{ t("login.oauthDescription") }}
      </el-text>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()

// OAuth 登录处理 - 简单跳转到 OAuth 登录页面
function handleOAuthLogin() {
  // 保留当前页面的 redirect 参数
  const redirect = route.query.redirect as string
  
  if (redirect) {
    router.push(`/auth/login?redirect=${encodeURIComponent(redirect)}`)
  } else {
    router.push('/auth/login')
  }
}
</script>

<style scoped>
.oauth-login-section {
  margin-top: 20px;
}

.oauth-login-btn {
  width: 100%;
  height: 44px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  background: #ffffff;
  color: #333333;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.oauth-login-btn:hover {
  border-color: #409eff;
  background: #f0f9ff;
  color: #409eff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.oauth-login-btn:active {
  transform: translateY(0);
}

.oauth-login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.oauth-login-btn:hover::before {
  left: 100%;
}

.oauth-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #409eff;
}

.oauth-btn-text {
  font-weight: 500;
  letter-spacing: 0.5px;
}

.oauth-description {
  margin-top: 12px;
  text-align: center;
  padding: 0 20px;
}

.oauth-description .el-text {
  line-height: 1.4;
}


/* 暗色模式支持 */
.dark .oauth-login-btn {
  border-color: #4c4d4f;
  background: #2d2d2d;
  color: #ffffff;
}

.dark .oauth-login-btn:hover {
  border-color: #409eff;
  background: #1a1a1a;
  color: #409eff;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .oauth-login-btn {
    height: 40px;
    font-size: 13px;
  }
  
  .oauth-description {
    padding: 0 10px;
  }
}
</style>