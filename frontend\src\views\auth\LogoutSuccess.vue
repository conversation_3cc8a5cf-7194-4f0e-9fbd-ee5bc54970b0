<template>
  <div class="logout-container">
    <div class="logout-card">
      <div class="logout-content">
        <!-- 成功图标 -->
        <div class="logout-icon">
          <el-icon size="64" color="#67c23a">
            <svg viewBox="0 0 1024 1024" width="64" height="64">
              <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z" fill="currentColor"/>
            </svg>
          </el-icon>
        </div>
        
        <!-- 成功消息 -->
        <h2 class="logout-title">{{ t("logout.success") }}</h2>
        <p class="logout-message">{{ t("logout.successMessage") }}</p>
        
        <!-- 登录按钮 -->
        <div class="logout-actions">
          <el-button 
            type="primary" 
            @click="goToLogin"
            class="login-btn"
          >
            {{ t("logout.backToLogin") }}
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 页面底部版权 -->
    <el-text size="small" class="py-2.5! fixed bottom-0 text-center">
      Copyright © 2025 hntsz.com All Rights Reserved.
      <a href="http://beian.miit.gov.cn/" target="_blank">琼ICP备2024044720号</a>
    </el-text>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()

// 跳转到登录页面
function goToLogin() {
  const redirect = route.query.redirect as string
  
  // 根据环境变量决定跳转到哪个登录页面
  if (import.meta.env.VITE_APP_AUTO_REDIRECT_OAUTH === 'true') {
    // 如果启用了自动重定向OAuth，跳转到OAuth登录页面
    if (redirect) {
      router.push(`/auth/login?redirect=${encodeURIComponent(redirect)}`)
    } else {
      router.push('/auth/login')
    }
  } else {
    // 否则跳转到普通登录页面
    if (redirect) {
      router.push(`/login?redirect=${encodeURIComponent(redirect)}`)
    } else {
      router.push('/login')
    }
  }
}
</script>

<style scoped>
.logout-container {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* 添加伪元素作为背景层 */
.logout-container::before {
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  content: "";
  background: url("@/assets/images/login-bg.svg");
  background-position: center center;
  background-size: cover;
}

.logout-card {
  background: white;
  border-radius: 10px;
  box-shadow: var(--el-box-shadow-light);
  backdrop-filter: blur(3px);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.logout-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.logout-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  margin: 0 auto;
  background: rgba(103, 194, 58, 0.1);
  border-radius: 50%;
}

.logout-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.logout-message {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.logout-actions {
  width: 100%;
}

.login-btn {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
}

/* 暗色模式支持 */
.dark .logout-card {
  background: #1f2937;
  border: 1px solid #374151;
}

.dark .logout-title {
  color: #f9fafb;
}

.dark .logout-message {
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .logout-card {
    padding: 30px 20px;
  }
  
  .logout-title {
    font-size: 20px;
  }
  
  .logout-message {
    font-size: 14px;
  }
  
  .login-btn {
    height: 40px;
    font-size: 14px;
  }
}
</style>