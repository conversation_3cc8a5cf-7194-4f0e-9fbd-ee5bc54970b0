<template>
  <div class="app-container">
    <!-- 合同信息 -->
    <el-card shadow="never" class="mb-4">
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">合同信息</span>
          <el-button @click="goBack">
            <el-icon><Back /></el-icon>
            返回合同列表
          </el-button>
        </div>
      </template>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="合同编号">{{ contractInfo.contractNo }}</el-descriptions-item>
        <el-descriptions-item label="合同名称">{{ contractInfo.contractName }}</el-descriptions-item>
        <el-descriptions-item label="合同金额">
          <span v-if="contractInfo.contractAmount">
            ¥{{ contractInfo.contractAmount.toLocaleString() }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="合同状态">
          <el-tag :type="getStatusType(contractInfo.contractStatus)">
            {{ getStatusLabel(contractInfo.contractStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="签署日期">{{ contractInfo.signingDate }}</el-descriptions-item>
        <el-descriptions-item label="负责人">{{ contractInfo.responsibleUserName }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 付款记录列表 -->
    <el-card shadow="never">
      <template #header>
        <div class="flex justify-between">
          <span class="text-lg font-medium">付款记录</span>
          <el-button type="success" @click="openFormDrawer()">
            <el-icon><Plus /></el-icon>
            新增付款记录
          </el-button>
        </div>
      </template>

      <el-table v-loading="loading" :data="pageData">
        <el-table-column label="付款单号" prop="paymentNo" width="150" />
        <el-table-column label="付款类型" prop="paymentTypeLabel" width="120" />
        <el-table-column label="付款方" prop="payerPartnerName" min-width="150" show-overflow-tooltip />
        <el-table-column label="收款方" prop="payeePartnerName" min-width="150" show-overflow-tooltip />
        <el-table-column label="计划金额" prop="plannedAmount" width="120" align="right">
          <template #default="scope">
            <span v-if="scope.row.plannedAmount">
              ¥{{ scope.row.plannedAmount.toLocaleString() }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="实际金额" prop="actualAmount" width="120" align="right">
          <template #default="scope">
            <span v-if="scope.row.actualAmount">
              ¥{{ scope.row.actualAmount.toLocaleString() }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="计划日期" prop="plannedDate" width="120" />
        <el-table-column label="实际日期" prop="actualDate" width="120" />
        <el-table-column label="付款状态" width="100">
          <template #default="scope">
            <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)">
              {{ getPaymentStatusLabel(scope.row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="发票状态" width="100">
          <template #default="scope">
            <el-tag :type="getInvoiceStatusType(scope.row.invoiceStatus)">
              {{ getInvoiceStatusLabel(scope.row.invoiceStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="openFormDrawer(scope.row.id)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button link type="primary" size="small" @click="openDetailDrawer(scope.row.id)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button link type="danger" size="small" @click="handleDelete(scope.row.id)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <!-- 表单抽屉 -->
    <el-drawer
      v-model="formDrawer.visible"
      :title="formDrawer.title"
      direction="rtl"
      size="800px"
      @close="closeFormDrawer"
    >
      <el-form
        ref="dataFormRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="付款单号" prop="paymentNo">
              <el-input v-model="formData.paymentNo" placeholder="请输入付款单号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款类型" prop="paymentType">
              <el-select v-model="formData.paymentType" placeholder="请选择付款类型" style="width: 100%">
                <el-option
                  v-for="item in paymentTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="付款方" prop="payerPartnerId">
              <el-select v-model="formData.payerPartnerId" placeholder="请选择付款方" style="width: 100%" filterable>
                <el-option
                  v-for="item in partnerOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款方" prop="payeePartnerId">
              <el-select v-model="formData.payeePartnerId" placeholder="请选择收款方" style="width: 100%" filterable>
                <el-option
                  v-for="item in partnerOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计划金额" prop="plannedAmount">
              <el-input-number
                v-model="formData.plannedAmount"
                :precision="2"
                :min="0"
                style="width: 100%"
                placeholder="请输入计划金额"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际金额" prop="actualAmount">
              <el-input-number
                v-model="formData.actualAmount"
                :precision="2"
                :min="0"
                style="width: 100%"
                placeholder="请输入实际金额"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计划日期" prop="plannedDate">
              <el-date-picker
                v-model="formData.plannedDate"
                type="date"
                placeholder="请选择计划日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际日期" prop="actualDate">
              <el-date-picker
                v-model="formData.actualDate"
                type="date"
                placeholder="请选择实际日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="付款状态" prop="paymentStatus">
              <el-select v-model="formData.paymentStatus" placeholder="请选择付款状态" style="width: 100%">
                <el-option label="待付款" value="pending" />
                <el-option label="已付款" value="paid" />
                <el-option label="部分付款" value="partial" />
                <el-option label="已逾期" value="overdue" />
                <el-option label="已取消" value="cancelled" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发票状态" prop="invoiceStatus">
              <el-select v-model="formData.invoiceStatus" placeholder="请选择发票状态" style="width: 100%">
                <el-option label="未开票" value="not_issued" />
                <el-option label="已开票" value="issued" />
                <el-option label="已收票" value="received" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发票号码" prop="invoiceNo">
              <el-input v-model="formData.invoiceNo" placeholder="请输入发票号码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发票金额" prop="invoiceAmount">
              <el-input-number
                v-model="formData.invoiceAmount"
                :precision="2"
                :min="0"
                style="width: 100%"
                placeholder="请输入发票金额"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
        
        <!-- 文件管理 -->
        <el-card shadow="never" class="section-card" v-if="formData.id">
          <template #header>
            <div class="card-header">
              <h3>文件管理</h3>
            </div>
          </template>
          <payment-file-manager
            ref="fileManagerRef"
            :payment-id="formData.id"
          />
        </el-card>
      </el-form>

      <template #footer>
        <div class="drawer-footer">
          <el-button @click="closeFormDrawer">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 详情抽屉 -->
    <el-drawer
      v-model="detailDrawer.visible"
      title="付款记录详情"
      direction="rtl"
      size="800px"
      @close="closeDetailDrawer"
    >
      <div class="detail-content">
        <el-card shadow="never" class="mb-4">
          <template #header>
            <h3>基本信息</h3>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="付款单号">{{ detailData.paymentNo }}</el-descriptions-item>
            <el-descriptions-item label="付款类型">{{ detailData.paymentTypeLabel }}</el-descriptions-item>
            <el-descriptions-item label="付款方式">{{ detailData.paymentMethodLabel }}</el-descriptions-item>
            <el-descriptions-item label="币种">{{ detailData.currency || 'CNY' }}</el-descriptions-item>
            <el-descriptions-item label="付款方">{{ detailData.payerPartnerName }}</el-descriptions-item>
            <el-descriptions-item label="收款方">{{ detailData.payeePartnerName }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card shadow="never" class="mb-4">
          <template #header>
            <h3>金额信息</h3>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="计划金额">
              <span v-if="detailData.plannedAmount" class="amount-text">
                ¥{{ detailData.plannedAmount.toLocaleString() }}
              </span>
              <span v-else class="text-gray-400">未设置</span>
            </el-descriptions-item>
            <el-descriptions-item label="实际金额">
              <span v-if="detailData.actualAmount" class="amount-text">
                ¥{{ detailData.actualAmount.toLocaleString() }}
              </span>
              <span v-else class="text-gray-400">未设置</span>
            </el-descriptions-item>
            <el-descriptions-item label="计划日期">{{ detailData.plannedDate || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="实际日期">{{ detailData.actualDate || '未设置' }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card shadow="never" class="mb-4">
          <template #header>
            <h3>状态信息</h3>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="付款状态">
              <el-tag :type="getPaymentStatusType(detailData.paymentStatus)">
                {{ getPaymentStatusLabel(detailData.paymentStatus) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="发票状态">
              <el-tag :type="getInvoiceStatusType(detailData.invoiceStatus)">
                {{ getInvoiceStatusLabel(detailData.invoiceStatus) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card shadow="never" class="mb-4">
          <template #header>
            <h3>发票信息</h3>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="发票号码">{{ detailData.invoiceNo || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="发票金额">
              <span v-if="detailData.invoiceAmount" class="amount-text">
                ¥{{ detailData.invoiceAmount.toLocaleString() }}
              </span>
              <span v-else class="text-gray-400">未设置</span>
            </el-descriptions-item>
            <el-descriptions-item label="开票日期" :span="2">{{ detailData.invoiceDate || '未设置' }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card shadow="never" class="mb-4" v-if="detailData.bankName || detailData.bankAccount || detailData.transactionNo">
          <template #header>
            <h3>银行信息</h3>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="付款银行">{{ detailData.bankName || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="付款账号">{{ detailData.bankAccount || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="交易流水号" :span="2">{{ detailData.transactionNo || '未设置' }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card shadow="never" class="mb-4">
          <template #header>
            <h3>其他信息</h3>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="创建时间">{{ detailData.createTime }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ detailData.updateTime }}</el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">
              <span v-if="detailData.remark">{{ detailData.remark }}</span>
              <span v-else class="text-gray-400">无备注</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 文件管理 -->
        <el-card shadow="never" class="mb-4">
          <template #header>
            <h3>相关文件</h3>
          </template>
          <payment-file-manager :payment-id="detailData.id" />
        </el-card>
      </div>
      <template #footer>
        <div class="drawer-footer">
          <el-button @click="closeDetailDrawer">关闭</el-button>
          <el-button type="primary" @click="editFromDetail">编辑</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  Plus,
  Delete,
  Edit,
  View,
  Back,
} from "@element-plus/icons-vue";
import {
  ContractPaymentPageQuery,
  ContractPaymentPageVO,
  ContractPaymentForm,
} from "@/api/contract/payment.api";
import ContractPaymentAPI from "@/api/contract/payment.api";
import ContractAPI, { ContractVO } from "@/api/contract/contract.api";
import PartnerAPI from "@/api/contract/partner.api";
import { useDictStore } from "@/store/modules/dict.store";
import PaymentFileManager from "@/components/Payment/PaymentFileManager.vue";

defineOptions({
  name: "ContractPayment",
  inheritAttrs: false,
});


const route = useRoute();
const router = useRouter();
const dictStore = useDictStore();

const dataFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);

const contractId = ref<string>(route.params.contractId as string);
const contractInfo = ref<ContractVO>({} as ContractVO);

const queryParams = reactive<ContractPaymentPageQuery>({
  pageNum: 1,
  pageSize: 10,
  contractId: contractId.value,
});

const pageData = ref<ContractPaymentPageVO[]>([]);

const formDrawer = reactive({
  title: "",
  visible: false,
});

const detailDrawer = reactive({
  visible: false,
});

const detailData = ref<any>({});

const formData = reactive<ContractPaymentForm>({
  contractId: contractId.value,
  paymentType: "",
  paymentStatus: "pending",
  invoiceStatus: "not_issued",
  currency: "CNY",
});

const rules = reactive({
  paymentType: [{ required: true, message: "请选择付款类型", trigger: "change" }],
});

const paymentTypeOptions = ref<OptionType[]>([]);
const partnerOptions = ref<OptionType[]>([]);

function getStatusType(status: string): "info" | "warning" | "success" | "danger" {
  const statusMap: Record<string, "info" | "warning" | "success" | "danger"> = {
    draft: "info",
    pending: "warning",
    active: "success",
    completed: "success",
    terminated: "danger",
    cancelled: "danger",
  };
  return statusMap[status] || "info";
}

function getStatusLabel(status: string) {
  const statusMap: Record<string, string> = {
    draft: "草稿",
    pending: "待签署",
    active: "已生效",
    completed: "已完成",
    terminated: "已终止",
    cancelled: "已作废",
  };
  return statusMap[status] || status;
}

function getPaymentStatusType(status: string): "info" | "warning" | "success" | "danger" {
  const statusMap: Record<string, "info" | "warning" | "success" | "danger"> = {
    pending: "warning",
    paid: "success",
    partial: "info",
    overdue: "danger",
    cancelled: "danger",
  };
  return statusMap[status] || "info";
}

function getPaymentStatusLabel(status: string) {
  const statusMap: Record<string, string> = {
    pending: "待付款",
    paid: "已付款",
    partial: "部分付款",
    overdue: "已逾期",
    cancelled: "已取消",
  };
  return statusMap[status] || status;
}

function getInvoiceStatusType(status: string): "info" | "warning" | "success" | "danger" {
  const statusMap: Record<string, "info" | "warning" | "success" | "danger"> = {
    not_issued: "warning",
    issued: "info",
    received: "success",
  };
  return statusMap[status] || "info";
}

function getInvoiceStatusLabel(status: string) {
  const statusMap: Record<string, string> = {
    not_issued: "未开票",
    issued: "已开票",
    received: "已收票",
  };
  return statusMap[status] || status;
}

function goBack() {
  router.push("/contract/list");
}

function handleQuery() {
  loading.value = true;
  ContractPaymentAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

function openFormDrawer(id?: string) {
  formDrawer.visible = true;
  if (id) {
    formDrawer.title = "修改付款记录";
    getFormData(id);
  } else {
    formDrawer.title = "新增付款记录";
  }
}

function closeFormDrawer() {
  formDrawer.visible = false;
  resetForm();
}

function openDetailDrawer(id: string) {
  detailDrawer.visible = true;
  getDetailData(id);
}

function closeDetailDrawer() {
  detailDrawer.visible = false;
  detailData.value = {};
}

function editFromDetail() {
  const id = detailData.value.id;
  closeDetailDrawer();
  openFormDrawer(id);
}

function getDetailData(id: string) {
  ContractPaymentAPI.getDetail(id).then((data) => {
    detailData.value = data;
  }).catch(() => {
    ElMessage.error("获取付款记录详情失败");
    closeDetailDrawer();
  });
}

function resetForm() {
  dataFormRef.value?.resetFields();
  dataFormRef.value?.clearValidate();

  formData.id = undefined;
  formData.contractId = contractId.value;
  formData.paymentNo = "";
  formData.paymentType = "";
  formData.payerPartnerId = undefined;
  formData.payeePartnerId = undefined;
  formData.plannedAmount = undefined;
  formData.actualAmount = undefined;
  formData.plannedDate = "";
  formData.actualDate = "";
  formData.paymentStatus = "pending";
  formData.invoiceStatus = "not_issued";
  formData.invoiceNo = "";
  formData.invoiceAmount = undefined;
  formData.remark = "";
}

function getFormData(id: string) {
  ContractPaymentAPI.getFormData(id).then((data) => {
    Object.assign(formData, data);
  });
}

function handleSubmit() {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      const id = formData.id;
      if (id) {
        ContractPaymentAPI.update(id, formData).then(() => {
          ElMessage.success("修改成功");
          closeFormDrawer();
          handleQuery();
        });
      } else {
        ContractPaymentAPI.create(formData).then(() => {
          ElMessage.success("新增成功");
          closeFormDrawer();
          handleQuery();
        });
      }
    }
  });
}

function handleDelete(id: string) {
  ElMessageBox.confirm("确认删除该付款记录?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ContractPaymentAPI.delete(id).then(() => {
      ElMessage.success("删除成功");
      handleQuery();
    });
  });
}


function loadContractInfo() {
  ContractAPI.getDetail(contractId.value).then((data) => {
    contractInfo.value = data;
  });
}

onMounted(async () => {
  loadContractInfo();
  handleQuery();
  
  // 加载字典数据
  await dictStore.loadDictItems("payment_type");
  paymentTypeOptions.value = dictStore.getDictItems("payment_type");
  
  // 加载伙伴选项
  PartnerAPI.getOptions().then((options) => {
    partnerOptions.value = options;
  });
});
</script>

<style scoped>
.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px;
  border-top: 1px solid #e4e7ed;
}

.detail-content {
  padding: 0 20px;
}

.detail-content h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.amount-text {
  font-weight: 600;
  color: #409eff;
}

.text-gray-400 {
  color: #909399;
}

.mb-4 {
  margin-bottom: 16px;
}

.section-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}
</style>
