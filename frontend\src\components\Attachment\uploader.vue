<template>
  <div>
    <el-dialog v-model="dialogVisible" title="新增附件" width="50%" @close="handleClose">
      <el-upload
        class="upload-demo"
        :file-list="fileList"
        drag
        multiple
        :before-upload="handleBeforeUpload"
        :http-request="handleUpload"
        :on-success="onSuccess"
        :on-error="onError"
        :on-progress="onProgress"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          拖拽文件或
          <em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">文件大小不能超过 20MB</div>
        </template>
      </el-upload>

      <!-- 上传进度提示 -->
      <div v-if="isUploading" class="upload-progress">
        <el-alert title="文件上传中..." type="info" :closable="false" show-icon />
        <el-progress
          :percentage="uploadProgress"
          :format="progressFormat"
          status="success"
          striped
          striped-flow
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import AttachmentAPI from "@/api/attachment/attachment.api";
import { UploadRequestOptions } from "element-plus";
import type { UploadFile, UploadFiles, UploadProgressEvent } from "element-plus";

// 定义上传错误类型
class CustomUploadError extends Error {
  status: number;
  method: string;
  url: string;

  constructor(message: string) {
    super(message);
    this.name = "UploadError";
    this.status = 500;
    this.method = "POST";
    this.url = "";
  }
}

const emit = defineEmits<{
  success: [];
  close: [];
}>();

const props = defineProps<{
  dialogVisible: boolean;
}>();

const dialogVisible = ref(false);
const fileList = ref<File[]>([]);
const isUploading = ref(false);
const uploadProgress = ref(0);
const totalFiles = ref(0);
const uploadedFiles = ref(0);

watch(
  () => props.dialogVisible,
  (newVal) => {
    dialogVisible.value = newVal;
    if (newVal) {
      fileList.value = [];
      resetUploadStatus();
    }
  }
);

const resetUploadStatus = () => {
  isUploading.value = false;
  uploadProgress.value = 0;
  totalFiles.value = 0;
  uploadedFiles.value = 0;
};

const progressFormat = (percentage: number) => {
  return `${uploadedFiles.value}/${totalFiles.value} 文件 (${percentage}%)`;
};

const handleBeforeUpload = (file: File) => {
  console.log(file);
  // 当开始上传第一个文件时，设置上传状态
  if (!isUploading.value) {
    isUploading.value = true;
    totalFiles.value += 1;
  } else {
    totalFiles.value += 1;
  }
  return true;
};

async function handleUpload(options: UploadRequestOptions) {
  try {
    const response = await AttachmentAPI.upload(options.file as File);
    uploadedFiles.value += 1;
    updateProgress();
    options.onSuccess(response);
  } catch (error) {
    const uploadError = new CustomUploadError(error instanceof Error ? error.message : "上传失败");
    options.onError(uploadError);
    uploadedFiles.value += 1;
    updateProgress();
    ElMessage.error("上传失败");
  }
}

const updateProgress = () => {
  if (totalFiles.value > 0) {
    uploadProgress.value = Math.floor((uploadedFiles.value / totalFiles.value) * 100);
  }

  // 当所有文件都上传完成后
  if (uploadedFiles.value === totalFiles.value && totalFiles.value > 0) {
    setTimeout(() => {
      isUploading.value = false;
    }, 500); // 延迟半秒关闭进度条，让用户看到100%
  }
};

const onProgress = (evt: UploadProgressEvent, file: UploadFile) => {
  console.log("上传进度:", evt.percent, file.name);
};

const handleClose = () => {
  dialogVisible.value = false;
  fileList.value = [];
  resetUploadStatus();
  emit("close");
};

const onSuccess = (response: any, file: UploadFile, fileList: UploadFiles) => {
  console.log(response, file, fileList);
  // 当所有文件都上传完成时关闭对话框
  if (fileList.every((file) => file.status === "success")) {
    ElMessage.success("上传成功");
    setTimeout(() => {
      dialogVisible.value = false;
      emit("success");
    }, 800); // 延迟关闭对话框，让用户看到成功状态
  }
};

const onError = (error: Error, file: UploadFile, fileList: UploadFiles) => {
  console.log(error, file, fileList);
  ElMessage.error(`文件 ${file.name} 上传失败`);
};
</script>

<style scoped>
.upload-progress {
  margin-top: 20px;
  padding: 10px;
}

.el-progress {
  margin-top: 15px;
}
</style>
