name: 部署到测试环境(后端)
on:
  workflow_dispatch:
  push:
    branches: [ staging ]  # 根据您的需求调整分支名
    paths:
      - .github/workflows/backend-deploy.yml
      - "backend/**"

jobs:
  deploy:
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
        working-directory: backend

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Java for package
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'oracle'

      - name: Package the application
        run: mvn clean package -DskipTests

      - name: Rename the JAR file
        run: mv target/tsz-java-admin.jar app.jar

      - name: Deploy to server
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.SIT_HOST }}
          username: ${{ secrets.SIT_USERNAME }}
          password: ${{ secrets.SIT_PASSWORD }}
          source: "backend/app.jar"
          target: "/opt/tsz-integrated-platform-staging"

      - name: Restart service
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SIT_HOST }}
          username: ${{ secrets.SIT_USERNAME }}
          password: ${{ secrets.SIT_PASSWORD }}
          script: cd /opt/tsz-integrated-platform-staging && docker compose down backend && docker compose up -d backend