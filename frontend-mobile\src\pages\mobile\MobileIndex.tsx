import { MobileHeader } from "@/components/mobile/MobileHeader";
import { BottomNavigation } from "@/components/mobile/BottomNavigation";
import { StatsCard } from "@/components/mobile/StatsCard";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useNavigate } from "react-router-dom";
import BizOpportunitiesAPI, {
  IOpportunityWithFollow,
} from "@/api/biz-opportunities";
import ContractAPI from "@/api/contract";

import { useUserStore } from "@/store";
import {
  FileText,
  Target,
  Users,
  DollarSign,
  TrendingUp,
  CheckCircle,
} from "lucide-react";
import { useEffect, useState } from "react";
import { OpportunityStatusEnum } from "@/dicts/OpportunityStatusDict";
import {
  OpportunityFollowResult,
  OpportunityFollowResultColorMap,
} from "@/dicts/OpportunityFollowResultDict";
import {
  OpportunityFollowType,
  OpportunityFollowTypeColorMap,
} from "@/dicts/OpportunityFollowTypeDict";

/** 商机详情数据类型 */
export interface IOpportunityWithFollowVO extends IOpportunityWithFollow {
  /** 距离下次跟进天数 */
  daysUntilFollow?: number;
  /** 是否逾期 */
  isOverdue?: boolean;
  /** 上次跟进结果 */
  lastFollowResult?: string;
  /** 上次跟进结果标签 */
  lastFollowResultText?: string;
  /** 跟进方式 */
  followType?: string;
  /** 跟进方式标签 */
  followTypeText?: string;
}

export default function MobileIndex() {
  const navigate = useNavigate();

  // 使用用户状态管理
  const { userInfo, userInfoLoading } = useUserStore();

  // 待跟进商机列表
  const [opportunities, setOpportunities] = useState<
    IOpportunityWithFollowVO[]
  >([]);

  /**
   * 获取待跟进商机列表
   */
  const fetchFollowUpOpportunities = async () => {
    try {
      // const res = await BizOpportunitiesAPI.getOpportunitiesPage({
      const res = await BizOpportunitiesAPI.getOpportunitiesWithFollowPage({
        pageNum: 1,
        pageSize: 10,
        opportunityStatus: OpportunityStatusEnum.ACTIVE,
      });
      setOpportunities(
        res.list.map((item) => {
          // 距离下次跟进日期剩余天数
          let daysUntilFollow = void 0;
          try {
            daysUntilFollow = item.nextFollowDate
              ? Math.ceil(
                  (new Date(item.nextFollowDate).getTime() -
                    new Date().getTime()) /
                    (1000 * 60 * 60 * 24)
                )
              : void 0;
          } catch (error) {
            daysUntilFollow = void 0;
          }

          return {
            ...item,
            lastFollowResult: item.lastFollow?.followResult,
            lastFollowResultText: item.lastFollow?.followResultLabel,
            followTypeText: item.lastFollow?.followTypeLabel,
            followType: item.lastFollow?.followType,
            // 下次跟进日期减去当前日期天数
            daysUntilFollow: daysUntilFollow,
            isOverdue: daysUntilFollow < 0,
          };
        })
      );
    } catch (error) {
      console.error("获取待跟进商机失败:", error);
    }
  };

  /** 统计数据 */
  const [statsData, setStatsData] = useState([
    {
      key: "opportunity",
      title: "商机总数",
      value: "0",
      icon: Target,
      gradient: true,
      onClick: () => navigate("/opportunities"),
    },
    {
      key: "contract",
      title: "合同总数",
      value: "0",
      gradient: false,
      icon: FileText,
      onClick: () => navigate("/contracts"),
    },
  ]);

  // 获取商机统计数据
  const fetchOpportunityStatistic = async () => {
    try {
      const res = await BizOpportunitiesAPI.getOpportunityStatistic();
      setStatsData((prev) => {
        return prev.map((stat) => {
          if (stat.key === "opportunity") {
            return {
              ...stat,
              value: res.totalCount,
            };
          }
          return stat;
        });
      });
    } catch (error) {
      console.error("获取商机统计数据失败:", error);
    }
  };

  // 获取合同总数
  const fetchContractStatistic = async () => {
    try {
      const res = await ContractAPI.getStatistics();
      setStatsData((prev) => {
        return prev.map((stat) => {
          if (stat.key === "contract") {
            return {
              ...stat,
              value: res.count ? res.count.toString() : "0",
            };
          }
          return stat;
        });
      });
    } catch (error) {
      console.error("获取合同统计数据失败:", error);
    }
  };

  // 请求数据
  useEffect(() => {
    fetchOpportunityStatistic();
    fetchContractStatistic();
    fetchFollowUpOpportunities();
  }, []);

  // 获取上次结果文本颜色配置

  return (
    <div className="min-h-screen bg-background pb-20">
      <MobileHeader title="探数者综合管理平台" />

      <div className="p-4 space-y-6">
        {/* 欢迎区域 */}
        {userInfo && (
          <div className="bg-gradient-hero rounded-lg p-6 text-white">
            <div className="flex items-center gap-4 mb-4">
              <Avatar className="h-12 w-12 border-2 border-white/20">
                <AvatarImage src={userInfo.avatar} />
                <AvatarFallback className="bg-white/20 text-white text-base">
                  头像
                </AvatarFallback>
              </Avatar>
              <div>
                <h2 className="text-xl font-bold">
                  早上好，{userInfo.nickname}
                </h2>
                <p className="text-white/80 text-sm">{userInfo.roleNames}</p>
              </div>
            </div>
          </div>
        )}

        {/* 数据统计 */}
        <div>
          <h3 className="text-lg font-semibold mb-4">数据概览</h3>
          <div className="grid grid-cols-2 gap-4">
            {statsData.map((stat, index) => (
              <StatsCard
                key={index}
                title={stat.title}
                value={stat.value}
                icon={stat.icon}
                gradient={stat.gradient}
                onClick={stat.onClick}
              />
            ))}
          </div>
        </div>

        {/* 待跟进商机 */}
        <div>
          <h3 className="text-lg font-semibold mb-4">待跟进商机</h3>
          <div className="space-y-3">
            {opportunities.map((opportunity) => (
              <div
                key={opportunity.id}
                className={`bg-card rounded-lg p-4 shadow-soft cursor-pointer hover:shadow-md transition-shadow ${
                  opportunity.isOverdue ? "border-l-4 border-destructive" : ""
                }`}
                onClick={() => {
                  navigate(`/opportunities/${opportunity.id}`);
                }}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <span
                      className={`text-xs px-2 py-1 rounded-full font-medium ${
                        opportunity.priority === "high"
                          ? "bg-destructive/10 text-destructive"
                          : opportunity.priority === "medium"
                          ? "bg-warning/10 text-warning"
                          : "bg-secondary/10 text-secondary-foreground"
                      }`}
                    >
                      {opportunity.priorityLabel}
                    </span>
                    {/* TODO: 状态样式不全 */}
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${
                        opportunity.opportunityStage === "proposal"
                          ? "bg-warning/10 text-warning"
                          : opportunity.opportunityStage === "negotiation"
                          ? "bg-primary/10 text-primary"
                          : "bg-info/10 text-info"
                      }`}
                    >
                      {opportunity.opportunityStageLabel}
                    </span>
                  </div>
                  <div className="text-right">
                    <div
                      className={`text-xs font-medium ${
                        opportunity.isOverdue
                          ? "text-destructive"
                          : opportunity.daysUntilFollow === 0
                          ? "text-warning"
                          : "text-muted-foreground"
                      }`}
                    >
                      {opportunity.isOverdue
                        ? `逾期${Math.abs(opportunity.daysUntilFollow)}天`
                        : opportunity.daysUntilFollow === 0
                        ? "今日跟进"
                        : `${opportunity.daysUntilFollow}天后`}
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      成单率: {opportunity.winProbability}%
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div>
                    <h4 className="font-semibold text-sm mb-1">
                      {opportunity.opportunityName}
                    </h4>
                    <p className="text-xs text-muted-foreground">
                      编号: {opportunity.opportunityCode}
                    </p>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3 text-muted-foreground" />
                      <span className="text-muted-foreground">
                        {opportunity.partnerName}
                      </span>
                    </div>
                    <span className="font-medium text-primary">
                      {opportunity.estimatedAmount}
                    </span>
                  </div>

                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center gap-4">
                      <span className="text-muted-foreground">
                        联系人: {opportunity.contactPerson}
                      </span>
                      <span
                        className={`px-2 py-1 rounded-full ${
                          OpportunityFollowTypeColorMap[
                            opportunity.followType as OpportunityFollowType
                          ]
                            ? `bg-${
                                OpportunityFollowTypeColorMap[
                                  opportunity.followType as OpportunityFollowType
                                ]
                              }/10 text-${
                                OpportunityFollowTypeColorMap[
                                  opportunity.followType as OpportunityFollowType
                                ]
                              }`
                            : "bg-success/10 text-success"
                        }`}
                      >
                        {opportunity.followTypeText}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs pt-2 border-t border-border">
                    <span className="text-muted-foreground">
                      上次结果:{" "}
                      <span
                        className={`font-medium text-${
                          OpportunityFollowResultColorMap[
                            opportunity.lastFollowResult as OpportunityFollowResult
                          ] || "info"
                        }`}
                      >
                        {opportunity.lastFollowResultText}
                      </span>
                    </span>
                    <span className="text-muted-foreground">
                      {opportunity.nextFollowDate}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <BottomNavigation />
    </div>
  );
}
