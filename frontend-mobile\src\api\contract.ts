import request from '@/utils/request';

const CONTRACT_BASE_URL = "/api/v1/contracts";
const CONTRACT_PAYMENT_BASE_URL="/api/v1/contract-payments/by-contract";

const ContractAPI = {
  /** 获取合同分页列表 */
  getContractPage(params: ContractQuery) {
    return request<any, PageResult<ContractVO>>({
      url: `${CONTRACT_BASE_URL}/page`,
      method: "get",
      params,
    });
  },

  /**
   * 根据状态统计合同
   */
  /**
   * 根据状态统计合同
   * @param status 合同状态，不传则统计全部
   */
  getStatistics(status?: string) {
    return request<any, ContractStatisticsVO>({
      url: `${CONTRACT_BASE_URL}/statistics`,
      method: 'GET',
      params: status ? { status } : {}
    });
  },

  /** 获取合同详情 */
  getContractDetail(id: number) {
    return request<any, ContractVO>({
      url: `${CONTRACT_BASE_URL}/${id}`,
      method: "get",
    });
  },

  /** 获取合同选项列表 */
  getContractOptions() {
    return request<any, ContractVO[]>({
      url: `${CONTRACT_BASE_URL}/options`,
      method: "get",
    });
  },

  /** 获取合同文件列表 */
  getContractAttachments(contractId: number) {
    return request<any, ContractAttachmentVO[]>({
      url: `${CONTRACT_BASE_URL}/${contractId}/attachments`,
      method: "get",
    });
  },

    /** 获取合同完整详情（包含商机名称和附件） */
    getContractFullDetail(id: number) {
      return request<any, ContractVO>({
        url: `${CONTRACT_BASE_URL}/${id}/full-detail`,
        method: "get",
      });
    },

  /** 根据合同ID获取付款记录详情列表 */
  getContractPaymentDetails(contractId: number): Promise<ContractPaymentMobileVO[]> {
    return request.get(`${CONTRACT_PAYMENT_BASE_URL}/${contractId}/details`);
  },
  
  getContractPartnerDetails(contractId: number): Promise<ContractPartnerRelationVO[]> {
    return request({
      url: `${CONTRACT_BASE_URL}/${contractId}/partners/details`,
      method: "get",
    });
  },
};

export default ContractAPI;

/** 分页查询参数 */
export interface ContractQuery {
  /** 页码 */
  pageNum?: number;
  /** 每页大小 */
  pageSize?: number;
  /** 搜索关键字(合同编号、合同名称) */
  keywords?: string;
  /** 合同类型 */
  contractType?: string;
  /** 合同分类 */
  contractCategory?: string;
  /** 合同状态 */
  contractStatus?: string;
  /** 负责人ID */
  responsibleUserId?: number;
  /** 所属部门ID */
  deptId?: number;
  /** 签署日期范围开始 */
  signingDateStart?: string;
  /** 签署日期范围结束 */
  signingDateEnd?: string;
  /** 生效日期范围开始 */
  effectiveDateStart?: string;
  /** 生效日期范围结束 */
  effectiveDateEnd?: string;
  /** 到期日期范围开始 */
  expiryDateStart?: string;
  /** 到期日期范围结束 */
  expiryDateEnd?: string;
  /** 合同金额最小值 */
  contractAmountMin?: number;
  /** 合同金额最大值 */
  contractAmountMax?: number;
  /** 伙伴ID */
  partnerId?: number;
  /** 商机ID */
  opportunityId?: number;
}

/** 分页结果 */
export interface PageResult<T> {
  /** 数据列表 */
  list: T[];
  /** 总记录数 */
  total: number;
  /** 当前页码 */
  pageNum: number;
  /** 每页大小 */
  pageSize: number;
  /** 总页数 */
  pages: number;
}

/**
 * 合同统计视图对象
 */
export interface ContractStatisticsVO {
  count: number;
  totalAmount: number;
}

/** 合同视图对象 */
export interface ContractVO {
  /** 主键ID */
  id: number;
  /** 合同编号 */
  contractNo: string;
  /** 合同名称 */
  contractName: string;
  /** 合同类型 */
  contractType: string;
  /** 合同类型标签 */
  contractTypeLabel: string;
  /** 合同分类 */
  contractCategory: string;
  /** 合同分类标签 */
  contractCategoryLabel: string;
  /** 合同金额 */
  contractAmount: number;
  /** 关联商机ID */
  opportunityId?: number;
  /** 关联商机名称 */
  opportunityName?: string;
  /** 签署日期 */
  signingDate?: string;
  /** 生效日期 */
  effectiveDate?: string;
  /** 到期日期 */
  expiryDate?: string;
  /** 合同状态 */
  contractStatus: string;
  /** 付款方式 */
  paymentMethod?: string;
  /** 付款方式标签 */
  paymentMethodLabel?: string;
  /** 签署地点 */
  signingLocation?: string;
  /** 负责人ID */
  responsibleUserId?: number;
  /** 负责人姓名 */
  responsibleUserName?: string;
  /** 所属部门ID */
  deptId?: number;
  /** 所属部门名称 */
  deptName?: string;
  /** 备注 */
  remark?: string;
  /** 合同伙伴列表 */
  parties?: ContractPartnerRelationVO[];
  /** 合同文件列表 */
  attachments?: ContractAttachmentVO[];
  /** 收付款总金额 */
  totalPaymentAmount?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
}

/** 合同伙伴关联视图对象 */
export interface ContractPartnerRelationVO {
  /** 主键ID */
  id: number;
  /** 合同ID */
  contractId: number;
  /** 伙伴ID */
  partnerId: number;
  /** 伙伴名称 */
  partnerName: string;
  /** 伙伴编码 */
  partnerCode?: string;
  /** 伙伴类型 */
  partnerType?: string;
  /** 伙伴类型标签 */
  partnerTypeLabel?: string;
  /** 伙伴角色 */
  partnerRole?: string;
  /** 伙伴角色标签 */
  partnerRoleLabel?: string;
  /** 角色描述 */
  partnerRoleDesc?: string;
  /** 角色描述标签 */
  partnerRoleDescLabel?: string;
  /** 签署人 */
  signingPerson?: string;
  /** 签署人职务 */
  signingPersonTitle?: string;
  /** 签署日期 */
  signingDate?: string;
  /** 排序 */
  sort?: number;
  /** 备注 */
  remark?: string;
  /** 联系人 */
  contactPerson?: string;
  /** 联系电话 */
  contactPhone?: string;
  /** 联系邮箱 */
  contactEmail?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
}

/** 合同附件视图对象 */
export interface ContractAttachmentVO {
  /** 主键ID */
  id: number;
  /** 合同ID */
  contractId: number;
  /** 附件ID */
  attachmentId: number;
  /** 文件名称 */
  fileName: string;
  /** 文件类型 */
  fileType?: string;
  /** 文件大小 */
  fileSize?: string | number;
  /** 文件路径 */
  filePath?: string;
  /** 排序 */
  sort?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
}

/** 合同付款记录移动端视图对象 */
export interface ContractPaymentMobileVO {
  /** 主键ID */
  id: number;
  /** 付款单号 */
  paymentNo: string;
  /** 付款类型标签 */
  paymentTypeLabel?: string;
  /** 付款状态 */
  paymentStatus: string;
  /** 发票状态 */
  invoiceStatus?: string;
  /** 实际金额 */
  actualAmount?: number;
  /** 付款方式标签 */
  paymentMethodLabel?: string;
  /** 付款方式 */
  paymentMethod?: string;
  /** 计划金额 */
  plannedAmount?: number;
  /** 计划日期 */
  plannedDate?: string;
  /** 实际日期 */
  actualDate?: string;
  /** 发票号码 */
  invoiceNo?: string;
  /** 备注 */
  remark?: string;
}
