package com.hntsz.boot.modules.contract.converter;

import com.hntsz.boot.modules.contract.model.entity.Contract;
import com.hntsz.boot.modules.contract.model.form.ContractForm;
import com.hntsz.boot.modules.contract.model.vo.ContractVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * 合同转换器
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ContractConverter {

    /**
     * 实体转VO
     *
     * @param entity 实体
     * @return VO
     */
    ContractVO entityToVo(Contract entity);

    /**
     * VO转实体
     *
     * @param vo VO
     * @return 实体
     */
    Contract voToEntity(ContractVO vo);

    /**
     * 表单转实体
     *
     * @param form 表单
     * @return 实体
     */
    Contract formToEntity(ContractForm form);

    /**
     * 实体转表单
     *
     * @param entity 实体
     * @return 表单
     */
    ContractForm entityToForm(Contract entity);
}
