package com.hntsz.boot.core.security.extension.oauth;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.CredentialsExpiredException;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 通用 OAuth 用户信息服务实现
 * 
 * <AUTHOR>
 * @since 2.22.0
 */
@Service
@Slf4j
public class DefaultOAuthUserInfoService implements OAuthUserInfoService {
    
    @Value("${security.oauth.client-id:}")
    private String clientId;
    
    @Value("${security.oauth.client-secret:}")
    private String clientSecret;
    
    @Value("${security.oauth.authorization-url:}")
    private String authorizationUrl;
    
    @Value("${security.oauth.token-url:}")
    private String tokenUrl;
    
    @Value("${security.oauth.user-info-url:}")
    private String userInfoUrl;
    
    @Value("${security.oauth.redirect-uri:}")
    private String redirectUri;
    
    @Value("${security.oauth.logout-url:}")
    private String logoutUrl;
    
    @Value("${security.oauth.scopes:openid profile email}")
    private String scopes;
    
    @Value("${security.oauth.user-identifier:id}")
    private String userIdentifier;
    
    @Value("${security.oauth.auth-style:client_secret_post}")
    private String authStyle;

    @Value("${security.oauth.default-dept-id:1}")
    private Long defaultDeptId;

    @Value("${security.oauth.default-role-id:3}")
    private Long defaultRoleId;

    @Override
    public OAuthUserInfo getUserInfo(String provider, String authorizationCode) {
        if (StrUtil.isBlank(clientId) || StrUtil.isBlank(clientSecret)) {
            throw new CredentialsExpiredException("OAuth 客户端配置不完整");
        }
        
        try {
            // 1. 使用授权码获取访问令牌
            OAuthTokenResponse tokenResponse = requestAccessToken(authorizationCode);
            
            if (tokenResponse.hasError()) {
                log.error("OAuth 访问令牌获取失败：{}", tokenResponse.getErrorMessage());
                throw new CredentialsExpiredException("OAuth 访问令牌获取失败: " + tokenResponse.getErrorMessage());
            }
            
            if (StrUtil.isBlank(tokenResponse.getAccessToken())) {
                log.error("OAuth 访问令牌为空");
                throw new CredentialsExpiredException("OAuth 访问令牌获取失败");
            }
            
            // 2. 使用访问令牌获取用户信息
            OAuthUserInfo userInfo = fetchUserInfo(provider, tokenResponse.getAccessToken());
            
            // 3. 设置令牌信息
            userInfo.setAccessToken(tokenResponse.getAccessToken());
            userInfo.setTokenType(tokenResponse.getTokenType());
            userInfo.setExpiresIn(tokenResponse.getExpiresIn());
            userInfo.setRefreshToken(tokenResponse.getRefreshToken());
            userInfo.setScope(tokenResponse.getScope());
            
            return userInfo;
            
        } catch (Exception e) {
            log.error("OAuth 用户信息获取失败", e);
            throw new CredentialsExpiredException("OAuth 用户信息获取失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean supports(String provider) {
        return StrUtil.isNotBlank(provider) && 
               StrUtil.isNotBlank(clientId) && 
               StrUtil.isNotBlank(clientSecret);
    }
    
    
    /**
     * 请求访问令牌
     */
    private OAuthTokenResponse requestAccessToken(String authorizationCode) {
        if (StrUtil.isBlank(tokenUrl)) {
            throw new CredentialsExpiredException("OAuth Token URL 未配置");
        }
        
        Map<String, Object> params = new HashMap<>();
        params.put("code", authorizationCode);
        params.put("grant_type", "authorization_code");
        
        if (StrUtil.isNotBlank(redirectUri)) {
            params.put("redirect_uri", redirectUri);
        }
        
        HttpRequest request = HttpRequest.post(tokenUrl)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .header("Accept", "application/json");
        
        // 根据认证方式设置客户端凭证
        if ("client_secret_basic".equalsIgnoreCase(authStyle)) {
            // HTTP Basic 认证
            request.basicAuth(clientId, clientSecret);
        } else {
            // client_secret_post (默认)
            params.put("client_id", clientId);
            params.put("client_secret", clientSecret);
        }
        
        HttpResponse response = request.form(params).execute();
        
        if (response.getStatus() != 200) {
            log.error("OAuth 访问令牌请求失败，状态码：{}, 响应：{}", response.getStatus(), response.body());
            throw new CredentialsExpiredException("OAuth 访问令牌请求失败");
        }
        
        JSONObject tokenJson = JSONUtil.parseObj(response.body());
        return parseTokenResponse(tokenJson);
    }
    
    /**
     * 解析令牌响应
     */
    private OAuthTokenResponse parseTokenResponse(JSONObject tokenJson) {
        OAuthTokenResponse tokenResponse = new OAuthTokenResponse();
        
        // 访问令牌
        tokenResponse.setAccessToken(tokenJson.getStr("access_token"));
        
        // 令牌类型
        tokenResponse.setTokenType(tokenJson.getStr("token_type", "Bearer"));
        
        // 过期时间
        Integer expiresIn = tokenJson.getInt("expires_in");
        tokenResponse.setExpiresIn(expiresIn);
        
        // 刷新令牌
        tokenResponse.setRefreshToken(tokenJson.getStr("refresh_token"));
        
        // 权限范围
        tokenResponse.setScope(tokenJson.getStr("scope"));
        
        // 状态参数
        tokenResponse.setState(tokenJson.getStr("state"));
        
        // 错误信息
        tokenResponse.setError(tokenJson.getStr("error"));
        tokenResponse.setErrorDescription(tokenJson.getStr("error_description"));
        tokenResponse.setErrorUri(tokenJson.getStr("error_uri"));
        
        return tokenResponse;
    }
    
    /**
     * 获取用户信息
     */
    private OAuthUserInfo fetchUserInfo(String provider, String accessToken) {
        if (StrUtil.isBlank(userInfoUrl)) {
            throw new CredentialsExpiredException("OAuth 用户信息 URL 未配置");
        }
        
        HttpResponse response = HttpRequest.get(userInfoUrl)
                .header("Authorization", "Bearer " + accessToken)
                .header("Accept", "application/json")
                .execute();
        
        if (response.getStatus() != 200) {
            log.error("OAuth 用户信息获取失败，状态码：{}, 响应：{}", response.getStatus(), response.body());
            throw new CredentialsExpiredException("OAuth 用户信息获取失败");
        }
        
        JSONObject userJson = JSONUtil.parseObj(response.body());
        
        OAuthUserInfo userInfo = new OAuthUserInfo();
        userInfo.setProvider(provider);
        
        // 尝试从多个可能的字段中获取用户信息
        userInfo.setOauthId(getUserId(userJson));
        userInfo.setUsername(getUsername(userJson));
        userInfo.setNickname(getNickname(userJson));
        userInfo.setEmail(getEmail(userJson));
        userInfo.setMobile(getMobile(userJson));
        userInfo.setAvatar(getAvatar(userJson));
        userInfo.setGender(0); // 默认设为保密
        userInfo.setDefaultDeptId(defaultDeptId);
        userInfo.setDefaultRoleId(defaultRoleId);
        
        return userInfo;
    }
    
    /**
     * 获取用户ID（优先使用配置的用户标识符）
     */
    private String getUserId(JSONObject userJson) {
        // 优先使用配置的用户标识符
        if (StrUtil.isNotBlank(userIdentifier)) {
            String value = userJson.getStr(userIdentifier);
            if (StrUtil.isNotBlank(value)) {
                return value;
            }
        }
        
        // 如果配置的标识符不存在，尝试常见字段
        String[] idFields = {"id", "sub", "user_id", "uid"};
        for (String field : idFields) {
            String value = userJson.getStr(field);
            if (StrUtil.isNotBlank(value)) {
                return value;
            }
        }
        throw new CredentialsExpiredException("OAuth 响应中无法找到有效的用户ID字段");
    }
    
    /**
     * 获取用户名（尝试多个字段）
     */
    private String getUsername(JSONObject userJson) {
        String[] usernameFields = {"login", "username", "preferred_username", "email"};
        for (String field : usernameFields) {
            String value = userJson.getStr(field);
            if (StrUtil.isNotBlank(value)) {
                return value;
            }
        }
        return null;
    }
    
    /**
     * 获取昵称（尝试多个字段）
     */
    private String getNickname(JSONObject userJson) {
        String[] nicknameFields = {"name", "display_name", "nickname", "full_name"};
        for (String field : nicknameFields) {
            String value = userJson.getStr(field);
            if (StrUtil.isNotBlank(value)) {
                return value;
            }
        }
        return null;
    }
    
    /**
     * 获取邮箱（尝试多个字段）
     */
    private String getEmail(JSONObject userJson) {
        String[] emailFields = {"email", "mail", "email_address"};
        for (String field : emailFields) {
            String value = userJson.getStr(field);
            if (StrUtil.isNotBlank(value)) {
                return value;
            }
        }
        return null;
    }
    
    /**
     * 获取头像（尝试多个字段）
     */
    private String getAvatar(JSONObject userJson) {
        String[] avatarFields = {"avatar_url", "picture", "avatar", "photo"};
        for (String field : avatarFields) {
            String value = userJson.getStr(field);
            if (StrUtil.isNotBlank(value)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 获取手机号
     */
    private String getMobile(JSONObject userJson) {
        String[] mobileFields = {"phone_number", "mobile", "phone", "tel"};
        for (String field : mobileFields) {
            String value = userJson.getStr(field);
            if (StrUtil.isNotBlank(value)) {
                return value;
            }
        }
        return null;
    }
}