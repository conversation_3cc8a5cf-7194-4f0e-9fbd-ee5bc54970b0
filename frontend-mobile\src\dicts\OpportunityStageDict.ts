// 商机阶段(initial-初步接触 interested-有意向 proposal-方案阶段 negotiation-谈判阶段 closed_won-成交 closed_lost-失败)
/** 商机阶段 */
export const enum OpportunityStage {
  /** 初步接触 */
  INITIAL = "initial",
  /** 有意向 */
  INTERESTED = "interested",
  /** 方案阶段 */
  PROPOSAL = "proposal",
  /** 谈判阶段 */
  NEGOTIATION = "negotiation",
  /** 成交 */
  CLOSED_WON = "closed_won",
  /** 失败 */
  CLOSED_LOST = "closed_lost",
}

/** 跟进结果对应颜色配置 */
export const OpportunityStageColorMap = {
  [OpportunityStage.INITIAL]: "info",
  [OpportunityStage.INTERESTED]: "info",
  [OpportunityStage.PROPOSAL]: "warning",
  [OpportunityStage.NEGOTIATION]: "primary",
  [OpportunityStage.CLOSED_WON]: "success",
  [OpportunityStage.CLOSED_LOST]: "destructive",
};
