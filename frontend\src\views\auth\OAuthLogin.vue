<template>
  <div class="oauth-loading-container">
    <div class="oauth-loading-card">
      <div class="oauth-loading-content">
        <!-- 加载动画 -->
        <div class="oauth-loading-spinner">
          <el-icon class="rotating" size="48">
            <svg viewBox="0 0 1024 1024" width="48" height="48">
              <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z" fill="currentColor"/>
              <path d="M512 140c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm0 684c-172.2 0-312-139.8-312-312s139.8-312 312-312 312 139.8 312 312-139.8 312-312 312z" fill="currentColor"/>
              <path d="M464 336a48 48 0 1 0 96 0 48 48 0 1 0-96 0zm72 112c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V448z" fill="currentColor"/>
            </svg>
          </el-icon>
        </div>
        
        <!-- 加载文本 -->
        <h2 class="oauth-loading-title">{{ t("login.oauthLogin") }}</h2>
        <p class="oauth-loading-message">{{ statusMessage }}</p>
        
        <!-- 错误时显示返回按钮 -->
        <div v-if="hasError" class="oauth-loading-actions">
          <el-button 
            type="primary" 
            @click="goToLogin"
            class="back-btn"
          >
            {{ t("login.backToLogin") }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import AuthAPI from '@/api/auth.api'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()

const oauthLoading = ref(false)
const statusMessage = ref('')
const statusType = ref<'success' | 'warning' | 'danger' | 'info'>('info')
const hasError = ref(false)

// OAuth 登录处理
async function handleOAuthLogin() {

  if (oauthLoading.value) return
  
  oauthLoading.value = true
  statusMessage.value = t('login.oauthConfigLoading')
  statusType.value = 'info'
  
  try {
    // 从后端获取 OAuth 配置
    const oauthConfig = await AuthAPI.getOAuthConfig()

    // 检查是否启用 OAuth
    if (!oauthConfig.enabled) {
      statusMessage.value = t('login.oauthDisabled')
      statusType.value = 'warning'
      hasError.value = true
      ElMessage.warning(t('login.oauthDisabled'))
      return
    }

    // 检查配置是否完整
    if (!oauthConfig.clientId || !oauthConfig.authorizationUrl) {
      statusMessage.value = t('login.oauthConfigIncomplete')
      statusType.value = 'danger'
      hasError.value = true
      ElMessage.error(t('login.oauthConfigIncomplete'))
      return
    }

    // 生成 state 参数用于安全验证
    const state = Math.random().toString(36).substring(2, 15)
    sessionStorage.setItem('oauth_state', state)

    // 保存重定向地址
    const redirect = route.query.redirect as string
    if (redirect) {
      sessionStorage.setItem('oauth_redirect', redirect)
    }

    // 构建 OAuth 授权 URL
    const authUrl = new URL(oauthConfig.authorizationUrl)
    authUrl.searchParams.append('client_id', oauthConfig.clientId)
    authUrl.searchParams.append('redirect_uri', oauthConfig.redirectUri || `${window.location.origin}/#/auth/callback`)
    authUrl.searchParams.append('response_type', 'code')
    authUrl.searchParams.append('scope', oauthConfig.scopes || 'openid profile email')
    authUrl.searchParams.append('state', state)

    statusMessage.value = t('login.oauthRedirecting')
    statusType.value = 'success'
    ElMessage.success(t('login.oauthRedirecting'))
    
    // 跳转到 OAuth 授权页面
    window.location.href = authUrl.toString()
  } catch (error) {
    console.error('获取 OAuth 配置失败:', error)
    statusMessage.value = t('login.oauthConfigError')
    statusType.value = 'danger'
    hasError.value = true
    ElMessage.error(t('login.oauthConfigError'))
  } finally {
    // 延迟重置加载状态，因为页面可能即将跳转
    setTimeout(() => {
      oauthLoading.value = false
    }, 3000)
  }
}

// 返回登录页面
function goToLogin() {
  const redirect = route.query.redirect as string
  if (redirect) {
    router.push(`/login?redirect=${encodeURIComponent(redirect)}`)
  } else {
    router.push('/login')
  }
}

// 页面加载时自动尝试OAuth登录
onMounted(() => {
  statusMessage.value = t('login.oauthInitializing')
  handleOAuthLogin()
})
</script>

<style scoped>
.oauth-loading-container {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* 添加伪元素作为背景层 */
.oauth-loading-container::before {
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  content: "";
  background: url("@/assets/images/login-bg.svg");
  background-position: center center;
  background-size: cover;
}

.oauth-loading-card {
  background: white;
  border-radius: 10px;
  box-shadow: var(--el-box-shadow-light);
  backdrop-filter: blur(3px);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.oauth-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.oauth-loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
}

.rotating {
  animation: spin 1s linear infinite;
  color: #409eff;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.oauth-loading-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.oauth-loading-message {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.oauth-loading-actions {
  margin-top: 20px;
}

.back-btn {
  min-width: 120px;
}

/* 暗色模式支持 */
.dark .oauth-loading-card {
  background: #1f2937;
  border: 1px solid #374151;
}

.dark .oauth-loading-title {
  color: #f9fafb;
}

.dark .oauth-loading-message {
  color: #9ca3af;
}

.dark .rotating {
  color: #409eff;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .oauth-loading-card {
    padding: 30px 20px;
  }
  
  .oauth-loading-title {
    font-size: 20px;
  }
  
  .oauth-loading-message {
    font-size: 13px;
  }
}
</style>