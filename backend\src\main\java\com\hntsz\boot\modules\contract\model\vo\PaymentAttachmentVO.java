package com.hntsz.boot.modules.contract.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 付款文件关联VO
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Getter
@Setter
@Schema(description = "付款文件关联信息")
public class PaymentAttachmentVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "付款ID")
    private Long paymentId;

    @Schema(description = "附件ID")
    private Long attachmentId;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "文件路径")
    private String filePath;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "文件大小")
    private Long fileSize;
}