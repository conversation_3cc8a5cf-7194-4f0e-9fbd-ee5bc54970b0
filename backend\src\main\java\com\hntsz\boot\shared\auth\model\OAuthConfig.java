package com.hntsz.boot.shared.auth.model;

import lombok.Data;

/**
 * OAuth 配置信息
 *
 * <AUTHOR>
 * @since 2.22.0
 */
@Data
public class OAuthConfig {
    
    /**
     * OAuth 客户端 ID
     */
    private String clientId;
    
    /**
     * OAuth 授权端点 URL
     */
    private String authorizationUrl;
    
    /**
     * OAuth 回调地址
     */
    private String redirectUri;
    
    /**
     * OAuth 权限范围
     */
    private String scopes;
    
    /**
     * OAuth 登出 URL
     */
    private String logoutUrl;
    
    /**
     * 是否启用 OAuth 登录
     */
    private boolean enabled;
}