<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="关键字" prop="keywords">
          <el-input
            v-model="queryParams.keywords"
            placeholder="伙伴名称/编码/联系人"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="伙伴类型" prop="partnerType">
          <el-select v-model="queryParams.partnerType" placeholder="请选择" clearable style="width: 150px">
            <el-option
              v-for="item in partnerTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择" clearable style="width: 120px">
            <el-option label="正常" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <template #header>
        <div class="flex justify-between">
          <div class="flex items-center space-x-2">
            <el-icon><User /></el-icon>
            <span>业务伙伴列表</span>
          </div>
          <div>
            <el-button
              type="success"
              @click="openDialog()"
              v-hasPerm="['partner:add']"
            >
              <el-icon><Plus /></el-icon>
              新增
            </el-button>
            <el-button
              type="danger"
              :disabled="ids.length === 0"
              @click="handleDelete()"
              v-hasPerm="['partner:delete']"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="伙伴名称" prop="partnerName" min-width="150" />
        <el-table-column label="伙伴编码" prop="partnerCode" width="120" />
        <el-table-column label="伙伴类型" prop="partnerTypeLabel" width="100" />
        <el-table-column label="联系人" prop="contactPerson" width="100" />
        <el-table-column label="联系电话" prop="contactPhone" width="120" />
        <el-table-column label="联系邮箱" prop="contactEmail" width="150" />
        <el-table-column label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
              {{ scope.row.status === 'active' ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="180" />
        <el-table-column label="操作" width="220" align="center" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              link
              @click="openDialog(scope.row.id)"
              v-hasPerm="['partner:edit']"
            >
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button
              type="success"
              size="small"
              link
              @click="handleDetail(scope.row.id)"
              v-hasPerm="['partner:query']"
            >
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button
              type="danger"
              size="small"
              link
              @click="handleDelete(scope.row.id)"
              v-hasPerm="['partner:delete']"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <!-- 详情抽屉 -->
    <PartnerDetailDrawer
      v-model="detailDrawer.visible"
      :partner-id="detailDrawer.partnerId"
    />

    <!-- 表单弹窗 -->
    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      width="800px"
      @close="closeDialog"
    >
      <el-form
        ref="dataFormRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="伙伴名称" prop="partnerName">
              <el-input v-model="formData.partnerName" placeholder="请输入伙伴名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="伙伴编码" prop="partnerCode">
              <el-input v-model="formData.partnerCode" placeholder="请输入伙伴编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="伙伴类型" prop="partnerType">
              <el-select v-model="formData.partnerType" placeholder="请选择伙伴类型" style="width: 100%">
                <el-option
                  v-for="item in partnerTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否我司">
              <el-switch v-model="formData.isOurCompany" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="法定代表人">
              <el-input v-model="formData.legalRepresentative" placeholder="请输入法定代表人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系人">
              <el-input v-model="formData.contactPerson" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话">
              <el-input v-model="formData.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系邮箱">
              <el-input v-model="formData.contactEmail" placeholder="请输入联系邮箱" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="地址">
          <el-input v-model="formData.address" placeholder="请输入地址" />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="证件类型">
              <el-select v-model="formData.certificateType" placeholder="请选择证件类型" style="width: 100%">
                <el-option
                  v-for="item in certificateTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证件号码">
              <el-input v-model="formData.certificateNumber" placeholder="请输入证件号码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="税号">
              <el-input v-model="formData.taxNumber" placeholder="请输入税号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户银行">
              <el-input v-model="formData.bankName" placeholder="请输入开户银行" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="银行账号">
              <el-input v-model="formData.bankAccount" placeholder="请输入银行账号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="formData.status">
                <el-radio value="active">正常</el-radio>
                <el-radio value="inactive">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import {
  PartnerPageQuery,
  PartnerPageVO,
  PartnerForm,
} from "@/api/contract/partner.api";
import PartnerAPI from "@/api/contract/partner.api";
import { useDictStore } from "@/store/modules/dict.store";
import PartnerDetailDrawer from "./components/PartnerDetailDrawer.vue";

defineOptions({
  name: "Partner",
  inheritAttrs: false,
});

const dictStore = useDictStore();

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);

const loading = ref(false);
const ids = ref<string[]>([]);
const total = ref(0);

const queryParams = reactive<PartnerPageQuery>({
  pageNum: 1,
  pageSize: 10,
});

const pageData = ref<PartnerPageVO[]>([]);

const dialog = reactive({
  title: "",
  visible: false,
});

const detailDrawer = reactive({
  visible: false,
  partnerId: "",
});

const formData = reactive<PartnerForm>({
  partnerName: "",
  partnerType: "",
  isOurCompany: false,
  status: "active",
});

const rules = reactive({
  partnerName: [{ required: true, message: "请输入伙伴名称", trigger: "blur" }],
  partnerType: [{ required: true, message: "请选择伙伴类型", trigger: "change" }],
});

const partnerTypeOptions = ref<OptionType[]>([]);
const certificateTypeOptions = ref<OptionType[]>([]);

/** 查询 */
function handleQuery() {
  loading.value = true;
  PartnerAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置查询 */
function resetQuery() {
  queryFormRef.value.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}

/** 行选择 */
function handleSelectionChange(selection: PartnerPageVO[]) {
  ids.value = selection.map((item) => item.id);
}

/** 打开弹窗 */
function openDialog(id?: string) {
  dialog.visible = true;
  if (id) {
    dialog.title = "修改业务伙伴";
    getPartnerFormData(id);
  } else {
    dialog.title = "新增业务伙伴";
  }
}

/** 关闭弹窗 */
function closeDialog() {
  dialog.visible = false;
  resetForm();
}

/** 重置表单 */
function resetForm() {
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();

  formData.id = undefined;
  formData.partnerName = "";
  formData.partnerCode = "";
  formData.partnerType = "";
  formData.isOurCompany = false;
  formData.legalRepresentative = "";
  formData.contactPerson = "";
  formData.contactPhone = "";
  formData.contactEmail = "";
  formData.address = "";
  formData.certificateType = "";
  formData.certificateNumber = "";
  formData.taxNumber = "";
  formData.bankName = "";
  formData.bankAccount = "";
  formData.status = "active";
  formData.remark = "";
}

/** 获取业务伙伴表单数据 */
function getPartnerFormData(id: string) {
  PartnerAPI.getFormData(id).then((data) => {
    Object.assign(formData, data);
  });
}

/** 提交表单 */
function handleSubmit() {
  dataFormRef.value.validate((valid: any) => {
    if (valid) {
      const id = formData.id;
      if (id) {
        PartnerAPI.update(id, formData).then(() => {
          ElMessage.success("修改成功");
          closeDialog();
          handleQuery();
        });
      } else {
        PartnerAPI.create(formData).then(() => {
          ElMessage.success("新增成功");
          closeDialog();
          handleQuery();
        });
      }
    }
  });
}

/** 删除业务伙伴 */
function handleDelete(id?: string) {
  const partnerIds = id ? [id] : ids.value;
  if (!partnerIds || partnerIds.length === 0) {
    ElMessage.warning("请选择要删除的数据");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    PartnerAPI.delete(partnerIds.join(",")).then(() => {
      ElMessage.success("删除成功");
      handleQuery();
    });
  });
}

/** 查看详情 */
function handleDetail(id: string) {
  detailDrawer.partnerId = id;
  detailDrawer.visible = true;
}

/** 初始化字典数据 */
async function initDictData() {
  // 获取伙伴类型字典
  await dictStore.loadDictItems("partner_type");
  partnerTypeOptions.value = dictStore.getDictItems("partner_type");

  // 获取证件类型字典
  await dictStore.loadDictItems("certificate_type");
  certificateTypeOptions.value = dictStore.getDictItems("certificate_type");
}

onMounted(async () => {
  handleQuery();
  await initDictData();
});
</script>
