<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1400 800">
  <defs>
    <style>
      @media (prefers-color-scheme: dark) {
        #bg-rect { fill: #101a29; }
        #blueGlow-rect { fill: url(#blueGlowDark); }
        #blueGlow2-rect { fill: url(#blueGlow2Dark); }
        #pinkPurpleGlow-rect { fill: url(#pinkPurpleGlowDark); }
      }
    </style>

    <!-- 亮色主题渐变 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#f9fcff" />
      <stop offset="100%" stop-color="#f5f9fd" />
    </linearGradient>
    
    <!-- 中间区域淡蓝白光晕 -->
    <radialGradient id="blueGlow" cx="50%" cy="50%" r="70%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#ffffff" stop-opacity="0.9" />
      <stop offset="50%" stop-color="#f0f8ff" stop-opacity="0.5" />
      <stop offset="100%" stop-color="#eef7fd" stop-opacity="0" />
    </radialGradient>
    
    <!-- 左上角蓝白光晕  -->
    <radialGradient id="blueGlow2" cx="15%" cy="15%" r="40%" fx="15%" fy="15%">
      <stop offset="0%" stop-color="#d9efff" stop-opacity="0.85" />
      <stop offset="40%" stop-color="#e5f4fd" stop-opacity="0.6" />
      <stop offset="100%" stop-color="#e9f5fd" stop-opacity="0" />
    </radialGradient>
    
    <!-- 右下角粉紫色光晕  -->
    <radialGradient id="pinkPurpleGlow" cx="85%" cy="85%" r="40%" fx="85%" fy="85%">
      <stop offset="0%" stop-color="#f7e6f9" stop-opacity="0.8" />
      <stop offset="35%" stop-color="#f9edf8" stop-opacity="0.6" />
      <stop offset="100%" stop-color="#f8f2f8" stop-opacity="0" />
    </radialGradient>

    <!-- 暗色主题渐变 -->
    <radialGradient id="blueGlowDark" cx="50%" cy="50%" r="70%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#1e3a5e" stop-opacity="0.6" />
      <stop offset="50%" stop-color="#1c314e" stop-opacity="0.3" />
      <stop offset="100%" stop-color="#1a2d47" stop-opacity="0" />
    </radialGradient>
    
    <!-- 左上角蓝白光晕 - 暗色模式 -->
    <radialGradient id="blueGlow2Dark" cx="15%" cy="15%" r="40%" fx="15%" fy="15%">
      <stop offset="0%" stop-color="#1e3858" stop-opacity="0.85" />
      <stop offset="40%" stop-color="#1a304f" stop-opacity="0.6" />
      <stop offset="100%" stop-color="#172b45" stop-opacity="0" />
    </radialGradient>
    
    <!-- 右下角粉紫色光晕 - 暗色模式 -->
    <radialGradient id="pinkPurpleGlowDark" cx="85%" cy="85%" r="40%" fx="85%" fy="85%">
      <stop offset="0%" stop-color="#2e2335" stop-opacity="0.85" />
      <stop offset="35%" stop-color="#2a2035" stop-opacity="0.6" />
      <stop offset="100%" stop-color="#2a202d" stop-opacity="0" />
    </radialGradient>
  </defs>
  
  <!-- 背景层 -->
  <rect id="bg-rect" width="100%" height="100%" fill="url(#bgGradient)" />
  
  <!-- 中间淡蓝白光晕 -->
  <rect id="blueGlow-rect" width="100%" height="100%" fill="url(#blueGlow)" />
  
  <!-- 左上蓝白光晕 -->
  <rect id="blueGlow2-rect" width="100%" height="100%" fill="url(#blueGlow2)" />
  
  <!-- 右下粉紫光晕 -->
  <rect id="pinkPurpleGlow-rect" width="100%" height="100%" fill="url(#pinkPurpleGlow)" />
</svg>
