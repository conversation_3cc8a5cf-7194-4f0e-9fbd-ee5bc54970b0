export default {
  // 菜单国际化
  route: {
    dashboard: "首页",
    document: "项目文档",
  },
  // 登录页面国际化
  login: {
    themeToggle: "主题切换",
    languageToggle: "语言切换",
    dark: "暗黑",
    light: "明亮",
    username: "用户名",
    password: "密码",
    login: "登 录",
    captchaCode: "验证码",
    capsLock: "大写锁定已打开",
    rememberMe: "记住我",
    forgetPassword: "忘记密码？",
    message: {
      username: {
        required: "请输入用户名",
      },
      password: {
        required: "请输入密码",
        min: "密码不能少于6位",
        confirm: "请再次确认密码",
        inconformity: "两次密码输入不一致",
      },
      captchaCode: {
        required: "请输入验证码",
      },
    },
    otherLoginMethods: "其他登录方式",
    oauthLogin: "OAuth 登录",
    oauthDescription: "使用第三方账号快速登录",
    backToLogin: "返回登录",
    oauthInitializing: "正在初始化 OAuth 登录...",
    oauthConfigLoading: "正在加载配置...",
    oauthDisabled: "OAuth 登录未启用",
    oauthConfigIncomplete: "OAuth 配置不完整",
    oauthConfigError: "获取 OAuth 配置失败",
    oauthRedirecting: "正在跳转到授权页面...",
    resetPassword: "重置密码",
    thinkOfPasswd: "想起密码？",
    register: "注册账号",
    agree: "我已同意并阅读",
    userAgreement: "用户协议",
    haveAccount: "已有账号？",
    noAccount: "您没有账号？",
    quickFill: "快速填写",
    reg: "注 册",
  },
  // 退出登录国际化
  logout: {
    success: "退出成功",
    successMessage: "您已成功退出系统，感谢您的使用！",
    backToLogin: "返回登录",
  },
  // 导航栏国际化
  navbar: {
    dashboard: "首页",
    logout: "退出登录",
    document: "项目文档",
    gitee: "项目地址",
    profile: "个人中心",
  },
  sizeSelect: {
    tooltip: "布局大小",
    default: "默认",
    large: "大型",
    small: "小型",
    message: {
      success: "切换布局大小成功！",
    },
  },
  langSelect: {
    message: {
      success: "切换语言成功！",
    },
  },
  settings: {
    project: "项目配置",
    theme: "主题设置",
    interface: "界面设置",
    navigation: "导航设置",
    themeColor: "主题颜色",
    tagsView: "开启 Tags-View",
    sidebarLogo: "侧边栏 Logo",
    sidebarColorScheme: "侧边栏配色",
    watermark: "开启水印",
    classicBlue: "经典蓝",
    minimalWhite: "极简白",
  },
};
