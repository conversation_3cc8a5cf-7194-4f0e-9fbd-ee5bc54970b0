package com.hntsz.boot.core.security.extension.oauth;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hntsz.boot.core.security.model.SysUserDetails;
import com.hntsz.boot.core.security.model.UserAuthCredentials;
import com.hntsz.boot.system.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.CredentialsExpiredException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

/**
 * OAuth 认证提供者
 * 
 * <AUTHOR>
 * @since 2.22.0
 */
@Slf4j
@RequiredArgsConstructor
public class OAuthAuthenticationProvider implements AuthenticationProvider {
    
    private final UserService userService;
    private final OAuthUserInfoService oAuthUserInfoService;
    
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        OAuthAuthenticationToken authToken = (OAuthAuthenticationToken) authentication;
        
        String provider = authToken.getProvider();
        String authorizationCode = authToken.getAuthorizationCode();
        
        if (StrUtil.isBlank(provider) || StrUtil.isBlank(authorizationCode)) {
            throw new CredentialsExpiredException("OAuth 提供商或授权码不能为空");
        }
        
        // 检查是否支持该提供商
        if (!oAuthUserInfoService.supports(provider)) {
            throw new CredentialsExpiredException("不支持的 OAuth 提供商: " + provider);
        }
        
        // 通过授权码获取用户信息
        OAuthUserInfo oAuthUserInfo;
        try {
            oAuthUserInfo = oAuthUserInfoService.getUserInfo(provider, authorizationCode);
        } catch (Exception e) {
            log.error("OAuth 用户信息获取失败", e);
            throw new CredentialsExpiredException("OAuth 用户信息获取失败: " + e.getMessage());
        }
        
        if (oAuthUserInfo == null || StrUtil.isBlank(oAuthUserInfo.getOauthId())) {
            throw new UsernameNotFoundException("未能获取到 OAuth 用户信息");
        }

        if(StrUtil.isBlank(oAuthUserInfo.getMobile())){
            throw new CredentialsExpiredException("OAuth 用户信息获取失败: 未能获取到手机号");
        }
        
        // 根据 OAuth 提供商和用户ID查询用户信息
        UserAuthCredentials userAuthCredentials = userService.getAuthCredentialsByOAuth(provider, oAuthUserInfo.getOauthId());
        
        if (userAuthCredentials == null) {
            // 用户不存在则自动注册
            try {
                userService.registerOrBindOAuthUser(oAuthUserInfo);
                
                // 再次查询用户信息
                userAuthCredentials = userService.getAuthCredentialsByOAuth(provider, oAuthUserInfo.getOauthId());
                if (userAuthCredentials == null) {
                    throw new UsernameNotFoundException("OAuth 用户注册失败");
                }
            } catch (Exception e) {
                log.error("OAuth 用户注册失败", e);
                throw new UsernameNotFoundException("OAuth 用户注册失败: " + e.getMessage());
            }
        }
        
        // 检查用户状态
        if (ObjectUtil.notEqual(userAuthCredentials.getStatus(), 1)) {
            throw new DisabledException("用户已被禁用");
        }
        
        // 构建认证后的用户详情信息
        SysUserDetails userDetails = new SysUserDetails(userAuthCredentials);
        
        // 创建已认证的 OAuthAuthenticationToken
        return OAuthAuthenticationToken.authenticated(
                userDetails,
                userDetails.getAuthorities(),
                provider
        );
    }
    
    @Override
    public boolean supports(Class<?> authentication) {
        return OAuthAuthenticationToken.class.isAssignableFrom(authentication);
    }
}