<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="商机名称" prop="opportunityName">
          <el-input
            v-model="queryParams.opportunityName"
            placeholder="请输入商机名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="商机类型" prop="opportunityType">
          <el-select
            v-model="queryParams.opportunityType"
            placeholder="请选择商机类型"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="item in opportunityTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="商机阶段" prop="opportunityStage">
          <el-select
            v-model="queryParams.opportunityStage"
            placeholder="请选择商机阶段"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="item in opportunityStageOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="商机状态" prop="opportunityStatus">
          <el-select
            v-model="queryParams.opportunityStatus"
            placeholder="请选择商机状态"
            clearable
            style="width: 150px"
          >
            <el-option label="进行中" value="active" />
            <el-option label="已成交" value="won" />
            <el-option label="已失败" value="lost" />
            <el-option label="已取消" value="cancelled" />
            <el-option label="已归档" value="archived" />
          </el-select>
        </el-form-item>
        <el-form-item label="负责人" prop="responsibleUserId">
          <el-select
            v-model="queryParams.responsibleUserId"
            placeholder="请选择负责人"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="item in userOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <template #header>
        <div class="flex justify-between">
          <div class="flex items-center space-x-2">
            <el-button
              type="success"
              @click="openDrawer()"
              v-hasPerm="['opportunity:list:add']"
            >
              <el-icon><Plus /></el-icon>
              新增
            </el-button>
            <el-button
              type="warning"
              :disabled="ids.length === 0"
              @click="handleTransfer()"
              v-hasPerm="['opportunity:list:transfer']"
            >
              <el-icon><Sort /></el-icon>
              转移
            </el-button>
            <el-button
              type="warning"
              :disabled="ids.length === 0"
              @click="handleArchive()"
              v-hasPerm="['opportunity:list:archive']"
            >
              <el-icon><FolderOpened /></el-icon>
              归档
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="商机编码" prop="opportunityCode" width="120" />
        <el-table-column label="商机名称" prop="opportunityName" min-width="200" show-overflow-tooltip />
        <el-table-column label="客户名称" prop="partnerName" width="150" show-overflow-tooltip />
        <el-table-column label="商机类型" prop="opportunityTypeLabel" width="100" />
        <el-table-column label="商机阶段" width="100">
          <template #default="scope">
            <el-tag
              :type="getStageTagType(scope.row.opportunityStage)"
              effect="light"
            >
              {{ scope.row.opportunityStageLabel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="成单概率" prop="winProbability" width="100">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.winProbability || 0"
              :stroke-width="15"
              text-inside
            />
          </template>
        </el-table-column>
        <el-table-column label="预估金额" prop="estimatedAmount" width="120">
          <template #default="scope">
            <span v-if="scope.row.estimatedAmount">
              ¥{{ Number(scope.row.estimatedAmount).toLocaleString() }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="商机状态" width="100">
          <template #default="scope">
            <el-tag
              :type="getStatusTagType(scope.row.opportunityStatus)"
              effect="light"
            >
              {{ scope.row.opportunityStatusLabel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="优先级" prop="priorityLabel" width="80" />
        <el-table-column label="负责人" prop="responsibleUserName" width="100" />
        <el-table-column label="预计成交" prop="estimatedCloseDate" width="120" />
        <el-table-column label="跟进数" width="80" align="center">
          <template #default="scope">
            <div class="follow-count">
              <el-button
                link
                type="primary"
                size="small"
                @click="handleFollow(scope.row.id)"
              >
                {{ scope.row.followCount || 0 }}
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="180" />
        <el-table-column label="操作" width="320" align="center" fixed="right">
          <template #default="scope">
            <div class="flex items-center justify-center space-x-1">
              <el-button
                type="primary"
                size="small"
                link
                @click="openDrawer(scope.row.id)"
                v-hasPerm="['opportunity:list:edit']"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                type="success"
                size="small"
                link
                @click="handleDetail(scope.row.id)"
                v-hasPerm="['opportunity:list:query']"
              >
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-button
                type="info"
                size="small"
                link
                @click="handleFollow(scope.row.id)"
                v-hasPerm="['opportunity:list:query']"
              >
                <el-icon><Calendar /></el-icon>
                跟进情况
              </el-button>

              <el-dropdown @command="(command: string) => handleCommand(command, scope.row)">
                <el-button type="primary" size="small" link>
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="win" v-if="scope.row.opportunityStatus === 'active'">
                      <el-icon><SuccessFilled /></el-icon>
                      成交
                    </el-dropdown-item>
                    <el-dropdown-item command="lose" v-if="scope.row.opportunityStatus === 'active'">
                      <el-icon><CloseBold /></el-icon>
                      失败
                    </el-dropdown-item>
                    <el-dropdown-item command="archive" divided v-if="scope.row.opportunityStatus === 'lost' || scope.row.opportunityStatus === 'cancelled'">
                      <el-icon><FolderOpened /></el-icon>
                      归档
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <!-- 商机表单抽屉 -->
    <opportunity-form-drawer
      v-model="formDrawerVisible"
      :opportunity-id="currentOpportunityId"
      @success="handleQuery"
    />



    <!-- 转移负责人弹窗 -->
    <el-dialog v-model="transferDialog.visible" title="转移负责人" width="400px">
      <el-form>
        <el-form-item label="新负责人">
          <el-select v-model="transferDialog.responsibleUserId" placeholder="请选择新负责人" style="width: 100%">
            <el-option
              v-for="item in userOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="transferDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleTransferConfirm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 失败原因弹窗 -->
    <el-dialog v-model="loseDialog.visible" title="失败原因" width="400px">
      <el-form>
        <el-form-item label="失败原因">
          <el-select v-model="loseDialog.lostReason" placeholder="请选择失败原因" style="width: 100%">
            <el-option
              v-for="item in lostReasonOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="loseDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleLoseConfirm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 归档原因弹窗 -->
    <el-dialog v-model="archiveDialog.visible" title="归档原因" width="400px">
      <el-form>
        <el-form-item label="归档原因" required>
          <el-input
            v-model="archiveDialog.archiveReason"
            type="textarea"
            :rows="4"
            placeholder="请输入归档原因"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="archiveDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleArchiveConfirm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 商机详情抽屉 -->
    <opportunity-detail-drawer
      v-model="detailDrawerVisible"
      :opportunity-id="currentOpportunityId"
      @edit="handleEditFromDetail"
      @archive="handleArchiveFromDetail"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import {
  Search,
  Refresh,
  Plus,
  Sort,
  Delete,
  Edit,
  View,
  ArrowDown,
  SuccessFilled,
  CloseBold,
  Calendar,
  FolderOpened,
} from "@element-plus/icons-vue";
import {
  OpportunityPageQuery,
  OpportunityPageVO,
} from "@/api/opportunity/opportunity.api";
import OpportunityAPI from "@/api/opportunity/opportunity.api";
import UserAPI from "@/api/system/user.api";
import { useDictStore } from "@/store/modules/dict.store";
import OpportunityFormDrawer from "./OpportunityFormDrawer.vue";
import OpportunityDetailDrawer from "./OpportunityDetailDrawer.vue";

defineOptions({
  name: "Opportunity",
  inheritAttrs: false,
});

const dictStore = useDictStore();
const router = useRouter();

const queryFormRef = ref(ElForm);

const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<OpportunityPageQuery>({
  pageNum: 1,
  pageSize: 10,
});

const pageData = ref<OpportunityPageVO[]>([]);

const formDrawerVisible = ref(false);
const detailDrawerVisible = ref(false);
const currentOpportunityId = ref<number | undefined>(undefined);

const transferDialog = reactive({
  visible: false,
  responsibleUserId: undefined,
});

const loseDialog = reactive({
  visible: false,
  opportunityId: undefined as number | undefined,
  lostReason: "",
});

const archiveDialog = reactive({
  visible: false,
  archiveReason: "",
});

const opportunityTypeOptions = ref<OptionType[]>([]);
const opportunitySourceOptions = ref<OptionType[]>([]);
const opportunityStageOptions = ref<OptionType[]>([]);
const lostReasonOptions = ref<OptionType[]>([]);
const userOptions = ref<OptionType[]>([]);

/** 查询 */
function handleQuery() {
  loading.value = true;
  OpportunityAPI.getPage(queryParams)
    .then((response: any) => {
      // 根据后端实际返回结构处理响应数据
      // 后端返回: { code: "00000", data: { list, total }, msg }
      // axios会将响应包装在response.data中
      if (response.data && response.data.data) {
        // axios响应结构: response.data = { code, data: { list, total }, msg }
        pageData.value = response.data.data.list || [];
        total.value = response.data.data.total || 0;
        console.log('设置页面数据:', pageData.value.length, '条记录，总数:', total.value);
      } else if (response.data && response.data.list) {
        // 直接返回list的情况
        pageData.value = response.data.list || [];
        total.value = response.data.total || 0;
      } else if (response.list) {
        // 直接返回的情况
        pageData.value = response.list || [];
        total.value = response.total || 0;
      } else {
        // 兜底处理
        console.warn('未识别的响应结构:', response);
        pageData.value = [];
        total.value = 0;
      }
    })
    .catch((error) => {
      console.error('查询商机列表失败:', error);
      pageData.value = [];
      total.value = 0;
      ElMessage.error('查询失败，请重试');
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置查询 */
function resetQuery() {
  queryFormRef.value.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}

/** 行选择 */
function handleSelectionChange(selection: OpportunityPageVO[]) {
  ids.value = selection.map((item) => item.id!);
}

/** 打开抽屉 */
function openDrawer(id?: number) {
  currentOpportunityId.value = id;
  formDrawerVisible.value = true;
}



/** 归档商机 */
function handleArchive(id?: number) {
  if (id) {
    // 单个归档，检查商机状态
    const opportunity = pageData.value.find(item => item.id === id);
    if (!opportunity || (opportunity.opportunityStatus !== 'lost' && opportunity.opportunityStatus !== 'cancelled')) {
      ElMessage.warning("只有已失败或已取消的商机才能归档");
      return;
    }
    // 单个归档时，将当前ID设置为选中状态
    ids.value = [id];
  } else {
    // 批量归档，使用checkCanArchive检查
    if (!checkCanArchive()) {
      return;
    }
  }
  
  // 显示归档原因弹窗
  archiveDialog.visible = true;
  archiveDialog.archiveReason = "";
}

/** 确认归档 */
function handleArchiveConfirm() {
  if (!archiveDialog.archiveReason.trim()) {
    ElMessage.warning("请输入归档原因");
    return;
  }
  
  const opportunityIds = ids.value;
  if (opportunityIds.length === 0) {
    ElMessage.warning("请选择要归档的数据");
    return;
  }

  OpportunityAPI.archive(opportunityIds.join(","), archiveDialog.archiveReason).then(() => {
    ElMessage.success("归档成功");
    archiveDialog.visible = false;
    archiveDialog.archiveReason = "";
    handleQuery();
  }).catch(() => {
    ElMessage.error("归档失败");
  });
}

/** 查看详情 */
function handleDetail(id: number) {
  currentOpportunityId.value = id;
  detailDrawerVisible.value = true;
}

/** 从详情抽屉编辑 */
function handleEditFromDetail(id: number) {
  detailDrawerVisible.value = false;
  currentOpportunityId.value = id;
  formDrawerVisible.value = true;
}

/** 从详情抽屉归档 */
function handleArchiveFromDetail(id: number) {
  detailDrawerVisible.value = false;
  handleArchive(id);
}

/** 跟进记录 */
function handleFollow(opportunityId: string) {
  router.push(`/opportunity/follow/${opportunityId}`);
}

/** 转移负责人 */
function handleTransfer() {
  if (ids.value.length === 0) {
    ElMessage.warning("请选择要转移的商机");
    return;
  }
  transferDialog.visible = true;
}

/** 检查选中的商机是否可以归档 */
function checkCanArchive() {
  if (ids.value.length === 0) {
    ElMessage.warning("请选择要归档的商机");
    return false;
  }
  
  const selectedOpportunities = pageData.value.filter(item => ids.value.includes(item.id!));
  const canArchiveOpportunities = selectedOpportunities.filter(item => 
    item.opportunityStatus === 'lost' || item.opportunityStatus === 'cancelled'
  );
  
  if (canArchiveOpportunities.length === 0) {
    ElMessage.warning("只有已失败或已取消的商机才能归档");
    return false;
  }
  
  if (canArchiveOpportunities.length !== selectedOpportunities.length) {
    ElMessage.warning("选中的商机中包含无法归档的商机，只有已失败或已取消的商机才能归档");
    return false;
  }
  
  return true;
}

/** 确认转移负责人 */
function handleTransferConfirm() {
  if (!transferDialog.responsibleUserId) {
    ElMessage.warning("请选择新负责人");
    return;
  }

  OpportunityAPI.transfer(ids.value, transferDialog.responsibleUserId).then(() => {
    ElMessage.success("转移成功");
    transferDialog.visible = false;
    transferDialog.responsibleUserId = undefined;
    handleQuery();
  });
}

/** 操作命令 */
function handleCommand(command: string, row: OpportunityPageVO) {
  switch (command) {
    case "win":
      handleWin(row.id!);
      break;
    case "lose":
      loseDialog.visible = true;
      loseDialog.opportunityId = row.id;
      break;
    case "archive":
      handleArchive(row.id);
      break;
  }
}

/** 成交商机 */
function handleWin(id: number) {
  ElMessageBox.confirm("确认将此商机标记为成交?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "success",
  }).then(() => {
    OpportunityAPI.win(id).then(() => {
      ElMessage.success("操作成功");
      handleQuery();
    });
  });
}

/** 确认失败商机 */
function handleLoseConfirm() {
  if (!loseDialog.lostReason) {
    ElMessage.warning("请选择失败原因");
    return;
  }

  OpportunityAPI.lose(loseDialog.opportunityId!, loseDialog.lostReason).then(() => {
    ElMessage.success("操作成功");
    loseDialog.visible = false;
    loseDialog.opportunityId = undefined;
    loseDialog.lostReason = "";
    handleQuery();
  });
}



/** 获取阶段标签类型 */
function getStageTagType(stage: string): "success" | "primary" | "warning" | "info" | "danger" {
  const typeMap: Record<string, "success" | "primary" | "warning" | "info" | "danger"> = {
    initial: "info",
    interested: "success",
    proposal: "warning",
    negotiation: "danger",
    closed_won: "success",
    closed_lost: "info",
  };
  return typeMap[stage] || "info";
}

/** 获取状态标签类型 */
function getStatusTagType(status: string): "success" | "primary" | "warning" | "info" | "danger" {
  const typeMap: Record<string, "success" | "primary" | "warning" | "info" | "danger"> = {
    active: "primary",
    won: "success",
    lost: "danger",
    postponed: "warning",
    cancelled: "info",
  };
  return typeMap[status] || "info";
}

/** 初始化字典数据 */
async function initDictData() {
  // 获取商机类型字典
  await dictStore.loadDictItems("opportunity_type");
  opportunityTypeOptions.value = dictStore.getDictItems("opportunity_type");

  // 获取商机来源字典
  await dictStore.loadDictItems("opportunity_source");
  opportunitySourceOptions.value = dictStore.getDictItems("opportunity_source");

  // 获取商机阶段字典
  await dictStore.loadDictItems("opportunity_stage");
  opportunityStageOptions.value = dictStore.getDictItems("opportunity_stage");

  // 获取失败原因字典
  await dictStore.loadDictItems("lost_reason");
  lostReasonOptions.value = dictStore.getDictItems("lost_reason");

  // 获取用户选项
  try {
    userOptions.value = await UserAPI.getOptions();
  } catch (error) {
    console.error('获取用户选项失败:', error);
  }

  // TODO: 获取伙伴选项
  // partnerOptions.value = await PartnerAPI.getOptions();
}

onMounted(async () => {
  handleQuery();
  await initDictData();
});
</script>

<style scoped>
.tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin: 0;
  max-width: 100%;
  word-break: break-all;
}

.follow-count {
  text-align: center;
}
</style>