import request from '@/utils/request';
import { AxiosPromise } from 'axios';

/**
 * 商机跟进记录查询参数
 */
export interface OpportunityFollowPageQuery extends PageQuery {
  opportunityId?: number;
  followType?: string;
  followResult?: string;
  followUserId?: number;
  contactPerson?: string;
  followStartDate?: string;
  followEndDate?: string;
  createStartTime?: string;
  createEndTime?: string;
}

/**
 * 商机跟进记录分页对象
 */
export interface OpportunityFollowPageVO {
  id?: number;
  opportunityId?: number;
  opportunityName?: string;
  opportunityCode?: string;
  followType?: string;
  followTypeLabel?: string;
  followDate?: string;
  followDuration?: number;
  contactPerson?: string;
  followContent?: string;
  followResult?: string;
  followResultLabel?: string;
  nextAction?: string;
  nextFollowDate?: string;
  attachmentId?: string;
  followUserId?: number;
  followUserName?: string;
  remark?: string;
  createTime?: string;
  updateTime?: string;
}

/**
 * 商机跟进记录表单类型
 */
export interface OpportunityFollowForm {
  id?: number;
  opportunityId?: number;
  followType?: string;
  followDate?: string;
  followDuration?: number;
  contactPerson?: string;
  followContent?: string;
  followResult?: string;
  nextAction?: string;
  nextFollowDate?: string;
  attachmentId?: string;
  followUserId?: number;
  remark?: string;
}

/**
 * 商机跟进记录API
 */
class OpportunityFollowAPI {
  /**
   * 获取跟进记录分页列表
   *
   * @param queryParams 查询参数
   */
  static getPage(queryParams: OpportunityFollowPageQuery): AxiosPromise<PageResult<OpportunityFollowPageVO[]>> {
    return request({
      url: '/api/v1/opportunity-follows/page',
      method: 'get',
      params: queryParams,
    });
  }

  /**
   * 获取跟进记录详情
   *
   * @param id 跟进记录ID
   */
  static getDetail(id: number): AxiosPromise<OpportunityFollowPageVO> {
    return request({
      url: `/api/v1/opportunity-follows/${id}`,
      method: 'get',
    });
  }

  /**
   * 获取跟进记录表单数据
   *
   * @param id 跟进记录ID
   */
  static getFormData(id: number): AxiosPromise<OpportunityFollowForm> {
    return request({
      url: `/api/v1/opportunity-follows/${id}/form`,
      method: 'get',
    });
  }

  /**
   * 新增跟进记录
   *
   * @param data 表单数据
   */
  static create(data: OpportunityFollowForm): AxiosPromise<any> {
    return request({
      url: '/api/v1/opportunity-follows',
      method: 'post',
      data: data,
    });
  }

  /**
   * 修改跟进记录
   *
   * @param id 跟进记录ID
   * @param data 表单数据
   */
  static update(id: number, data: OpportunityFollowForm): AxiosPromise<any> {
    return request({
      url: `/api/v1/opportunity-follows/${id}`,
      method: 'put',
      data: data,
    });
  }

  /**
   * 删除跟进记录
   *
   * @param ids 跟进记录ID，多个以英文逗号(,)分割
   */
  static delete(ids: string): AxiosPromise<any> {
    return request({
      url: `/api/v1/opportunity-follows/${ids}`,
      method: 'delete',
    });
  }

  /**
   * 根据商机ID查询跟进记录列表
   *
   * @param opportunityId 商机ID
   */
  static getByOpportunityId(opportunityId: number): AxiosPromise<OpportunityFollowPageVO[]> {
    return request({
      url: `/api/v1/opportunity-follows/opportunity/${opportunityId}`,
      method: 'get',
    });
  }
}

export default OpportunityFollowAPI;