package com.hntsz.boot.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.system.model.entity.DictItem;
import com.hntsz.boot.system.model.query.DictItemPageQuery;
import com.hntsz.boot.system.model.vo.DictItemPageVO;

import org.apache.ibatis.annotations.Mapper;

/**
 * 字典项映射层
 *
 * <AUTHOR>
 * @since 2.9.0
 */
@Mapper
public interface DictItemMapper extends BaseMapper<DictItem> {

    /**
     * 字典项分页列表
     */
    Page<DictItemPageVO> getDictItemPage(Page<DictItemPageVO> page, DictItemPageQuery queryParams);
}




