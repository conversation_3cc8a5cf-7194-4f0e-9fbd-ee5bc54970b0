import type { IModalConfig, IContentConfig, ISearchConfig } from "@/components/CURD/types";

export interface PageLightProps {
  // 当前页面主体
  subjectName: string;

  // 编辑模态框是否使用添加模态框
  editModalUseAddModal?: boolean;
  // 编辑模态框提交（editModalUseAddModal为true时，使用）
  editModelSubmit?: (_: any) => Promise<any>;

  // 编辑模态框获取数据()
  editModelFetch?: (_: any) => Promise<any>;

  contentConfig: IContentConfig;
  addModalConfig?: IModalConfig;
  editModalConfig?: IModalConfig;
  searchConfig?: ISearchConfig;
}
