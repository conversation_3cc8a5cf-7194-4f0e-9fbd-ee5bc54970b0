package com.hntsz.boot.modules.contract.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hntsz.boot.common.base.BaseEntityExtra;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * 付款文件关联表
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Getter
@Setter
@TableName("payment_attachment")
@Schema(description = "付款文件关联")
public class PaymentAttachment extends BaseEntityExtra {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "付款ID")
    @TableField("payment_id")
    private Long paymentId;

    @Schema(description = "附件ID")
    @TableField("attachment_id")
    private Long attachmentId;

    @Schema(description = "排序")
    @TableField("sort")
    private Integer sort;
}