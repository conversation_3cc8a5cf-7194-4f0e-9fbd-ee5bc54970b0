package com.hntsz.boot.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hntsz.boot.core.security.extension.oauth.OAuthUserInfo;
import com.hntsz.boot.system.mapper.OAuthTokenMapper;
import com.hntsz.boot.system.model.entity.OAuthToken;
import com.hntsz.boot.system.service.OAuthTokenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * OAuth 令牌服务实现
 *
 * <AUTHOR>
 * @since 2.22.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OAuthTokenServiceImpl extends ServiceImpl<OAuthTokenMapper, OAuthToken> implements OAuthTokenService {

    @Override
    public void saveOrUpdateToken(Long userId, OAuthUserInfo oAuthUserInfo) {
        OAuthToken existingToken = getTokenByUserAndProvider(userId, oAuthUserInfo.getProvider());
        
        if (existingToken != null) {
            // 更新现有令牌
            updateTokenInfo(existingToken, oAuthUserInfo);
            this.updateById(existingToken);
            log.info("更新用户 {} 的 {} OAuth令牌", userId, oAuthUserInfo.getProvider());
        } else {
            // 创建新令牌
            OAuthToken newToken = createTokenFromUserInfo(userId, oAuthUserInfo);
            this.save(newToken);
            log.info("保存用户 {} 的 {} OAuth令牌", userId, oAuthUserInfo.getProvider());
        }
    }

    @Override
    public OAuthToken getTokenByUserAndProvider(Long userId, String provider) {
        return this.getOne(new LambdaQueryWrapper<OAuthToken>()
                .eq(OAuthToken::getUserId, userId)
                .eq(OAuthToken::getProvider, provider));
    }

    @Override
    public void removeToken(Long userId, String provider) {
        int removed = this.getBaseMapper().delete(new LambdaQueryWrapper<OAuthToken>()
                .eq(OAuthToken::getUserId, userId)
                .eq(OAuthToken::getProvider, provider));
        
        if (removed > 0) {
            log.info("删除用户 {} 的 {} OAuth令牌", userId, provider);
        }
    }

    /**
     * 更新令牌信息
     */
    private void updateTokenInfo(OAuthToken token, OAuthUserInfo oAuthUserInfo) {
        token.setOauthId(oAuthUserInfo.getOauthId());
        token.setAccessToken(oAuthUserInfo.getAccessToken());
        token.setTokenType(oAuthUserInfo.getTokenType());
        token.setExpiresIn(oAuthUserInfo.getExpiresIn());
        token.setRefreshToken(oAuthUserInfo.getRefreshToken());
        token.setScope(oAuthUserInfo.getScope());
        token.setExpiresAt(calculateExpiresAt(oAuthUserInfo.getExpiresIn()));
        token.setUpdatedAt(LocalDateTime.now());
    }

    /**
     * 从用户信息创建令牌
     */
    private OAuthToken createTokenFromUserInfo(Long userId, OAuthUserInfo oAuthUserInfo) {
        OAuthToken token = new OAuthToken();
        token.setUserId(userId);
        token.setProvider(oAuthUserInfo.getProvider());
        token.setOauthId(oAuthUserInfo.getOauthId());
        token.setAccessToken(oAuthUserInfo.getAccessToken());
        token.setTokenType(oAuthUserInfo.getTokenType());
        token.setExpiresIn(oAuthUserInfo.getExpiresIn());
        token.setRefreshToken(oAuthUserInfo.getRefreshToken());
        token.setScope(oAuthUserInfo.getScope());
        token.setExpiresAt(calculateExpiresAt(oAuthUserInfo.getExpiresIn()));
        token.setCreatedAt(LocalDateTime.now());
        token.setUpdatedAt(LocalDateTime.now());
        return token;
    }

    /**
     * 计算过期时间
     */
    private LocalDateTime calculateExpiresAt(Integer expiresIn) {
        if (expiresIn == null || expiresIn <= 0) {
            return null;
        }
        return LocalDateTime.now().plusSeconds(expiresIn);
    }
}