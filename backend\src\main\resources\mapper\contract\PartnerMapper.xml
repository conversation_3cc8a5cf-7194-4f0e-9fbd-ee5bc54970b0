<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.modules.contract.mapper.PartnerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hntsz.boot.modules.contract.model.entity.Partner">
        <id column="id" property="id" />
        <result column="partner_name" property="partnerName" />
        <result column="partner_code" property="partnerCode" />
        <result column="is_our_company" property="isOurCompany" />
        <result column="partner_type" property="partnerType" />
        <result column="legal_representative" property="legalRepresentative" />
        <result column="contact_person" property="contactPerson" />
        <result column="contact_phone" property="contactPhone" />
        <result column="contact_email" property="contactEmail" />
        <result column="address" property="address" />
        <result column="certificate_type" property="certificateType" />
        <result column="certificate_number" property="certificateNumber" />
        <result column="tax_number" property="taxNumber" />
        <result column="bank_name" property="bankName" />
        <result column="bank_account" property="bankAccount" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, partner_name, partner_code, is_our_company, partner_type, legal_representative, 
        contact_person, contact_phone, contact_email, address, certificate_type, certificate_number, 
        tax_number, bank_name, bank_account, status, remark, create_by, create_time, update_by, 
        update_time, is_deleted
    </sql>

    <!-- 获取业务伙伴分页列表 -->
    <select id="getPartnerPage" resultType="com.hntsz.boot.modules.contract.model.vo.PartnerVO">
        SELECT
            p.id,
            p.partner_name,
            p.partner_code,
            p.is_our_company,
            p.partner_type,
            pt.label AS partner_type_label,
            p.legal_representative,
            p.contact_person,
            p.contact_phone,
            p.contact_email,
            p.address,
            p.certificate_type,
            ct.label AS certificate_type_label,
            p.certificate_number,
            p.tax_number,
            p.bank_name,
            p.bank_account,
            p.status,
            p.remark,
            p.create_time,
            p.update_time
        FROM partner p
        LEFT JOIN sys_dict_item pt ON p.partner_type = pt.value AND pt.dict_code = 'partner_type'
        LEFT JOIN sys_dict_item ct ON p.certificate_type = ct.value AND ct.dict_code = 'certificate_type'
        WHERE p.is_deleted = 0
        <if test="query != null">
            <if test="query.keywords != null and query.keywords != ''">
                AND (p.partner_name LIKE CONCAT('%', #{query.keywords}, '%')
                OR p.partner_code LIKE CONCAT('%', #{query.keywords}, '%')
                OR p.contact_person LIKE CONCAT('%', #{query.keywords}, '%'))
            </if>
            <if test="query.partnerType != null and query.partnerType != ''">
                AND p.partner_type = #{query.partnerType}
            </if>
            <if test="query.isOurCompany != null">
                AND p.is_our_company = #{query.isOurCompany}
            </if>
            <if test="query.status != null and query.status != ''">
                AND p.status = #{query.status}
            </if>
            <if test="query.certificateType != null and query.certificateType != ''">
                AND p.certificate_type = #{query.certificateType}
            </if>
        </if>
        ORDER BY p.create_time DESC
    </select>

    <!-- 获取业务伙伴详情 -->
    <select id="getPartnerDetail" resultType="com.hntsz.boot.modules.contract.model.vo.PartnerVO">
        SELECT 
            p.id,
            p.partner_name,
            p.partner_code,
            p.is_our_company,
            p.partner_type,
            pt.label AS partner_type_label,
            p.legal_representative,
            p.contact_person,
            p.contact_phone,
            p.contact_email,
            p.address,
            p.certificate_type,
            ct.label AS certificate_type_label,
            p.certificate_number,
            p.tax_number,
            p.bank_name,
            p.bank_account,
            p.status,
            p.remark,
            p.create_time,
            p.update_time
        FROM partner p
        LEFT JOIN sys_dict_item pt ON p.partner_type = pt.value AND pt.dict_code = 'partner_type'
        LEFT JOIN sys_dict_item ct ON p.certificate_type = ct.value AND ct.dict_code = 'certificate_type'
        WHERE p.id = #{id} AND p.is_deleted = 0
    </select>

    <!-- 获取业务伙伴选项列表 -->
    <select id="getPartnerOptions" resultType="com.hntsz.boot.common.model.Option">
        SELECT 
            id AS value,
            partner_name AS label
        FROM partner
        WHERE is_deleted = 0 AND status = 'active'
        ORDER BY partner_name
    </select>

    <!-- 根据伙伴名称查询伙伴 -->
    <select id="getByPartnerName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM partner
        WHERE partner_name = #{partnerName} AND is_deleted = 0
        LIMIT 1
    </select>

    <!-- 根据伙伴编码查询伙伴 -->
    <select id="getByPartnerCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM partner
        WHERE partner_code = #{partnerCode} AND is_deleted = 0
        LIMIT 1
    </select>

</mapper>
