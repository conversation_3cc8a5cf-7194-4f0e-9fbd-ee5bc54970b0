<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="${package.Mapper}.${table.mapperName}">

#if(${enableCache})
    <!-- 开启二级缓存 -->
    <cache type="${cacheClassName}"/>

#end
#if(${baseResultMap})
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="${package.Entity}.${entity}">
#foreach($field in ${table.fields})
#if(${field.keyFlag})##生成主键排在第一位
        <id column="${field.name}" property="${field.propertyName}" />
#end
#end
#foreach($field in ${table.commonFields})##生成公共字段
        <result column="${field.name}" property="${field.propertyName}" />
#end
#foreach($field in ${table.fields})
#if(!${field.keyFlag})##生成普通字段
        <result column="${field.name}" property="${field.propertyName}" />
#end
#end
    </resultMap>

#end
#if(${baseColumnList})
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
#foreach($field in ${table.commonFields})
        ${field.columnName},
#end
        ${table.fieldNames}
    </sql>

#end

    <!-- ${table.comment}分页列表 -->
    <select id="listPaged${entity}s" resultType="${package.Parent}.model.bo.${entity}BO">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            ${table.name}
        <where>
            is_deleted = 0
            <if test='queryParams.keywords!=null and queryParams.keywords.trim() neq ""'>
                AND (
                    name LIKE CONCAT('%',#{queryParams.keywords},'%')
                )
            </if>
            <if test="queryParams.startTime != null">
                AND create_time &gt;= #{queryParams.startTime}
            </if>
            <if test="queryParams.endTime != null">
                AND create_time &lt;= #{queryParams.endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
