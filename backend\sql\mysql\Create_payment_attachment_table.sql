-- 创建付款文件关联表
CREATE TABLE payment_attachment (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    payment_id BIGINT NOT NULL COMMENT '付款ID',
    attachment_id BIGINT NOT NULL COMMENT '附件ID',
    sort INT DEFAULT 0 COMMENT '排序',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(100) DEFAULT '' COMMENT '创建者',
    update_by VARCHAR(100) DEFAULT '' COMMENT '更新者',
    is_deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除(0:否，1:是)',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='付款文件关联表';