import { Storage } from "./storage";

// 存储键常量
const ACCESS_TOKEN_KEY = "accessToken";
const REFRESH_TOKEN_KEY = "refreshToken";
const REMEMBER_ME_KEY = "rememberMe";

/**
 * 身份验证工具类
 * 集中管理所有与认证相关的功能
 */
export class Auth {
  /**
   * 判断用户是否已登录
   * @returns 是否已登录
   */
  static isLoggedIn(): boolean {
    return !!Auth.getAccessToken();
  }

  /**
   * 获取当前有效的访问令牌
   * @returns 当前有效的访问令牌
   */
  static getAccessToken(): string {
    const isRememberMe = Storage.get<boolean>(REMEMBER_ME_KEY, false);
    return isRememberMe
      ? Storage.get(ACCESS_TOKEN_KEY, "")
      : Storage.sessionGet(ACCESS_TOKEN_KEY, "");
  }

  /**
   * 获取刷新令牌
   * @returns 当前有效的刷新令牌
   */
  static getRefreshToken(): string {
    const isRememberMe = Storage.get<boolean>(REMEMBER_ME_KEY, false);
    return isRememberMe
      ? Storage.get(REFRESH_TOKEN_KEY, "")
      : Storage.sessionGet(REFRESH_TOKEN_KEY, "");
  }

  /**
   * 设置访问令牌和刷新令牌
   * @param accessToken 访问令牌
   * @param refreshToken 刷新令牌
   * @param rememberMe 是否记住我
   */
  static setTokens(
    accessToken: string,
    refreshToken: string,
    rememberMe: boolean
  ): void {
    Storage.set(REMEMBER_ME_KEY, rememberMe);

    if (rememberMe) {
      // 使用localStorage长期保存
      Storage.set(ACCESS_TOKEN_KEY, accessToken);
      Storage.set(REFRESH_TOKEN_KEY, refreshToken);
    } else {
      // 使用sessionStorage临时保存
      Storage.sessionSet(ACCESS_TOKEN_KEY, accessToken);
      Storage.sessionSet(REFRESH_TOKEN_KEY, refreshToken);
      Storage.remove(ACCESS_TOKEN_KEY);
      Storage.remove(REFRESH_TOKEN_KEY);
    }
  }

  /**
   * 获取记住我状态
   * @returns 记住我状态
   */
  static getRememberMe(): boolean {
    return Storage.get<boolean>(REMEMBER_ME_KEY, false);
  }

  /**
   * 清除所有认证信息
   */
  static clearAuth(): void {
    Storage.remove(ACCESS_TOKEN_KEY);
    Storage.remove(REFRESH_TOKEN_KEY);
    Storage.remove(REMEMBER_ME_KEY);
    Storage.sessionRemove(ACCESS_TOKEN_KEY);
    Storage.sessionRemove(REFRESH_TOKEN_KEY);
  }
}
