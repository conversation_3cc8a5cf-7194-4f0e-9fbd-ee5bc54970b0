SET NAMES utf8mb4;
-- ----------------------------
-- 1. 商机/线索表
-- ----------------------------
DROP TABLE IF EXISTS `opportunity`;
CREATE TABLE `opportunity` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `opportunity_code` varchar(50) NOT NULL COMMENT '商机编码(自动生成)',
    `opportunity_name` varchar(200) NOT NULL COMMENT '商机名称',
    `opportunity_type` varchar(20) NOT NULL COMMENT '商机类型(关联字典编码：opportunity_type)',
    `opportunity_source` varchar(20) COMMENT '商机来源(关联字典编码：opportunity_source)',
    `partner_id` bigint COMMENT '关联客户ID(关联partner表)',
    `contact_person` varchar(100) COMMENT '联系人',
    `contact_phone` varchar(20) COMMENT '联系电话',
    `contact_email` varchar(100) COMMENT '联系邮箱',
    `opportunity_stage` varchar(20) DEFAULT 'initial' COMMENT '商机阶段(initial-初步接触 interested-有意向 proposal-方案阶段 negotiation-谈判阶段 closed_won-成交 closed_lost-失败)',
    `win_probability` int DEFAULT 0 COMMENT '成单概率(%)',
    `estimated_amount` decimal(15,2) COMMENT '预估金额',
    `estimated_close_date` date COMMENT '预计成交日期',
    `actual_close_date` date COMMENT '实际成交日期',
    `opportunity_status` varchar(20) DEFAULT 'active' COMMENT '商机状态(active-进行中 won-已成交 lost-已失败 cancelled-已取消 archived-已归档)',
    `lost_reason` varchar(20) COMMENT '失败原因(关联字典编码：lost_reason)',
    `priority` varchar(10) DEFAULT 'medium' COMMENT '优先级(high-高 medium-中 low-低)',
    `product_interest` varchar(500) COMMENT '感兴趣的产品/服务',
    `requirements` text COMMENT '客户需求描述',
    `competition_info` varchar(500) COMMENT '竞争对手信息',
    `next_action` varchar(500) COMMENT '下一步行动计划',
    `next_follow_date` date COMMENT '下次跟进日期',
    `responsible_user_id` bigint COMMENT '负责人ID(关联sys_user表)',
    `dept_id` bigint COMMENT '所属部门ID(关联sys_dept表)',
    `tags` varchar(200) COMMENT '标签(多个标签用逗号分隔)',
    `archive_reason` varchar(500) COMMENT '归档原因',
    `remark` text COMMENT '备注',
    `create_by` bigint COMMENT '创建人ID',
    `create_time` datetime COMMENT '创建时间',
    `update_by` bigint COMMENT '修改人ID',
    `update_time` datetime COMMENT '更新时间',
    `is_deleted` tinyint DEFAULT 0 COMMENT '逻辑删除标识(1-已删除 0-未删除)',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COMMENT = '商机线索表';

-- ----------------------------
-- 2. 商机跟进记录表
-- ----------------------------
DROP TABLE IF EXISTS `opportunity_follow`;
CREATE TABLE `opportunity_follow` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `opportunity_id` bigint NOT NULL COMMENT '商机ID',
    `follow_type` varchar(20) NOT NULL COMMENT '跟进方式(关联字典编码：follow_type)',
    `follow_date` datetime NOT NULL COMMENT '跟进时间',
    `follow_duration` int COMMENT '跟进时长(分钟)',
    `contact_person` varchar(100) COMMENT '联系人',
    `follow_content` text NOT NULL COMMENT '跟进内容',
    `follow_result` varchar(20) COMMENT '跟进结果(关联字典编码：follow_result)',
    `next_action` varchar(500) COMMENT '下一步行动计划',
    `next_follow_date` date COMMENT '下次跟进日期',
    `attachment_id` varchar(500) COMMENT '附件ID',
    `follow_user_id` bigint COMMENT '跟进人ID(关联sys_user表)',
    `remark` varchar(500) COMMENT '备注',
    `create_by` bigint COMMENT '创建人ID',
    `create_time` datetime COMMENT '创建时间',
    `update_by` bigint COMMENT '修改人ID',
    `update_time` datetime COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COMMENT = '商机跟进记录表';


-- ----------------------------
-- 4. 初始化字典数据
-- ----------------------------
-- 清理已存在的字典数据，实现幂等性
DELETE FROM `sys_dict_item` WHERE `dict_code` IN ('opportunity_type', 'opportunity_source', 'opportunity_stage', 'lost_reason', 'follow_type', 'follow_result');
DELETE FROM `sys_dict` WHERE `dict_code` IN ('opportunity_type', 'opportunity_source', 'opportunity_stage', 'lost_reason', 'follow_type', 'follow_result');

-- 商机类型字典
INSERT INTO `sys_dict` (`dict_code`, `name`, `status`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`, `is_deleted`)
VALUES ('opportunity_type', '商机类型', 1, '商机管理-商机类型', now(), 1, now(), 1, 0);

INSERT INTO `sys_dict_item` (`dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
('opportunity_type', 'new_customer', '新客户开发', 'primary', 1, 1, '开发新客户的商机', now(), 1, now(), 1),
('opportunity_type', 'existing_customer', '老客户复购', 'success', 1, 2, '现有客户的复购商机', now(), 1, now(), 1),
('opportunity_type', 'upsell', '向上销售', 'info', 1, 3, '向现有客户销售更高端产品', now(), 1, now(), 1),
('opportunity_type', 'cross_sell', '交叉销售', 'warning', 1, 4, '向现有客户销售其他产品', now(), 1, now(), 1),
('opportunity_type', 'renewal', '续约', '', 1, 5, '合同续约商机', now(), 1, now(), 1),
('opportunity_type', 'partnership', '合作伙伴', '', 1, 6, '合作伙伴商机', now(), 1, now(), 1),
('opportunity_type', 'other', '其他', '', 1, 99, '其他类型商机', now(), 1, now(), 1);

-- 商机来源字典
INSERT INTO `sys_dict` (`dict_code`, `name`, `status`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`, `is_deleted`)
VALUES ('opportunity_source', '商机来源', 1, '商机管理-商机来源', now(), 1, now(), 1, 0);

INSERT INTO `sys_dict_item` (`dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
('opportunity_source', 'website', '官网咨询', 'primary', 1, 1, '通过官网获得的商机', now(), 1, now(), 1),
('opportunity_source', 'phone', '电话咨询', 'success', 1, 2, '电话咨询获得的商机', now(), 1, now(), 1),
('opportunity_source', 'exhibition', '展会', 'info', 1, 3, '展会获得的商机', now(), 1, now(), 1),
('opportunity_source', 'referral', '客户推荐', 'warning', 1, 4, '客户推荐的商机', now(), 1, now(), 1),
('opportunity_source', 'cold_call', '陌生拜访', '', 1, 5, '陌生拜访获得的商机', now(), 1, now(), 1),
('opportunity_source', 'social_media', '社交媒体', '', 1, 6, '社交媒体获得的商机', now(), 1, now(), 1),
('opportunity_source', 'advertisement', '广告推广', '', 1, 7, '广告推广获得的商机', now(), 1, now(), 1),
('opportunity_source', 'partnership', '合作伙伴', '', 1, 8, '合作伙伴介绍的商机', now(), 1, now(), 1),
('opportunity_source', 'other', '其他', '', 1, 99, '其他来源商机', now(), 1, now(), 1);

-- 商机阶段字典
INSERT INTO `sys_dict` (`dict_code`, `name`, `status`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`, `is_deleted`)
VALUES ('opportunity_stage', '商机阶段', 1, '商机管理-商机阶段', now(), 1, now(), 1, 0);

INSERT INTO `sys_dict_item` (`dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
('opportunity_stage', 'early', '线索', 'info', 1, 1, '在商机的较早期阶段', now(), 1, now(), 1),
('opportunity_stage', 'initial', '初步接触', 'info', 1, 2, '与客户的初步接触阶段', now(), 1, now(), 1),
('opportunity_stage', 'interested', '有意向', 'primary', 1, 3, '客户表现出兴趣', now(), 1, now(), 1),
('opportunity_stage', 'proposal', '方案阶段', 'warning', 1, 4, '提供解决方案或报价', now(), 1, now(), 1),
('opportunity_stage', 'negotiation', '谈判阶段', '', 1, 5, '价格和条件谈判阶段', now(), 1, now(), 1),
('opportunity_stage', 'closed_won', '成交', 'success', 1, 6, '成功签约', now(), 1, now(), 1),
('opportunity_stage', 'closed_lost', '失败', 'danger', 1, 7, '商机失败或流失', now(), 1, now(), 1);

-- 失败原因字典
INSERT INTO `sys_dict` (`dict_code`, `name`, `status`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`, `is_deleted`)
VALUES ('lost_reason', '失败原因', 1, '商机管理-失败原因', now(), 1, now(), 1, 0);

INSERT INTO `sys_dict_item` (`dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
('lost_reason', 'price_high', '价格过高', 'danger', 1, 1, '客户认为价格过高', now(), 1, now(), 1),
('lost_reason', 'competitor', '竞争对手', 'warning', 1, 2, '选择了竞争对手', now(), 1, now(), 1),
('lost_reason', 'no_budget', '预算不足', 'info', 1, 3, '客户预算不足', now(), 1, now(), 1),
('lost_reason', 'no_authority', '无决策权', '', 1, 4, '联系人无决策权', now(), 1, now(), 1),
('lost_reason', 'timing', '时机不对', '', 1, 5, '采购时机不合适', now(), 1, now(), 1),
('lost_reason', 'product_fit', '产品不匹配', '', 1, 6, '产品不符合需求', now(), 1, now(), 1),
('lost_reason', 'no_response', '无回应', '', 1, 7, '客户不回应跟进', now(), 1, now(), 1),
('lost_reason', 'project_cancelled', '项目取消', '', 1, 8, '客户项目取消', now(), 1, now(), 1),
('lost_reason', 'other', '其他', '', 1, 99, '其他原因', now(), 1, now(), 1);

-- 跟进方式字典
INSERT INTO `sys_dict` (`dict_code`, `name`, `status`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`, `is_deleted`)
VALUES ('follow_type', '跟进方式', 1, '商机管理-跟进方式', now(), 1, now(), 1, 0);

INSERT INTO `sys_dict_item` (`dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
('follow_type', 'phone', '电话跟进', 'primary', 1, 1, '电话跟进客户', now(), 1, now(), 1),
('follow_type', 'email', '邮件跟进', 'success', 1, 2, '邮件跟进客户', now(), 1, now(), 1),
('follow_type', 'visit', '上门拜访', 'info', 1, 3, '上门拜访客户', now(), 1, now(), 1),
('follow_type', 'wechat', '微信沟通', 'warning', 1, 4, '微信沟通', now(), 1, now(), 1),
('follow_type', 'meeting', '会议沟通', '', 1, 5, '会议沟通', now(), 1, now(), 1),
('follow_type', 'demo', '产品演示', '', 1, 6, '产品演示', now(), 1, now(), 1),
('follow_type', 'proposal', '方案提交', '', 1, 7, '提交解决方案', now(), 1, now(), 1),
('follow_type', 'quotation', '报价', '', 1, 8, '提交报价', now(), 1, now(), 1),
('follow_type', 'other', '其他', '', 1, 99, '其他跟进方式', now(), 1, now(), 1);

-- 跟进结果字典
INSERT INTO `sys_dict` (`dict_code`, `name`, `status`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`, `is_deleted`)
VALUES ('follow_result', '跟进结果', 1, '商机管理-跟进结果', now(), 1, now(), 1, 0);

INSERT INTO `sys_dict_item` (`dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
('follow_result', 'positive', '积极响应', 'success', 1, 1, '客户积极响应', now(), 1, now(), 1),
('follow_result', 'interested', '有兴趣', 'primary', 1, 2, '客户表示有兴趣', now(), 1, now(), 1),
('follow_result', 'considering', '考虑中', 'info', 1, 3, '客户考虑中', now(), 1, now(), 1),
('follow_result', 'need_more_info', '需要更多信息', 'warning', 1, 4, '客户需要更多信息', now(), 1, now(), 1),
('follow_result', 'price_concern', '价格有异议', 'danger', 1, 5, '对价格有异议', now(), 1, now(), 1),
('follow_result', 'no_response', '无回应', '', 1, 6, '客户无回应', now(), 1, now(), 1),
('follow_result', 'rejected', '拒绝', '', 1, 7, '客户明确拒绝', now(), 1, now(), 1),
('follow_result', 'postponed', '延期', '', 1, 8, '项目延期', now(), 1, now(), 1),
('follow_result', 'other', '其他', '', 1, 99, '其他结果', now(), 1, now(), 1);

-- ----------------------------
-- 5. 初始化菜单数据
-- ----------------------------
-- 清理已存在的菜单数据，实现幂等性
DELETE FROM `sys_role_menu` WHERE `menu_id` IN (170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182);
DELETE FROM `sys_menu` WHERE `id` IN (170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182);

-- 创建 "商机管理" 顶级目录
INSERT INTO `sys_menu`(`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `create_time`, `update_time`, `params`) VALUES
(170, 0, '0', '商机管理', 2, 'OpportunityMgmt', '/opportunity', 'Layout', NULL, 1, 0, 1, 0, 'el-icon-trophy', '/opportunity/list', NOW(), NOW(), NULL);

-- 创建 "商机列表" 菜单
INSERT INTO `sys_menu`(`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `create_time`, `update_time`, `params`) VALUES
(171, 170, '0,170', '商机列表', 1, 'OpportunityList', 'list', 'opportunity/opportunity/index', NULL, 0, 1, 1, 1, 'el-icon-data-analysis', NULL, NOW(), NOW(), NULL);

-- 创建 "商机列表" 的按钮权限
INSERT INTO `sys_menu`(`id`, `parent_id`, `tree_path`, `name`, `type`, `perm`, `sort`) VALUES
(172, 171, '0,170,171', '查询', 4, 'opportunity:list:query', 1),
(173, 171, '0,170,171', '新增', 4, 'opportunity:list:add', 2),
(174, 171, '0,170,171', '修改', 4, 'opportunity:list:edit', 3),
(175, 171, '0,170,171', '归档', 4, 'opportunity:list:archive', 4),
(176, 171, '0,170,171', '跟进', 4, 'opportunity:list:follow', 5),
(177, 171, '0,170,171', '转移', 4, 'opportunity:list:transfer', 6);

-- 创建 "跟进记录" 菜单
INSERT INTO `sys_menu`(`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `create_time`, `update_time`, `params`) VALUES
(178, 170, '0,170', '跟进记录', 1, 'OpportunityFollow', 'follow/:opportunityId', 'opportunity/follow/index', NULL, 0, 1, 0, 2, 'el-icon-chat-line-round', NULL, NOW(), NOW(), NULL);

-- 创建 "跟进记录" 的按钮权限
INSERT INTO `sys_menu`(`id`, `parent_id`, `tree_path`, `name`, `type`, `perm`, `sort`) VALUES
(179, 178, '0,170,178', '查询跟进', 4, 'opportunity:follow:query', 1),
(180, 178, '0,170,178', '新增跟进', 4, 'opportunity:follow:add', 2),
(181, 178, '0,170,178', '修改跟进', 4, 'opportunity:follow:edit', 3),
(182, 178, '0,170,178', '删除跟进', 4, 'opportunity:follow:delete', 4);

-- 为系统管理员分配菜单权限
INSERT INTO `sys_role_menu` VALUES (2, 170);
INSERT INTO `sys_role_menu` VALUES (2, 171);
INSERT INTO `sys_role_menu` VALUES (2, 172);
INSERT INTO `sys_role_menu` VALUES (2, 173);
INSERT INTO `sys_role_menu` VALUES (2, 174);
INSERT INTO `sys_role_menu` VALUES (2, 175);
INSERT INTO `sys_role_menu` VALUES (2, 176);
INSERT INTO `sys_role_menu` VALUES (2, 177);
INSERT INTO `sys_role_menu` VALUES (2, 178);
INSERT INTO `sys_role_menu` VALUES (2, 179);
INSERT INTO `sys_role_menu` VALUES (2, 180);
INSERT INTO `sys_role_menu` VALUES (2, 181);
INSERT INTO `sys_role_menu` VALUES (2, 182);
-- ----------------------------
-- 1. 业务伙伴表 (独立的伙伴信息)
-- ----------------------------
DROP TABLE IF EXISTS `partner`;
CREATE TABLE `partner` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `partner_name` varchar(200) NOT NULL COMMENT '伙伴名称',
    `partner_code` varchar(50) COMMENT '伙伴编码',
    `is_our_company` tinyint DEFAULT 0 COMMENT '是否我司或旗下企业(1-是 0-否)',
    `partner_type` varchar(20) NOT NULL COMMENT '伙伴类型(关联字典编码：partner_type)',
    `legal_representative` varchar(100) COMMENT '法定代表人',
    `contact_person` varchar(100) COMMENT '联系人',
    `contact_phone` varchar(20) COMMENT '联系电话',
    `contact_email` varchar(100) COMMENT '联系邮箱',
    `address` varchar(500) COMMENT '地址',
    `certificate_type` varchar(20) COMMENT '证件类型(关联字典编码：certificate_type)',
    `certificate_number` varchar(50) COMMENT '证件号码',
    `tax_number` varchar(50) COMMENT '税号',
    `bank_name` varchar(200) COMMENT '开户银行',
    `bank_account` varchar(50) COMMENT '银行账号',
    `status` varchar(10) DEFAULT 'active' COMMENT '状态(active-正常 inactive-禁用)',
    `remark` varchar(500) COMMENT '备注',
    `create_by` bigint COMMENT '创建人ID',
    `create_time` datetime COMMENT '创建时间',
    `update_by` bigint COMMENT '修改人ID',
    `update_time` datetime COMMENT '更新时间',
    `is_deleted` tinyint DEFAULT 0 COMMENT '逻辑删除标识(1-已删除 0-未删除)',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COMMENT = '业务伙伴表';

-- ----------------------------
-- 2. 合同主表
-- ----------------------------
DROP TABLE IF EXISTS `contract`;
CREATE TABLE `contract` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `contract_no` varchar(100) NOT NULL COMMENT '合同编号',
    `contract_name` varchar(200) NOT NULL COMMENT '合同名称',
    `contract_type` varchar(50) COMMENT '合同类型(关联字典编码：contract_type)',
    `contract_category` varchar(50) COMMENT '合同分类(关联字典编码：contract_category)',
    `contract_amount` decimal(15,2) COMMENT '合同金额',
    `opportunity_id` bigint COMMENT '关联商机ID(关联opportunity表)',
    `signing_date` date COMMENT '签署日期',
    `effective_date` date COMMENT '生效日期',
    `expiry_date` date COMMENT '到期日期',
    `contract_status` varchar(20) DEFAULT 'draft' COMMENT '合同状态(draft-草稿 pending-待签署 active-已生效 completed-已完成 terminated-已终止 cancelled-已作废)',
    `payment_method` varchar(50) COMMENT '付款方式(关联字典编码：payment_method)',
    `signing_location` varchar(200) COMMENT '签署地点',
    `responsible_user_id` bigint COMMENT '负责人ID(关联sys_user表)',
    `dept_id` bigint COMMENT '所属部门ID(关联sys_dept表)',
    `remark` varchar(500) COMMENT '备注',
    `create_by` bigint COMMENT '创建人ID',
    `create_time` datetime COMMENT '创建时间',
    `update_by` bigint COMMENT '修改人ID',
    `update_time` datetime COMMENT '更新时间',
    `is_deleted` tinyint DEFAULT 0 COMMENT '逻辑删除标识(1-已删除 0-未删除)',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COMMENT = '合同主表';

-- ----------------------------
-- 3. 合同伙伴关联表 (中间表)
-- ----------------------------
DROP TABLE IF EXISTS `contract_partner_relation`;
CREATE TABLE `contract_partner_relation` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `contract_id` bigint NOT NULL COMMENT '合同ID',
    `partner_id` bigint NOT NULL COMMENT '伙伴ID',
    `partner_role` varchar(20) NOT NULL COMMENT '伙伴角色(关联字典编码：partner_role)',
    `partner_role_desc` varchar(20) COMMENT '角色描述(关联字典编码：partner_role_desc)',
    `signing_person` varchar(100) COMMENT '签署人',
    `signing_person_title` varchar(100) COMMENT '签署人职务',
    `signing_date` date COMMENT '签署日期',
    `sort` int DEFAULT 0 COMMENT '排序',
    `remark` varchar(500) COMMENT '备注',
    `create_by` bigint COMMENT '创建人ID',
    `create_time` datetime COMMENT '创建时间',
    `update_by` bigint COMMENT '修改人ID',
    `update_time` datetime COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COMMENT = '合同伙伴关联表';

-- ----------------------------
-- 4. 合同文件关联表
-- ----------------------------
DROP TABLE IF EXISTS `contract_attachment`;
CREATE TABLE `contract_attachment` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `contract_id` bigint NOT NULL COMMENT '合同ID(关联contract表)',
    `attachment_id` bigint NOT NULL COMMENT '附件ID(关联tsz_attachment表)',
    `sort` int DEFAULT 0 COMMENT '排序',
    `create_by` bigint COMMENT '创建人ID',
    `create_time` datetime COMMENT '创建时间',
    `update_by` bigint COMMENT '修改人ID',
    `update_time` datetime COMMENT '更新时间',
    `is_deleted` tinyint DEFAULT 0 COMMENT '逻辑删除标识(1-已删除 0-未删除)',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_contract_id` (`contract_id`) USING BTREE,
    KEY `idx_attachment_id` (`attachment_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COMMENT = '合同文件关联表';

-- ----------------------------
-- 5. 合同付款记录表
-- ----------------------------
DROP TABLE IF EXISTS `contract_payment`;
CREATE TABLE `contract_payment` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `contract_id` bigint NOT NULL COMMENT '合同ID',
    `payment_no` varchar(100) COMMENT '付款单号',
    `payment_type` varchar(20) NOT NULL COMMENT '付款类型(关联字典编码：payment_type)',
    `payment_method` varchar(50) COMMENT '付款方式(关联字典编码：payment_method)',
    `payer_partner_id` bigint COMMENT '付款方ID(关联partner表)',
    `payee_partner_id` bigint COMMENT '收款方ID(关联partner表)',
    `planned_amount` decimal(15,2) COMMENT '计划金额',
    `actual_amount` decimal(15,2) COMMENT '实际金额',
    `currency` varchar(10) DEFAULT 'CNY' COMMENT '币种',
    `planned_date` date COMMENT '计划付款日期',
    `actual_date` date COMMENT '实际付款日期',
    `payment_status` varchar(20) DEFAULT 'pending' COMMENT '付款状态(pending-待付款 paid-已付款 partial-部分付款 overdue-已逾期 cancelled-已取消)',
    `bank_name` varchar(200) COMMENT '付款银行',
    `bank_account` varchar(50) COMMENT '付款账号',
    `transaction_no` varchar(100) COMMENT '交易流水号',
    `voucher_attachment_id` varchar(500) COMMENT '付款凭证文件Id',
    `invoice_status` varchar(20) DEFAULT 'not_issued' COMMENT '发票状态(not_issued-未开票 issued-已开票 received-已收票)',
    `invoice_no` varchar(100) COMMENT '发票号码',
    `invoice_amount` decimal(15,2) COMMENT '发票金额',
    `invoice_date` date COMMENT '开票日期',
    `remark` varchar(500) COMMENT '备注',
    `create_by` bigint COMMENT '创建人ID',
    `create_time` datetime COMMENT '创建时间',
    `update_by` bigint COMMENT '修改人ID',
    `update_time` datetime COMMENT '更新时间',
    `is_deleted` tinyint DEFAULT 0 COMMENT '逻辑删除标识(1-已删除 0-未删除)',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COMMENT = '合同付款记录表';


-- ----------------------------
-- 6. 初始化字典数据
-- ----------------------------
-- 清理已存在的字典数据，实现幂等性
DELETE FROM `sys_dict_item` WHERE `dict_code` IN ('contract_type', 'contract_category', 'payment_method', 'partner_type', 'certificate_type', 'partner_role', 'partner_role_desc', 'payment_type');
DELETE FROM `sys_dict` WHERE `dict_code` IN ('contract_type', 'contract_category', 'payment_method', 'partner_type', 'certificate_type', 'partner_role', 'partner_role_desc', 'payment_type');

-- 合同类型字典
INSERT INTO `sys_dict` (`dict_code`, `name`, `status`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`, `is_deleted`)
VALUES ('contract_type', '合同类型', 1, '合同管理-合同类型', now(), 1, now(), 1, 0);

INSERT INTO `sys_dict_item` (`dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
('contract_type', 'purchase', '采购合同', 'primary', 1, 1, '采购商品或服务的合同', now(), 1, now(), 1),
('contract_type', 'sales', '销售合同', 'success', 1, 2, '销售商品或服务的合同', now(), 1, now(), 1),
('contract_type', 'service', '服务合同', 'info', 1, 3, '提供服务的合同', now(), 1, now(), 1),
('contract_type', 'lease', '租赁合同', 'warning', 1, 4, '租赁房屋、设备等的合同', now(), 1, now(), 1),
('contract_type', 'construction', '建设工程合同', 'danger', 1, 5, '建设工程相关合同', now(), 1, now(), 1),
('contract_type', 'labor', '劳务合同', '', 1, 6, '劳务服务合同', now(), 1, now(), 1),
('contract_type', 'cooperation', '合作协议', '', 1, 7, '合作相关协议', now(), 1, now(), 1),
('contract_type', 'other', '其他', '', 1, 99, '其他类型合同', now(), 1, now(), 1);

-- 合同分类字典
INSERT INTO `sys_dict` (`dict_code`, `name`, `status`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`, `is_deleted`)
VALUES ('contract_category', '合同分类', 1, '合同管理-合同分类', now(), 1, now(), 1, 0);

INSERT INTO `sys_dict_item` (`dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
('contract_category', 'internal', '内部合同', 'primary', 1, 1, '公司内部合同', now(), 1, now(), 1),
('contract_category', 'external', '外部合同', 'success', 1, 2, '与外部单位签署的合同', now(), 1, now(), 1),
('contract_category', 'framework', '框架协议', 'info', 1, 3, '框架性协议合同', now(), 1, now(), 1),
('contract_category', 'supplement', '补充协议', 'warning', 1, 4, '对原合同的补充协议', now(), 1, now(), 1);

-- 付款方式字典
INSERT INTO `sys_dict` (`dict_code`, `name`, `status`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`, `is_deleted`)
VALUES ('payment_method', '付款方式', 1, '合同管理-付款方式', now(), 1, now(), 1, 0);

INSERT INTO `sys_dict_item` (`dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
('payment_method', 'bank_transfer', '银行转账', 'primary', 1, 1, '通过银行转账付款', now(), 1, now(), 1),
('payment_method', 'cash', '现金支付', 'success', 1, 2, '现金付款', now(), 1, now(), 1),
('payment_method', 'check', '支票支付', 'info', 1, 3, '支票付款', now(), 1, now(), 1),
('payment_method', 'letter_credit', '信用证', 'warning', 1, 4, '信用证付款', now(), 1, now(), 1),
('payment_method', 'installment', '分期付款', '', 1, 5, '分期付款', now(), 1, now(), 1),
('payment_method', 'advance', '预付款', '', 1, 6, '预付款方式', now(), 1, now(), 1),
('payment_method', 'cod', '货到付款', '', 1, 7, '货到付款', now(), 1, now(), 1),
('payment_method', 'other', '其他', '', 1, 99, '其他付款方式', now(), 1, now(), 1);

-- 伙伴类型字典
INSERT INTO `sys_dict` (`dict_code`, `name`, `status`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`, `is_deleted`)
VALUES ('partner_type', '伙伴类型', 1, '合同管理-伙伴类型', now(), 1, now(), 1, 0);

INSERT INTO `sys_dict_item` (`dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
('partner_type', 'company', '企业', 'primary', 1, 1, '企业法人单位', now(), 1, now(), 1),
('partner_type', 'personal', '个人', 'success', 1, 2, '个人或个体工商户', now(), 1, now(), 1),
('partner_type', 'government', '政府机构', 'info', 1, 3, '政府部门或事业单位', now(), 1, now(), 1),
('partner_type', 'other', '其他', '', 1, 4, '其他类型伙伴', now(), 1, now(), 1);

-- 证件类型字典
INSERT INTO `sys_dict` (`dict_code`, `name`, `status`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`, `is_deleted`)
VALUES ('certificate_type', '证件类型', 1, '合同管理-证件类型', now(), 1, now(), 1, 0);

INSERT INTO `sys_dict_item` (`dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
('certificate_type', 'unified_credit', '统一社会信用代码', 'primary', 1, 1, '企业统一社会信用代码', now(), 1, now(), 1),
('certificate_type', 'id_card', '身份证', 'success', 1, 2, '个人身份证号码', now(), 1, now(), 1),
('certificate_type', 'passport', '护照', 'info', 1, 3, '护照号码', now(), 1, now(), 1),
('certificate_type', 'other', '其他', '', 1, 4, '其他类型证件', now(), 1, now(), 1);

-- 伙伴角色字典
INSERT INTO `sys_dict` (`dict_code`, `name`, `status`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`, `is_deleted`)
VALUES ('partner_role', '伙伴角色', 1, '合同管理-伙伴角色', now(), 1, now(), 1, 0);

INSERT INTO `sys_dict_item` (`dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
('partner_role', 'party_a', '甲方', 'primary', 1, 1, '合同甲方', now(), 1, now(), 1),
('partner_role', 'party_b', '乙方', 'success', 1, 2, '合同乙方', now(), 1, now(), 1),
('partner_role', 'party_c', '丙方', 'info', 1, 3, '合同丙方', now(), 1, now(), 1),
('partner_role', 'party_d', '丁方', 'warning', 1, 4, '合同丁方', now(), 1, now(), 1),
('partner_role', 'guarantor', '担保方', 'danger', 1, 5, '合同担保方', now(), 1, now(), 1),
('partner_role', 'witness', '见证方', '', 1, 6, '合同见证方', now(), 1, now(), 1);

-- 伙伴角色描述字典
INSERT INTO `sys_dict` (`dict_code`, `name`, `status`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`, `is_deleted`)
VALUES ('partner_role_desc', '伙伴角色描述', 1, '合同管理-伙伴角色描述', now(), 1, now(), 1, 0);

INSERT INTO `sys_dict_item` (`dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
('partner_role_desc', 'supplier', '供应商', 'primary', 1, 1, '提供商品或服务的供应商', now(), 1, now(), 1),
('partner_role_desc', 'purchaser', '采购方', 'success', 1, 2, '采购商品或服务的采购方', now(), 1, now(), 1),
('partner_role_desc', 'contractor', '承包商', 'info', 1, 3, '工程或服务承包商', now(), 1, now(), 1),
('partner_role_desc', 'subcontractor', '分包商', 'warning', 1, 4, '分包商或次承包商', now(), 1, now(), 1),
('partner_role_desc', 'client', '委托方', '', 1, 5, '委托方或客户', now(), 1, now(), 1),
('partner_role_desc', 'agent', '代理方', '', 1, 6, '代理方或中介方', now(), 1, now(), 1),
('partner_role_desc', 'guarantor', '担保方', 'danger', 1, 7, '提供担保的担保方', now(), 1, now(), 1),
('partner_role_desc', 'lessor', '出租方', '', 1, 8, '租赁合同出租方', now(), 1, now(), 1),
('partner_role_desc', 'lessee', '承租方', '', 1, 9, '租赁合同承租方', now(), 1, now(), 1),
('partner_role_desc', 'investor', '投资方', '', 1, 10, '投资方或出资方', now(), 1, now(), 1),
('partner_role_desc', 'business_partner', '业务伙伴', '', 1, 11, '业务合作伙伴', now(), 1, now(), 1),
('partner_role_desc', 'other', '其他', '', 1, 99, '其他角色描述', now(), 1, now(), 1);


-- 付款类型字典
INSERT INTO `sys_dict` (`dict_code`, `name`, `status`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`, `is_deleted`)
VALUES ('payment_type', '付款类型', 1, '合同管理-付款类型', now(), 1, now(), 1, 0);

INSERT INTO `sys_dict_item` (`dict_code`, `value`, `label`, `tag_type`, `status`, `sort`, `remark`, `create_time`, `create_by`, `update_time`, `update_by`) VALUES
('payment_type', 'advance', '预付款', 'primary', 1, 1, '合同签署后的预付款', now(), 1, now(), 1),
('payment_type', 'progress', '进度款', 'info', 1, 2, '按进度支付的款项', now(), 1, now(), 1),
('payment_type', 'final', '尾款', 'success', 1, 3, '合同完成后的尾款', now(), 1, now(), 1),
('payment_type', 'penalty', '违约金', 'danger', 1, 4, '违约产生的罚金', now(), 1, now(), 1),
('payment_type', 'deposit', '保证金', 'warning', 1, 5, '履约保证金', now(), 1, now(), 1),
('payment_type', 'other', '其他', '', 1, 99, '其他类型款项', now(), 1, now(), 1);

-- ----------------------------
-- 7. 初始化菜单数据
-- ----------------------------
-- 清理已存在的菜单数据，实现幂等性
DELETE FROM `sys_role_menu` WHERE `menu_id` IN (150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169);
DELETE FROM `sys_menu` WHERE `id` IN (150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169);

-- 创建 "合同管理" 顶级目录
INSERT INTO `sys_menu`(`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `create_time`, `update_time`, `params`) VALUES
(150, 0, '0', '合同管理', 2, 'ContractMgmt', '/contract', 'Layout', NULL, 1, 0, 1, 0, 'el-icon-document-copy', '/contract/contract', NOW(), NOW(), NULL);

-- 创建 "合同管理" 菜单
INSERT INTO `sys_menu`(`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `create_time`, `update_time`, `params`) VALUES
(151, 150, '0,150', '合同管理', 1, 'ContractList', 'contract', 'contract/contract/index', NULL, 0, 1, 1, 1, 'el-icon-tickets', NULL, NOW(), NOW(), NULL);

-- 创建 "合同详情" 菜单（隐藏菜单，通过路由访问）
INSERT INTO `sys_menu`(`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `create_time`, `update_time`, `params`) VALUES
(152, 150, '0,150', '合同详情', 1, 'ContractDetail', 'contract/detail/:id', 'contract/contract/detail', NULL, 0, 1, 0, 2, 'el-icon-view', NULL, NOW(), NOW(), NULL);

-- 创建 "合同编辑" 菜单（隐藏菜单，通过路由访问）
INSERT INTO `sys_menu`(`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `create_time`, `update_time`, `params`) VALUES
(153, 150, '0,150', '合同编辑', 1, 'ContractEdit', 'contract/edit/:id?', 'contract/contract/edit', NULL, 0, 1, 0, 3, 'el-icon-edit', NULL, NOW(), NOW(), NULL);

-- 创建 "合同管理" 的按钮权限
INSERT INTO `sys_menu`(`id`, `parent_id`, `tree_path`, `name`, `type`, `perm`, `sort`) VALUES
(154, 151, '0,150,151', '查询', 4, 'contract:query', 1),
(155, 151, '0,150,151', '新增', 4, 'contract:add', 2),
(156, 151, '0,150,151', '修改', 4, 'contract:edit', 3),
(157, 151, '0,150,151', '删除', 4, 'contract:delete', 4),
(158, 151, '0,150,151', '详情', 4, 'contract:detail', 5);

-- 创建 "业务伙伴" 菜单
INSERT INTO `sys_menu`(`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `create_time`, `update_time`, `params`) VALUES
(159, 1, '0,1', '业务伙伴', 1, 'Partner', 'partner', 'contract/partner/index', NULL, 0, 1, 1, 4, 'el-icon-user', NULL, NOW(), NOW(), NULL);

-- 创建 "业务伙伴" 的按钮权限
INSERT INTO `sys_menu`(`id`, `parent_id`, `tree_path`, `name`, `type`, `perm`, `sort`) VALUES
(160, 159, '0,1,159', '查询', 4, 'partner:query', 1),
(161, 159, '0,1,159', '新增', 4, 'partner:add', 2),
(162, 159, '0,1,159', '修改', 4, 'partner:edit', 3),
(163, 159, '0,1,159', '删除', 4, 'partner:delete', 4);

-- 创建 "付款记录" 菜单（隐藏菜单，通过路由访问）
INSERT INTO `sys_menu`(`id`, `parent_id`, `tree_path`, `name`, `type`, `route_name`, `route_path`, `component`, `perm`, `always_show`, `keep_alive`, `visible`, `sort`, `icon`, `redirect`, `create_time`, `update_time`, `params`) VALUES
(164, 150, '0,150', '付款记录', 1, 'ContractPayment', 'payment/:contractId', 'contract/payment/index', NULL, 0, 1, 0, 5, 'el-icon-money', NULL, NOW(), NOW(), NULL);

-- 创建 "付款记录" 的按钮权限
INSERT INTO `sys_menu`(`id`, `parent_id`, `tree_path`, `name`, `type`, `perm`, `sort`) VALUES
(165, 164, '0,150,164', '付款查询', 4, 'contract:payment:query', 1),
(166, 164, '0,150,164', '付款新增', 4, 'contract:payment:add', 2),
(167, 164, '0,150,164', '付款修改', 4, 'contract:payment:edit', 3),
(168, 164, '0,150,164', '付款删除', 4, 'contract:payment:delete', 4);

-- 为系统管理员分配菜单权限
INSERT INTO `sys_role_menu` VALUES (2, 150);
INSERT INTO `sys_role_menu` VALUES (2, 151);
INSERT INTO `sys_role_menu` VALUES (2, 152);
INSERT INTO `sys_role_menu` VALUES (2, 153);
INSERT INTO `sys_role_menu` VALUES (2, 154);
INSERT INTO `sys_role_menu` VALUES (2, 155);
INSERT INTO `sys_role_menu` VALUES (2, 156);
INSERT INTO `sys_role_menu` VALUES (2, 157);
INSERT INTO `sys_role_menu` VALUES (2, 158);
INSERT INTO `sys_role_menu` VALUES (2, 159);
INSERT INTO `sys_role_menu` VALUES (2, 160);
INSERT INTO `sys_role_menu` VALUES (2, 161);
INSERT INTO `sys_role_menu` VALUES (2, 162);
INSERT INTO `sys_role_menu` VALUES (2, 163);
INSERT INTO `sys_role_menu` VALUES (2, 164);
INSERT INTO `sys_role_menu` VALUES (2, 165);
INSERT INTO `sys_role_menu` VALUES (2, 166);
INSERT INTO `sys_role_menu` VALUES (2, 167);
INSERT INTO `sys_role_menu` VALUES (2, 168);
