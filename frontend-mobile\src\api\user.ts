import request from "@/utils/request";

/** 模块API接口前缀 */
const MODULE_API_PREFIX = "/api/v1/users";

/**
 * 用户模块的API
 */
const SystemUsersAPI = {
  /** 获取当前登录用户信息 */
  getInfo() {
    return request<null, IUserInfo>({
      url: `${MODULE_API_PREFIX}/profile`,
      method: "get",
    });
  },
};

export default SystemUsersAPI;

/** 登录用户信息 */
export interface IUserInfo {
  /**
   * 头像URL
   */
  avatar?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 部门名称
   */
  deptName?: string;
  /**
   * 邮箱
   */
  email?: string;
  /**
   * 性别
   */
  gender?: number;
  /**
   * 用户ID
   */
  id?: number;
  /**
   * 手机号
   */
  mobile?: string;
  /**
   * 用户昵称
   */
  nickname?: string;
  /**
   * 角色名称
   */
  roleNames?: string;
  /**
   * 用户名
   */
  username?: string;
}
