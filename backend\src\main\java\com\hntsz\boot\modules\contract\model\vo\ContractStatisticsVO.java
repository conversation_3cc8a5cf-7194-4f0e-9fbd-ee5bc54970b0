package com.hntsz.boot.modules.contract.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 合同统计视图对象
 */
@Getter
@Setter
@Schema(description = "合同统计视图对象")
public class ContractStatisticsVO {

    /**
     * 合同数量
     */
    @Schema(description = "合同数量")
    private Long count;

    /**
     * 合同总金额
     */
    @Schema(description = "合同总金额")
    private BigDecimal totalAmount;
}