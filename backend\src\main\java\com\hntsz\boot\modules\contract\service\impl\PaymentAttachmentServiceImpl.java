package com.hntsz.boot.modules.contract.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hntsz.boot.modules.contract.mapper.PaymentAttachmentMapper;
import com.hntsz.boot.modules.contract.model.entity.PaymentAttachment;
import com.hntsz.boot.modules.contract.model.form.PaymentAttachmentForm;
import com.hntsz.boot.modules.contract.model.vo.PaymentAttachmentVO;
import com.hntsz.boot.modules.contract.service.PaymentAttachmentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 付款文件关联服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Service
@RequiredArgsConstructor
public class PaymentAttachmentServiceImpl extends ServiceImpl<PaymentAttachmentMapper, PaymentAttachment> implements PaymentAttachmentService {

    private final PaymentAttachmentMapper paymentAttachmentMapper;

    @Override
    public List<PaymentAttachmentVO> getByPaymentId(Long paymentId) {
        return paymentAttachmentMapper.getByPaymentId(paymentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePaymentAttachment(PaymentAttachmentForm form) {
        // 检查是否已存在相同的关联
        LambdaQueryWrapper<PaymentAttachment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentAttachment::getPaymentId, form.getPaymentId())
                .eq(PaymentAttachment::getAttachmentId, form.getAttachmentId());
        
        if (this.count(queryWrapper) > 0) {
            return false; // 已存在相同关联
        }

        PaymentAttachment entity = new PaymentAttachment();
        entity.setPaymentId(form.getPaymentId());
        entity.setAttachmentId(form.getAttachmentId());
        entity.setSort(form.getSort());
        
        return this.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSavePaymentAttachments(Long paymentId, List<Long> attachmentIds) {
        for (Long attachmentId : attachmentIds) {
            PaymentAttachmentForm form = new PaymentAttachmentForm();
            form.setPaymentId(paymentId);
            form.setAttachmentId(attachmentId);
            form.setSort(attachmentIds.indexOf(attachmentId) + 1);
            
            if (!savePaymentAttachment(form)) {
                // 如果保存失败，可能是因为已存在相同关联，继续处理下一个
                continue;
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByPaymentId(Long paymentId) {
        return paymentAttachmentMapper.deleteByPaymentId(paymentId) >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByAttachmentId(Long attachmentId) {
        return paymentAttachmentMapper.deleteByAttachmentId(attachmentId) >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePaymentAttachment(Long paymentId, Long attachmentId) {
        return paymentAttachmentMapper.deletePaymentAttachment(paymentId, attachmentId) > 0;
    }
}