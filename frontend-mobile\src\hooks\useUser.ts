import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import SystemUsersAPI, { type IUserInfo } from '@/api/user';
import AuthAPI, { type LoginFormData } from '@/api/auth';
import { Auth } from '@/utils/auth';

/**
 * 用户查询键常量
 */
export const USER_QUERY_KEYS = {
  userInfo: ['user', 'info'] as const,
  profile: ['user', 'profile'] as const,
} as const;

/**
 * 获取当前用户信息的 Hook
 * @returns 用户信息查询结果
 */
export function useUserInfo() {
  return useQuery({
    queryKey: USER_QUERY_KEYS.userInfo,
    queryFn: () => SystemUsersAPI.getInfo(),
    enabled: Auth.isLoggedIn(), // 只有登录时才查询
    staleTime: 5 * 60 * 1000, // 5分钟内数据保持新鲜
    gcTime: 10 * 60 * 1000, // 10分钟缓存时间
    retry: (failureCount, error: any) => {
      // 如果是401错误，不重试
      if (error?.response?.status === 401) {
        return false;
      }
      return failureCount < 3;
    },
  });
}

/**
 * 用户登录 Hook
 * @returns 登录 mutation
 */
export function useLogin() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (loginData: LoginFormData) => AuthAPI.login(loginData),
    onSuccess: (data) => {
      const { accessToken, refreshToken } = data;
      // 保存令牌
      Auth.setTokens(accessToken, refreshToken, loginData.rememberMe);
      
      // 登录成功后立即获取用户信息
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.userInfo });
    },
  });
}

/**
 * 用户登出 Hook
 * @returns 登出 mutation
 */
export function useLogout() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: () => AuthAPI.logout(),
    onSuccess: () => {
      // 清除所有缓存
      queryClient.clear();
      // 清除本地存储的令牌
      Auth.clearTokens();
    },
  });
}

/**
 * 刷新令牌 Hook
 * @returns 刷新令牌 mutation
 */
export function useRefreshToken() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: () => {
      const refreshToken = Auth.getRefreshToken();
      if (!refreshToken) {
        throw new Error('没有有效的刷新令牌');
      }
      return AuthAPI.refreshToken(refreshToken);
    },
    onSuccess: (data) => {
      const { accessToken, refreshToken: newRefreshToken } = data;
      // 更新令牌
      Auth.setTokens(accessToken, newRefreshToken, Auth.getRememberMe());
      
      // 重新获取用户信息
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.userInfo });
    },
  });
}