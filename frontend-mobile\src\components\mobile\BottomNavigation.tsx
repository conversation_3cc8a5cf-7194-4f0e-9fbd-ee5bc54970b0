import { NavLink } from "react-router-dom";
import { 
  Home, 
  FileText, 
  Target, 
  Users, 
  User 
} from "lucide-react";

export function BottomNavigation() {
  const navItems = [
    { to: "/", icon: Home, label: "首页" },
    { to: "/opportunities", icon: Target, label: "商机" },
    { to: "/contracts", icon: FileText, label: "合同" },
    { to: "/profile", icon: User, label: "我的" },
  ];

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-card border-t border-border z-50">
      <div className="flex items-center justify-around py-2">
        {navItems.map((item) => (
          <NavLink
            key={item.to}
            to={item.to}
            className={({ isActive }) =>
              `flex flex-col items-center justify-center px-3 py-2 min-w-0 flex-1 transition-colors ${
                isActive
                  ? "text-primary"
                  : "text-muted-foreground hover:text-foreground"
              }`
            }
          >
            <item.icon className="h-5 w-5 mb-1" />
            <span className="text-xs font-medium">{item.label}</span>
          </NavLink>
        ))}
      </div>
    </nav>
  );
}