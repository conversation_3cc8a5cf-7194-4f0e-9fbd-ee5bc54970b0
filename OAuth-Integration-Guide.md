# OAuth 集成指南

本系统已集成通用的 OAuth 2.0 认证功能，支持与任何标准 OAuth 2.0 提供商进行集成。

## 功能特性

- ✅ 通用 OAuth 2.0 兼容
- ✅ 自动用户注册和账号绑定
- ✅ 安全的 state 参数验证
- ✅ 支持多字段映射（适配不同提供商的 API 响应格式）
- ✅ 与现有认证系统无缝集成

## 后端配置

### 1. 数据库设置

首先运行数据库迁移脚本添加 OAuth 字段：

```sql
-- 执行 backend/sql/mysql/oauth_complete_migration.sql
source backend/sql/mysql/oauth_complete_migration.sql;
```

### 2. 应用配置

在 `backend/src/main/resources/application-dev.yml` 中配置：

```yaml
security:
  oauth:
    client-id: ${OAUTH_CLIENT_ID:}                    # OAuth 客户端 ID
    client-secret: ${OAUTH_CLIENT_SECRET:}            # OAuth 客户端密钥
    authorization-url: ${OAUTH_AUTHORIZATION_URL:}    # 授权端点 URL
    token-url: ${OAUTH_TOKEN_URL:}                    # Token 端点 URL
    user-info-url: ${OAUTH_USER_INFO_URL:}            # 用户信息端点 URL
    redirect-uri: ${OAUTH_REDIRECT_URI:}              # 回调地址（可选）
    logout-url: ${OAUTH_LOGOUT_URL:}                  # 登出端点 URL（可选）
    scopes: ${OAUTH_SCOPES:openid profile email}     # 权限范围
    user-identifier: ${OAUTH_USER_IDENTIFIER:id}     # 用户标识符字段名
    auth-style: ${OAUTH_AUTH_STYLE:client_secret_post} # 认证方式
```

### 3. 环境变量设置

设置以下环境变量：

```bash
# 必需配置
export OAUTH_CLIENT_ID="your-oauth-client-id"
export OAUTH_CLIENT_SECRET="your-oauth-client-secret"
export OAUTH_AUTHORIZATION_URL="https://your-oauth-provider.com/oauth/authorize"
export OAUTH_TOKEN_URL="https://your-oauth-provider.com/oauth/token"
export OAUTH_USER_INFO_URL="https://your-oauth-provider.com/oauth/userinfo"

# 可选配置
export OAUTH_REDIRECT_URI="http://localhost:3000/#/auth/callback"
export OAUTH_LOGOUT_URL="https://your-oauth-provider.com/oauth/logout"
export OAUTH_SCOPES="openid profile email"
export OAUTH_USER_IDENTIFIER="id"
export OAUTH_AUTH_STYLE="client_secret_post"
```

## 前端配置

### 1. 自动重定向配置（可选）

如果需要启用自动重定向到 OAuth 登录，可以在环境变量中设置：

```bash
# 在 frontend/.env.development.local 中添加
VITE_APP_AUTO_REDIRECT_OAUTH=true
```

启用后，用户访问需要权限的页面时会自动跳转到 OAuth 登录页面。

### 2. 用户界面

系统提供了以下 OAuth 登录入口：

- **登录页面**：在主登录页面显示 OAuth 登录按钮
- **独立页面**：`/auth/login` - 简洁的 OAuth 登录加载页面
- **回调处理**：`/auth/callback` - 处理 OAuth 回调的专用页面

## 配置参数说明

### 标准 OAuth 配置映射

**重要变更**：前端配置已改为通过后端 API 动态获取，无需在前端环境变量中配置 OAuth 参数。

| 标准配置项 | 后端环境变量 | 前端获取方式 | 说明 |
|-----------|-------------|-------------|------|
| Client ID | `OAUTH_CLIENT_ID` | 通过 API 获取 | OAuth 客户端ID |
| Client Secret | `OAUTH_CLIENT_SECRET` | 仅后端 | OAuth 客户端密钥（仅后端） |
| Authorization URL | `OAUTH_AUTHORIZATION_URL` | 通过 API 获取 | OAuth 授权端点 |
| Access Token URL | `OAUTH_TOKEN_URL` | 仅后端 | OAuth Token 端点（仅后端） |
| Resource URL | `OAUTH_USER_INFO_URL` | 仅后端 | 用户信息端点（仅后端） |
| Redirect URL | `OAUTH_REDIRECT_URI` | 通过 API 获取 | OAuth 回调地址 |
| Logout URL | `OAUTH_LOGOUT_URL` | 通过 API 获取 | OAuth 登出端点 |
| User Identifier | `OAUTH_USER_IDENTIFIER` | 仅后端 | 用户ID字段名（仅后端） |
| Scopes | `OAUTH_SCOPES` | 通过 API 获取 | OAuth 权限范围 |
| Auth Style | `OAUTH_AUTH_STYLE` | 仅后端 | 客户端认证方式（仅后端） |

### 认证方式说明

- **client_secret_post**（默认）：客户端凭证在请求体中发送
- **client_secret_basic**：客户端凭证通过HTTP Basic认证发送

## OAuth 提供商配置示例

### GitHub OAuth

```bash
# 后端环境变量（所有配置都在后端）
OAUTH_CLIENT_ID="your-github-client-id"
OAUTH_CLIENT_SECRET="your-github-client-secret"
OAUTH_AUTHORIZATION_URL="https://github.com/login/oauth/authorize"
OAUTH_TOKEN_URL="https://github.com/login/oauth/access_token"
OAUTH_USER_INFO_URL="https://api.github.com/user"
OAUTH_REDIRECT_URI="http://localhost:3000/#/auth/callback"
OAUTH_SCOPES="user:email"
OAUTH_USER_IDENTIFIER="id"
OAUTH_AUTH_STYLE="client_secret_post"
```

### Google OAuth

```bash
# 后端环境变量（所有配置都在后端）
OAUTH_CLIENT_ID="your-google-client-id"
OAUTH_CLIENT_SECRET="your-google-client-secret"
OAUTH_AUTHORIZATION_URL="https://accounts.google.com/o/oauth2/v2/auth"
OAUTH_TOKEN_URL="https://oauth2.googleapis.com/token"
OAUTH_USER_INFO_URL="https://www.googleapis.com/oauth2/v2/userinfo"
OAUTH_REDIRECT_URI="http://localhost:3000/#/auth/callback"
OAUTH_SCOPES="openid profile email"
OAUTH_USER_IDENTIFIER="id"
OAUTH_AUTH_STYLE="client_secret_post"
```

### GitLab OAuth

```bash
# 后端环境变量（所有配置都在后端）
OAUTH_CLIENT_ID="your-gitlab-client-id"
OAUTH_CLIENT_SECRET="your-gitlab-client-secret"
OAUTH_AUTHORIZATION_URL="https://gitlab.com/oauth/authorize"
OAUTH_TOKEN_URL="https://gitlab.com/oauth/token"
OAUTH_USER_INFO_URL="https://gitlab.com/api/v4/user"
OAUTH_REDIRECT_URI="http://localhost:3000/#/auth/callback"
OAUTH_SCOPES="read_user"
OAUTH_USER_IDENTIFIER="id"
OAUTH_AUTH_STYLE="client_secret_post"
```

## OAuth 流程说明

### 1. 用户授权流程

1. 用户在登录页面点击 OAuth 登录按钮，或直接访问 `/auth/login`
2. 系统从后端 API 获取 OAuth 配置信息
3. 系统生成 `state` 参数并自动跳转到 OAuth 提供商授权页面
4. 用户在 OAuth 提供商页面进行授权
5. 授权成功后跳转回系统回调地址 `/auth/callback`
6. OAuth 回调页面验证 `state` 参数并获取授权码
7. 系统调用后端API，用授权码换取访问令牌
8. 后端用访问令牌获取用户信息并存储 OAuth 令牌
9. 后端自动注册或绑定用户账号
10. 前端接收登录结果并跳转到首页

### 2. 用户账号绑定策略

- **新用户**：自动创建账号，用户名格式为 `{provider}_{oauth_username}`
- **已有用户**：根据邮箱自动绑定到现有账号
- **权限分配**：新注册用户默认分配普通用户角色

### 3. 用户信息字段映射

系统支持多种用户信息字段格式，自动适配不同 OAuth 提供商：

| 系统字段 | 支持的 API 字段 |
|----------|----------------|
| 用户ID   | `id`, `sub`, `user_id`, `uid` |
| 用户名   | `login`, `username`, `preferred_username`, `email` |
| 昵称     | `name`, `display_name`, `nickname`, `full_name` |
| 邮箱     | `email`, `mail`, `email_address` |
| 头像     | `avatar_url`, `picture`, `avatar`, `photo` |

## API 端点

### OAuth 配置获取

```http
GET /api/v1/auth/oauth/config
```

**响应：**
```json
{
  "code": "00000",
  "data": {
    "clientId": "your-oauth-client-id",
    "authorizationUrl": "https://oauth-provider.com/oauth/authorize",
    "redirectUri": "http://localhost:3000/#/auth/callback",
    "scopes": "openid profile email",
    "logoutUrl": "https://oauth-provider.com/oauth/logout",
    "enabled": true
  }
}
```

### OAuth 登录

```http
POST /api/v1/auth/oauth/login
Content-Type: multipart/form-data

provider=oauth&code=authorization_code
```

**响应：**
```json
{
  "code": "00000",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 7200
  }
}
```

## 测试步骤

### 1. 配置测试环境

1. 在 OAuth 提供商创建应用并获取 `client_id` 和 `client_secret`
2. 配置回调地址为 `http://localhost:3000/auth/callback`
3. 设置环境变量
4. 启动后端和前端服务

### 2. 测试登录流程

方式一：通过主登录页面
1. 访问登录页面：`http://localhost:3000/login`
2. 点击 OAuth 登录按钮
3. 完成 OAuth 提供商的授权流程
4. 验证是否成功跳转回系统并登录

方式二：直接访问 OAuth 登录页面
1. 访问 OAuth 登录页面：`http://localhost:3000/auth/login`
2. 系统自动跳转到 OAuth 提供商授权页面
3. 完成授权流程
4. 验证是否成功跳转回系统并登录

### 3. 验证数据

1. 检查数据库中是否创建了新用户记录
2. 验证 `oauth_provider` 和 `oauth_id` 字段是否正确
3. 确认用户权限和角色分配
4. 检查 `oauth_token` 表中是否正确存储了 OAuth 令牌信息

## 已知问题和注意事项

### 需要注意的配置项

1. **默认角色和部门配置**
   - 在 `UserServiceImpl.registerOrBindOAuthUser()` 方法中
   - 通过配置文件设置默认部门ID和角色ID
   - **建议**：根据实际数据库配置调整这些ID

2. **Vue Router Hash 模式**
   - 当前项目使用 hash 模式路由
   - OAuth 回调地址需要包含 `#` 符号：`http://localhost:3000/#/auth/callback`
   - 如果切换到 history 模式，需要相应调整回调地址

3. **错误处理**
   - OAuth 登录失败时会自动跳转回登录页面
   - 建议在生产环境中添加更详细的错误日志

## 故障排除

### 常见问题

1. **"OAuth 配置不完整"**
   - 检查后端环境变量是否正确设置
   - 确认 `client-id`, `client-secret`, `token-url`, `user-info-url` 都已配置

2. **"OAuth 访问令牌获取失败"**
   - 检查 `token-url` 是否正确
   - 验证 `client-id` 和 `client-secret` 是否有效
   - 确认 `redirect-uri` 与 OAuth 提供商配置一致

3. **"OAuth 用户信息获取失败"**
   - 检查 `user-info-url` 是否正确
   - 确认访问令牌是否有读取用户信息的权限

4. **"无效的 state 参数"**
   - 确认浏览器未禁用 sessionStorage
   - 检查是否存在跨域问题

### 调试模式

启用后端日志调试：

```yaml
logging:
  level:
    com.hntsz.boot.core.security.extension.oauth: DEBUG
```

查看详细的 OAuth 交互日志。

## 安全注意事项

1. **保护客户端密钥**：确保 `client-secret` 不被泄露
2. **验证回调地址**：严格控制 OAuth 提供商的回调地址配置
3. **State 参数验证**：系统已实现 state 参数验证防止 CSRF 攻击
4. **HTTPS 环境**：生产环境建议使用 HTTPS
5. **权限控制**：根据需要调整 OAuth 用户的默认权限

## 数据库结构

### sys_user 表扩展字段

```sql
-- OAuth 提供商类型
oauth_provider varchar(32) COMMENT 'OAuth 提供商类型 (google, github, gitlab, etc.)'

-- OAuth 提供商用户ID
oauth_id varchar(128) COMMENT 'OAuth 提供商用户ID'
```

### oauth_token 表结构

```sql
CREATE TABLE `oauth_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `provider` varchar(32) NOT NULL COMMENT 'OAuth 提供商',
  `oauth_id` varchar(128) NOT NULL COMMENT 'OAuth 用户ID',
  `access_token` text NOT NULL COMMENT '访问令牌',
  `token_type` varchar(32) DEFAULT 'Bearer' COMMENT '令牌类型',
  `expires_in` int(11) DEFAULT NULL COMMENT '过期时间（秒）',
  `refresh_token` text COMMENT '刷新令牌',
  `scope` varchar(255) COMMENT '权限范围',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_provider` (`user_id`, `provider`),
  KEY `idx_provider_oauth_id` (`provider`, `oauth_id`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OAuth 令牌表';
```

## 组件结构

### 前端组件

- **OAuthLoginButton.vue**：OAuth 登录按钮组件，用于主登录页面
- **OAuthLogin.vue**：独立的 OAuth 登录页面，显示加载状态并自动跳转
- **OAuthCallback.vue**：OAuth 回调处理页面，处理授权码并完成登录

### 后端组件

- **OAuthAuthenticationProvider**：OAuth 认证提供者
- **OAuthUserInfoService**：OAuth 用户信息服务接口
- **DefaultOAuthUserInfoService**：默认的 OAuth 用户信息服务实现
- **AuthController**：认证控制器，提供 OAuth 配置和登录端点
- **OAuthConfig**：OAuth 配置数据传输对象
- **OAuthToken**：OAuth 令牌实体类

## 扩展开发

如需支持特定 OAuth 提供商的特殊功能，可以：

1. 继承 `OAuthUserInfoService` 接口
2. 实现特定提供商的用户信息解析逻辑
3. 在 Spring 配置中注册自定义服务

系统设计具有良好的扩展性，可以轻松适配各种 OAuth 2.0 提供商。