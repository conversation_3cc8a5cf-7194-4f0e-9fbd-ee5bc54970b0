package com.hntsz.boot.modules.contract.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hntsz.boot.modules.contract.model.entity.ContractAttachment;
import com.hntsz.boot.modules.contract.model.form.ContractAttachmentForm;
import com.hntsz.boot.modules.contract.model.vo.ContractAttachmentVO;

import java.util.List;

/**
 * 合同文件关联Service接口
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface ContractAttachmentService extends IService<ContractAttachment> {

    /**
     * 根据合同ID获取文件列表
     *
     * @param contractId 合同ID
     * @return 文件列表
     */
    List<ContractAttachmentVO> getByContractId(Long contractId);

    /**
     * 保存合同文件关联
     *
     * @param form 表单数据
     * @return 是否成功
     */
    boolean saveContractAttachment(ContractAttachmentForm form);

    /**
     * 批量保存合同文件关联
     *
     * @param contractId 合同ID
     * @param attachmentIds 附件ID列表
     * @return 是否成功
     */
    boolean batchSaveContractAttachments(Long contractId, List<Long> attachmentIds);

    /**
     * 根据合同ID删除文件关联
     *
     * @param contractId 合同ID
     * @return 是否成功
     */
    boolean deleteByContractId(Long contractId);

    /**
     * 根据附件ID删除文件关联
     *
     * @param attachmentId 附件ID
     * @return 是否成功
     */
    boolean deleteByAttachmentId(Long attachmentId);

    /**
     * 删除合同文件关联
     *
     * @param contractId 合同ID
     * @param attachmentId 附件ID
     * @return 是否成功
     */
    boolean deleteContractAttachment(Long contractId, Long attachmentId);
}
