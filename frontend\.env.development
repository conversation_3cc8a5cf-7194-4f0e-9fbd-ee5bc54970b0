# 应用端口
VITE_APP_PORT=3000

# 代理前缀
VITE_APP_BASE_API=/dev-api

# 接口地址
# VITE_APP_API_URL=https://api.hntsz.com # 线上
VITE_APP_API_URL=http://localhost:8989    # 本地

# WebSocket 端点（不配置则关闭），线上 ws://api.hntsz.com/ws ，本地 ws://localhost:8989/ws
VITE_APP_WS_ENDPOINT=

# 启用 Mock 服务
VITE_MOCK_DEV_SERVER=false

# 文件地址
VITE_APP_FILE_BASE_URL=https://admin.staging.hntsz.com/upload
VITE_APP_FILE_ENCODE=false

# 自动转到 OAuth
VITE_APP_AUTO_REDIRECT_OAUTH=false