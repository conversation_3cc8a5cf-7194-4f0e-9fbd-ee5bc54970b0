import { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { MobileHeader } from "@/components/mobile/MobileHeader";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  Target,
  Building,
  User,
  Phone,
  Mail,
  DollarSign,
  Calendar,
  TrendingUp,
  Clock,
  MessageSquare,
  FileText,
  Tag,
} from "lucide-react";
import BizOpportunitiesAPI, { IOpportunity } from "@/api/biz-opportunities";
import BizOpportunityFollowUpAPI, {
  IOpportunityFollowDetail,
} from "@/api/biz-opportunity-follow-up";

export default function OpportunityDetailPage() {
  const { id } = useParams();
  const navigate = useNavigate();

  // 商机详情
  const [opportunityDetail, setOpportunityDetail] = useState<IOpportunity>();
  const fetchOpportunityDetail = async () => {
    try {
      const res = await BizOpportunitiesAPI.getOpportunitiesDetail(
        parseInt(id)
      );
      setOpportunityDetail(res);
    } catch (error) {
      console.error("获取商机详情失败:", error);
    }
  };

  // 商机跟进列表
  const [followRecords, setFollowRecords] = useState<
    IOpportunityFollowDetail[]
  >([]);
  const fetchFollowRecords = async () => {
    try {
      const res = await BizOpportunityFollowUpAPI.getUpOpportunityFollowUpList({
        id,
      });
      setFollowRecords(res);
    } catch (error) {
      console.error("获取商机跟进记录失败:", error);
    }
  };
  useEffect(() => {
    fetchOpportunityDetail();
    fetchFollowRecords();
  }, [id]);

  const getStageColor = (stage: string) => {
    switch (stage) {
      case "initial":
        return "secondary";
      case "interested":
        return "warning";
      case "proposal":
        return "accent";
      case "negotiation":
        return "primary";
      case "closed_won":
        return "success";
      case "closed_lost":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "destructive";
      case "medium":
        return "warning";
      case "low":
        return "secondary";
      default:
        return "secondary";
    }
  };

  const getFollowTypeColor = (type: string) => {
    switch (type) {
      case "phone":
        return "primary";
      case "email":
        return "success";
      case "visit":
        return "info";
      case "wechat":
        return "warning";
      case "meeting":
        return "accent";
      default:
        return "secondary";
    }
  };

  const getFollowResultColor = (result: string) => {
    switch (result) {
      case "positive":
        return "success";
      case "interested":
        return "primary";
      case "considering":
        return "info";
      case "need_more_info":
        return "warning";
      case "price_concern":
        return "destructive";
      case "no_response":
        return "secondary";
      case "rejected":
        return "destructive";
      default:
        return "secondary";
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <MobileHeader title="商机详情" showBack onBack={() => navigate(-1)} />
      {opportunityDetail && (
        <div className="p-4 space-y-4">
          {/* 商机基本信息卡片 */}

          <div className="bg-card rounded-lg p-4 shadow-soft">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <Target className="h-5 w-5 text-primary" />
                  <h1 className="text-lg font-bold">
                    {opportunityDetail.opportunityName}
                  </h1>
                </div>
                <p className="text-sm text-muted-foreground mb-2">
                  编号: {opportunityDetail.opportunityCode}
                </p>
                <div className="flex items-center gap-2 mb-3">
                  <Badge
                    variant={
                      getStageColor(opportunityDetail.opportunityStage) as any
                    }
                  >
                    {opportunityDetail.opportunityStageLabel}
                  </Badge>
                  <Badge
                    variant={
                      getPriorityColor(opportunityDetail.priority) as any
                    }
                  >
                    {opportunityDetail.priorityLabel}优先级
                  </Badge>
                  <Badge variant="outline">
                    {opportunityDetail.opportunityStatusLabel}
                  </Badge>
                </div>
              </div>
            </div>

            {/* 成单概率 */}
            <div className="mb-4">
              <div className="flex items-center justify-between text-sm mb-2">
                <span className="flex items-center gap-1 text-muted-foreground">
                  <TrendingUp className="h-4 w-4" />
                  成单概率
                </span>
                <span className="font-bold text-lg">
                  {opportunityDetail.winProbability}%
                </span>
              </div>
              <Progress
                value={opportunityDetail.winProbability}
                className="h-3"
              />
            </div>

            {/* 金额和时间 */}
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground">预估金额</p>
                  <p className="font-bold text-primary">
                    {opportunityDetail.estimatedAmount}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-xs text-muted-foreground">预计成交</p>
                  <p className="font-medium">
                    {opportunityDetail.estimatedCloseDate}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 客户信息卡片 */}
          <div className="bg-card rounded-lg p-4 shadow-soft">
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <Building className="h-4 w-4" />
              客户信息
            </h3>
            <div className="space-y-3">
              <div>
                <p className="text-sm text-muted-foreground">客户名称</p>
                <p className="font-medium">{opportunityDetail.partnerName}</p>
              </div>
              <div className="grid grid-cols-1 gap-3">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-xs text-muted-foreground">联系人</p>
                    <p className="font-medium">
                      {opportunityDetail.contactPerson}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-xs text-muted-foreground">联系电话</p>
                    <p className="font-medium">
                      {opportunityDetail.contactPhone}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-xs text-muted-foreground">邮箱</p>
                    <p className="font-medium">
                      {opportunityDetail.contactEmail}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 标签页内容 */}
          <Tabs defaultValue="details" className="space-y-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="details">详细信息</TabsTrigger>
              <TabsTrigger value="follow">跟进记录</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-4">
              {/* 商机详情 */}
              <div className="bg-card rounded-lg p-4 shadow-soft">
                <h3 className="font-semibold mb-3">商机详情</h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-muted-foreground">商机类型</p>
                    <p className="font-medium">
                      {opportunityDetail.opportunityTypeLabel}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">商机来源</p>
                    <p className="font-medium">
                      {opportunityDetail.opportunitySourceLabel}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      感兴趣的产品/服务
                    </p>
                    <p className="font-medium">
                      {opportunityDetail.productInterest}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">客户需求</p>
                    <p className="font-medium text-sm leading-relaxed">
                      {opportunityDetail.requirements}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      竞争对手信息
                    </p>
                    <p className="font-medium">
                      {opportunityDetail.competitionInfo}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">下一步行动</p>
                    <p className="font-medium">
                      {opportunityDetail.nextAction}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      下次跟进日期
                    </p>
                    <p className="font-medium">
                      {opportunityDetail.nextFollowDate}
                    </p>
                  </div>
                  {opportunityDetail.tags && (
                    <div>
                      <p className="text-sm text-muted-foreground mb-2">标签</p>
                      <div className="flex flex-wrap gap-1">
                        {opportunityDetail.tags.split(",").map((tag, index) => (
                          <Badge
                            key={index}
                            variant="outline"
                            className="text-xs"
                          >
                            <Tag className="h-3 w-3 mr-1" />
                            {tag.trim()}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  {opportunityDetail.remark && (
                    <div>
                      <p className="text-sm text-muted-foreground">备注</p>
                      <p className="font-medium text-sm leading-relaxed">
                        {opportunityDetail.remark}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* 负责人信息 */}
              <div className="bg-card rounded-lg p-4 shadow-soft">
                <h3 className="font-semibold mb-3">负责人信息</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">负责人</p>
                    <p className="font-medium">
                      {opportunityDetail.responsibleUserName}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">所属部门</p>
                    <p className="font-medium">{opportunityDetail.deptName}</p>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="follow" className="space-y-4">
              {/* 跟进记录列表 */}
              <div className="space-y-3">
                {followRecords.map((record) => (
                  <div
                    key={record.id}
                    className="bg-card rounded-lg p-4 shadow-soft"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={getFollowTypeColor(record.followType) as any}
                          className="text-xs"
                        >
                          {record.followTypeLabel}
                        </Badge>
                        <Badge
                          variant={
                            getFollowResultColor(record.followResult) as any
                          }
                          className="text-xs"
                        >
                          {record.followResultLabel}
                        </Badge>
                      </div>
                      <div className="text-right">
                        <p className="text-xs text-muted-foreground">
                          {record.followDate}
                        </p>
                        {record.followDuration && (
                          <p className="text-xs text-muted-foreground">
                            <Clock className="h-3 w-3 inline mr-1" />
                            {record.followDuration}分钟
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <User className="h-3 w-3 text-muted-foreground" />
                        <span className="text-muted-foreground">
                          联系人: {record.contactPerson}
                        </span>
                        <span className="text-muted-foreground">
                          跟进人: {record.followUserName}
                        </span>
                      </div>

                      <div>
                        <p className="text-sm font-medium mb-1">跟进内容：</p>
                        <p className="text-sm text-muted-foreground leading-relaxed">
                          {record.followContent}
                        </p>
                      </div>

                      {record.nextAction && (
                        <div>
                          <p className="text-sm font-medium mb-1">
                            下一步行动：
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {record.nextAction}
                          </p>
                        </div>
                      )}

                      {record.nextFollowDate && (
                        <div className="flex items-center gap-1 text-sm">
                          <Calendar className="h-3 w-3 text-muted-foreground" />
                          <span className="text-muted-foreground">
                            下次跟进: {record.nextFollowDate}
                          </span>
                        </div>
                      )}

                      {record.remark && (
                        <div className="pt-2 border-t border-border">
                          <p className="text-xs text-muted-foreground">
                            {record.remark}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
}
