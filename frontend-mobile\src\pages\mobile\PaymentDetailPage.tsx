import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { MobileHeader } from "@/components/mobile/MobileHeader";
import { Badge } from "@/components/ui/badge";
import { 
  Receipt, 
  Building,
  DollarSign,
  Calendar,
  CreditCard,
  FileText,
  Users,
  Briefcase,
  ArrowRightLeft,
  CheckCircle,
  AlertCircle,
  Clock,
  XCircle,
  Banknote
} from "lucide-react";

export default function PaymentDetailPage() {
  const { contractId, paymentId } = useParams();
  const navigate = useNavigate();
  
  // 模拟付款记录详情数据（基于SQL样本数据）
  const paymentDetail = {
    id: parseInt(paymentId || "1"),
    contractId: parseInt(contractId || "1"),
    contractName: "智慧城市项目一期软件销售合同",
    contractNo: "CON-SAL-2023-005",
    paymentNo: "PAY-********-001",
    paymentType: "advance",
    paymentTypeText: "预付款",
    paymentMethod: "bank_transfer",
    paymentMethodText: "银行转账",
    payerPartnerName: "北京卓越客户集团",
    payeePartnerName: "我司信息技术有限公司",
    plannedAmount: "¥600,000.00",
    actualAmount: "¥600,000.00",
    currency: "CNY",
    currencyText: "人民币",
    plannedDate: "2023-03-25",
    actualDate: "2023-03-26",
    paymentStatus: "paid",
    paymentStatusText: "已付款",
    bankName: "中国工商银行北京朝阳支行",
    bankAccount: "0200003409008888888",
    transactionNo: "20230326001234567890",
    voucherAttachmentId: "123,456",
    invoiceStatus: "issued",
    invoiceStatusText: "已开票",
    invoiceNo: "INV-BJ-********",
    invoiceAmount: "¥600,000.00",
    invoiceDate: "2023-03-27",
    remark: "首付款，合同签署后按时支付",
    createTime: "2023-03-20 10:30:00",
    updateTime: "2023-03-26 15:20:00"
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "pending": return "warning";
      case "paid": return "success";
      case "partial": return "info";
      case "overdue": return "destructive";
      case "cancelled": return "secondary";
      default: return "secondary";
    }
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case "pending": return <Clock className="h-4 w-4" />;
      case "paid": return <CheckCircle className="h-4 w-4" />;
      case "partial": return <AlertCircle className="h-4 w-4" />;
      case "overdue": return <AlertCircle className="h-4 w-4" />;
      case "cancelled": return <XCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getInvoiceStatusColor = (status: string) => {
    switch (status) {
      case "not_issued": return "secondary";
      case "issued": return "success";
      case "received": return "primary";
      default: return "secondary";
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <MobileHeader 
        title="付款记录详情" 
        showBack 
        onBack={() => navigate(-1)}
      />
      
      <div className="p-4 space-y-4">
        {/* 付款基本信息卡片 */}
        <div className="bg-card rounded-lg p-4 shadow-soft">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Receipt className="h-5 w-5 text-primary" />
                <h1 className="text-lg font-bold">{paymentDetail.paymentNo}</h1>
              </div>
              <p className="text-sm text-muted-foreground mb-2">
                付款类型: {paymentDetail.paymentTypeText}
              </p>
              <div className="flex items-center gap-2 mb-3">
                <Badge variant={getPaymentStatusColor(paymentDetail.paymentStatus) as any} className="flex items-center gap-1">
                  {getPaymentStatusIcon(paymentDetail.paymentStatus)}
                  {paymentDetail.paymentStatusText}
                </Badge>
                <Badge variant={getInvoiceStatusColor(paymentDetail.invoiceStatus) as any}>
                  {paymentDetail.invoiceStatusText}
                </Badge>
                <Badge variant="outline">
                  {paymentDetail.paymentMethodText}
                </Badge>
              </div>
            </div>
          </div>

          {/* 付款金额 */}
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <DollarSign className="h-5 w-5 text-success" />
              <span className="text-sm text-muted-foreground">实际金额</span>
            </div>
            <p className="text-2xl font-bold text-success">{paymentDetail.actualAmount}</p>
            <p className="text-sm text-muted-foreground">计划金额: {paymentDetail.plannedAmount}</p>
          </div>

          {/* 时间信息 */}
          <div className="grid grid-cols-1 gap-3">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div className="flex-1">
                <p className="text-xs text-muted-foreground">计划日期</p>
                <p className="font-medium">{paymentDetail.plannedDate}</p>
              </div>
            </div>
            {paymentDetail.actualDate && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div className="flex-1">
                  <p className="text-xs text-muted-foreground">实际日期</p>
                  <p className="font-medium">{paymentDetail.actualDate}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 关联合同信息 */}
        <div className="bg-card rounded-lg p-4 shadow-soft">
          <h3 className="font-semibold mb-3 flex items-center gap-2">
            <Briefcase className="h-4 w-4" />
            关联合同
          </h3>
          <div 
            className="p-3 bg-primary/5 rounded-lg cursor-pointer hover:bg-primary/10 transition-colors"
            onClick={() => navigate(`/contracts/${paymentDetail.contractId}`)}
          >
            <p className="font-medium text-primary">{paymentDetail.contractName}</p>
            <p className="text-xs text-muted-foreground">合同编号: {paymentDetail.contractNo}</p>
            <p className="text-xs text-muted-foreground">点击查看合同详情</p>
          </div>
        </div>

        {/* 付款双方信息 */}
        <div className="bg-card rounded-lg p-4 shadow-soft">
          <h3 className="font-semibold mb-3 flex items-center gap-2">
            <ArrowRightLeft className="h-4 w-4" />
            付款双方
          </h3>
          <div className="space-y-3">
            <div className="flex items-center gap-3 p-3 bg-destructive/5 rounded-lg">
              <div className="w-10 h-10 bg-destructive/10 rounded-full flex items-center justify-center">
                <Building className="h-5 w-5 text-destructive" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-muted-foreground">付款方</p>
                <p className="font-medium">{paymentDetail.payerPartnerName}</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-success/5 rounded-lg">
              <div className="w-10 h-10 bg-success/10 rounded-full flex items-center justify-center">
                <Building className="h-5 w-5 text-success" />
              </div>
              <div className="flex-1">
                <p className="text-sm text-muted-foreground">收款方</p>
                <p className="font-medium">{paymentDetail.payeePartnerName}</p>
              </div>
            </div>
          </div>
        </div>

        {/* 付款详情 */}
        <div className="bg-card rounded-lg p-4 shadow-soft">
          <h3 className="font-semibold mb-3">付款详情</h3>
          <div className="space-y-3">
            <div>
              <p className="text-sm text-muted-foreground">币种</p>
              <p className="font-medium">{paymentDetail.currencyText} ({paymentDetail.currency})</p>
            </div>
            
            {paymentDetail.bankName && (
              <div>
                <p className="text-sm text-muted-foreground">付款银行</p>
                <p className="font-medium">{paymentDetail.bankName}</p>
              </div>
            )}
            
            {paymentDetail.bankAccount && (
              <div>
                <p className="text-sm text-muted-foreground">付款账号</p>
                <p className="font-medium font-mono">{paymentDetail.bankAccount}</p>
              </div>
            )}
            
            {paymentDetail.transactionNo && (
              <div>
                <p className="text-sm text-muted-foreground">交易流水号</p>
                <p className="font-medium font-mono">{paymentDetail.transactionNo}</p>
              </div>
            )}
          </div>
        </div>

        {/* 发票信息 */}
        {paymentDetail.invoiceStatus !== "not_issued" && (
          <div className="bg-card rounded-lg p-4 shadow-soft">
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <FileText className="h-4 w-4" />
              发票信息
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">发票状态:</span>
                <Badge variant={getInvoiceStatusColor(paymentDetail.invoiceStatus) as any}>
                  {paymentDetail.invoiceStatusText}
                </Badge>
              </div>
              
              {paymentDetail.invoiceNo && (
                <div>
                  <p className="text-sm text-muted-foreground">发票号码</p>
                  <p className="font-medium font-mono">{paymentDetail.invoiceNo}</p>
                </div>
              )}
              
              {paymentDetail.invoiceAmount && (
                <div>
                  <p className="text-sm text-muted-foreground">发票金额</p>
                  <p className="font-medium text-success">{paymentDetail.invoiceAmount}</p>
                </div>
              )}
              
              {paymentDetail.invoiceDate && (
                <div>
                  <p className="text-sm text-muted-foreground">开票日期</p>
                  <p className="font-medium">{paymentDetail.invoiceDate}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 附件信息 */}
        {paymentDetail.voucherAttachmentId && (
          <div className="bg-card rounded-lg p-4 shadow-soft">
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <FileText className="h-4 w-4" />
              付款凭证
            </h3>
            <div className="space-y-2">
              {paymentDetail.voucherAttachmentId.split(',').map((id, index) => (
                <div 
                  key={id} 
                  className="flex items-center gap-2 p-2 bg-muted/50 rounded-lg cursor-pointer hover:bg-muted transition-colors"
                >
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">付款凭证{index + 1}.pdf</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 备注信息 */}
        {paymentDetail.remark && (
          <div className="bg-card rounded-lg p-4 shadow-soft">
            <h3 className="font-semibold mb-3">备注信息</h3>
            <p className="text-sm text-muted-foreground leading-relaxed">
              {paymentDetail.remark}
            </p>
          </div>
        )}

        {/* 记录信息 */}
        <div className="bg-card rounded-lg p-4 shadow-soft">
          <h3 className="font-semibold mb-3">记录信息</h3>
          <div className="grid grid-cols-1 gap-3">
            <div>
              <p className="text-sm text-muted-foreground">创建时间</p>
              <p className="font-medium">{paymentDetail.createTime}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">更新时间</p>
              <p className="font-medium">{paymentDetail.updateTime}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}