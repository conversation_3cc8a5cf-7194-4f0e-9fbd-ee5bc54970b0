import { useEffect } from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useUserStore } from "@/store";

// 页面组件导入
import MobileIndex from "@/pages/mobile/MobileIndex";
import LoginPage from "@/pages/mobile/LoginPage";
import ContractsPage from "@/pages/mobile/ContractsPage";
import ContractDetailPage from "@/pages/mobile/ContractDetailPage";
import PaymentDetailPage from "@/pages/mobile/PaymentDetailPage";
import OpportunitiesPage from "@/pages/mobile/OpportunitiesPage";
import OpportunityDetailPage from "@/pages/mobile/OpportunityDetailPage";
import PartnersPage from "@/pages/mobile/PartnersPage";
import PartnerDetailPage from "@/pages/mobile/PartnerDetailPage";
import ProfilePage from "@/pages/mobile/ProfilePage";
// 导入新的页面组件
import OAuthLoginPage from "@/pages/auth/OAuthLoginPage";
import OAuthCallbackPage from "@/pages/auth/OAuthCallbackPage";

import NotFound from "./pages/NotFound";

// 创建QueryClient实例
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5分钟
    },
  },
});

/**
 * 应用初始化组件
 * 负责检查登录状态和初始化用户信息
 */
function AppInitializer({ children }: { children: React.ReactNode }) {
  const { checkLoginStatus } = useUserStore();

  useEffect(() => {
    // 应用启动时检查登录状态
    checkLoginStatus();
  }, [checkLoginStatus]);

  return <>{children}</>;
}

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AppInitializer>
          <Routes>
            <Route path="/" element={<MobileIndex />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/contracts" element={<ContractsPage />} />
            <Route path="/contracts/:id" element={<ContractDetailPage />} />
            <Route
              path="/contracts/:contractId/payments/:paymentId"
              element={<PaymentDetailPage />}
            />
            <Route path="/opportunities" element={<OpportunitiesPage />} />
            <Route
              path="/opportunities/:id"
              element={<OpportunityDetailPage />}
            />
            <Route path="/partners" element={<PartnersPage />} />
            <Route path="/partners/:id" element={<PartnerDetailPage />} />
            <Route path="/profile" element={<ProfilePage />} />
            <Route path="/auth/login" element={<OAuthLoginPage />} />
            <Route path="/auth/callback" element={<OAuthCallbackPage />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />{" "}
          </Routes>
        </AppInitializer>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
