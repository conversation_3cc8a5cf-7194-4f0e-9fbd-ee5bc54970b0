<template>
  <el-drawer
    v-model="visible"
    :title="drawerTitle"
    direction="rtl"
    size="900px"
    @close="handleClose"
  >
    <el-form
      ref="dataFormRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="contract-form"
    >
      <!-- 基本信息 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>基本信息</h3>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合同编号" prop="contractNo">
              <el-input v-model="formData.contractNo" placeholder="请输入合同编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同名称" prop="contractName">
              <el-input v-model="formData.contractName" placeholder="请输入合同名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合同类型" prop="contractType">
              <el-select v-model="formData.contractType" placeholder="请选择合同类型" style="width: 100%">
                <el-option
                  v-for="item in contractTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同分类" prop="contractCategory">
              <el-select v-model="formData.contractCategory" placeholder="请选择合同分类" style="width: 100%">
                <el-option
                  v-for="item in contractCategoryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合同金额" prop="contractAmount">
              <el-input-number
                v-model="formData.contractAmount"
                :precision="2"
                :min="0"
                style="width: 100%"
                placeholder="请输入合同金额"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联商机" prop="opportunityId">
              <el-select
                v-model="formData.opportunityId"
                placeholder="请选择关联商机（可选）"
                style="width: 100%"
                filterable
                clearable
              >
                <el-option
                  v-for="item in opportunityOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="付款方式" prop="paymentMethod">
              <el-select v-model="formData.paymentMethod" placeholder="请选择付款方式" style="width: 100%">
                <el-option
                  v-for="item in paymentMethodOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="responsibleUserId">
              <el-select
                v-model="formData.responsibleUserId"
                placeholder="请选择负责人"
                style="width: 100%"
                filterable
                clearable
              >
                <el-option
                  v-for="item in userOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="签署日期" prop="signingDate">
              <el-date-picker
                v-model="formData.signingDate"
                type="date"
                placeholder="请选择签署日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="生效日期" prop="effectiveDate">
              <el-date-picker
                v-model="formData.effectiveDate"
                type="date"
                placeholder="请选择生效日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="到期日期" prop="expiryDate">
              <el-date-picker
                v-model="formData.expiryDate"
                type="date"
                placeholder="请选择到期日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="签署地点" prop="signingLocation">
              <el-input v-model="formData.signingLocation" placeholder="请输入签署地点" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同状态" prop="contractStatus">
              <el-select v-model="formData.contractStatus" placeholder="请选择合同状态" style="width: 100%">
                <el-option label="草稿" value="draft" />
                <el-option label="待签署" value="pending" />
                <el-option label="已生效" value="active" />
                <el-option label="已完成" value="completed" />
                <el-option label="已终止" value="terminated" />
                <el-option label="已作废" value="cancelled" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-card>

      <!-- 伙伴信息 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>伙伴信息</h3>
            <el-button type="primary" size="small" @click="addPartner">
              <el-icon><Plus /></el-icon>
              添加伙伴
            </el-button>
          </div>
        </template>
        <el-table :data="formData.parties" border style="width: 100%">
          <el-table-column label="伙伴" min-width="180">
            <template #default="scope">
              <el-form-item
                :prop="`parties.${scope.$index}.partnerId`"
                :rules="partnerValidationRules"
                class="table-form-item"
              >
                <el-select
                  v-model="scope.row.partnerId"
                  placeholder="请选择伙伴"
                  style="width: 100%"
                  filterable
                >
                  <el-option
                    v-for="item in partnerOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="伙伴角色" width="110">
            <template #default="scope">
              <el-select
                v-model="scope.row.partnerRole"
                placeholder="请选择角色"
                style="width: 100%"
              >
                <el-option
                  v-for="item in partnerRoleOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="角色描述" width="110">
            <template #default="scope">
              <el-select
                v-model="scope.row.partnerRoleDesc"
                placeholder="请选择描述"
                style="width: 100%"
              >
                <el-option
                  v-for="item in partnerRoleDescOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="签署人" min-width="100">
            <template #default="scope">
              <el-input v-model="scope.row.signingPerson" placeholder="签署人" />
            </template>
          </el-table-column>
          <el-table-column label="签署人职务" min-width="120">
            <template #default="scope">
              <el-input v-model="scope.row.signingPersonTitle" placeholder="职务" />
            </template>
          </el-table-column>
          <el-table-column label="签署日期" width="140">
            <template #default="scope">
              <el-date-picker
                v-model="scope.row.signingDate"
                type="date"
                placeholder="签署日期"
                style="width: 100%"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" align="center">
            <template #default="scope">
              <el-button
                link
                type="danger"
                size="small"
                @click="removePartner(scope.$index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div v-if="!formData.parties || formData.parties.length === 0" class="no-data">
          暂无伙伴信息
        </div>
      </el-card>

      <!-- 文件管理 -->
      <el-card shadow="never" class="section-card" v-if="formData.id">
        <template #header>
          <div class="card-header">
            <h3>文件管理</h3>
          </div>
        </template>
        <contract-file-manager
          ref="fileManagerRef"
          :contract-id="formData.id"
        />
      </el-card>
    </el-form>

    <template #footer>
      <div class="action-buttons">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, computed, watch } from "vue";
import {
  ContractForm,
  ContractPartnerRelationForm,
} from "@/api/contract/contract.api";
import ContractAPI from "@/api/contract/contract.api";
import PartnerAPI from "@/api/contract/partner.api";
import OpportunityAPI from "@/api/opportunity/opportunity.api";
import UserAPI from "@/api/system/user.api";
import ContractFileManager from "@/components/Contract/ContractFileManager.vue";
import { useDictStore } from "@/store/modules/dict.store";

defineOptions({
  name: "ContractFormDrawer",
  inheritAttrs: false,
});

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  contractId: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["update:modelValue", "success"]);

const visible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit("update:modelValue", val);
  },
});

const dictStore = useDictStore();

const dataFormRef = ref(ElForm);

const isEditMode = computed(() => !!formData.id);

const drawerTitle = computed(() => {
  return isEditMode.value ? '编辑合同' : '新建合同';
});

const formData = reactive<ContractForm>({
  contractNo: "",
  contractName: "",
  contractStatus: "draft",
  parties: [],
});

const fileManagerRef = ref();

const rules = reactive({
  contractNo: [{ required: true, message: "请输入合同编号", trigger: "blur" }],
  contractName: [{ required: true, message: "请输入合同名称", trigger: "blur" }],
});

// 伙伴验证规则
const partnerValidationRules = [
  { required: true, message: "请选择伙伴", trigger: "change" }
];

const contractTypeOptions = ref<OptionType[]>([]);
const contractCategoryOptions = ref<OptionType[]>([]);
const paymentMethodOptions = ref<OptionType[]>([]);
const opportunityOptions = ref<OptionType[]>([]);
const partnerOptions = ref<OptionType[]>([]);
const partnerRoleOptions = ref<OptionType[]>([]);
const partnerRoleDescOptions = ref<OptionType[]>([]);
const userOptions = ref<OptionType[]>([]);

function addPartner() {
  const newPartner: ContractPartnerRelationForm = {
    partnerId: "",
    partnerRole: "",
    sort: formData.parties?.length || 0,
  };
  if (!formData.parties) {
    formData.parties = [];
  }
  formData.parties.push(newPartner);
}

function removePartner(index: number) {
  formData.parties?.splice(index, 1);
}
  
function handleSubmit() {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      const id = formData.id;
      if (id) {
        ContractAPI.update(id, formData).then(() => {
          ElMessage.success("合同修改成功");
          emit("success");
          handleClose();
        });
      } else {
        ContractAPI.create(formData).then(() => {
          ElMessage.success("合同创建成功");
          emit("success");
          handleClose();
        });
      }
    }
  });
}

function loadFormData(id: string) {
  if (id) {
    ContractAPI.getFormData(id).then((data) => {
      Object.assign(formData, data);
    });
  }
}



function handleClose() {
  visible.value = false;
  dataFormRef.value.resetFields();
  Object.assign(formData, {
    id: undefined,
    contractNo: "",
    contractName: "",
    contractType: undefined,
    contractCategory: undefined,
    contractAmount: undefined,
    opportunityId: undefined,
    signingDate: undefined,
    effectiveDate: undefined,
    expiryDate: undefined,
    contractStatus: "draft",
    paymentMethod: undefined,
    attachmentId: undefined,
    signingLocation: undefined,
    responsibleUserId: undefined,
    deptId: undefined,
    remark: undefined,
    parties: [],
  });

}

watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      if (props.contractId) {
        loadFormData(props.contractId);
      }
    }
  }
);

onMounted(async () => {
  // 加载字典数据
  await dictStore.loadDictItems("contract_type");
  contractTypeOptions.value = dictStore.getDictItems("contract_type");

  await dictStore.loadDictItems("contract_category");
  contractCategoryOptions.value = dictStore.getDictItems("contract_category");

  await dictStore.loadDictItems("payment_method");
  paymentMethodOptions.value = dictStore.getDictItems("payment_method");

  await dictStore.loadDictItems("partner_role");
  partnerRoleOptions.value = dictStore.getDictItems("partner_role");

  await dictStore.loadDictItems("partner_role_desc");
  partnerRoleDescOptions.value = dictStore.getDictItems("partner_role_desc");

  // 加载伙伴选项
  PartnerAPI.getOptions().then((options) => {
    partnerOptions.value = options;
  });

  // 加载商机选项
  OpportunityAPI.getOptions().then((options) => {
    opportunityOptions.value = options;
  });

  // 加载用户选项
  UserAPI.getOptions().then((options) => {
    userOptions.value = options;
  });
});
</script>

<style scoped>
.contract-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0 20px;
}

.section-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 40px 0;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表格内表单项样式 */
.table-form-item {
  margin-bottom: 0 !important;
  margin-left: 0 !important;
}

.table-form-item .el-form-item__content {
  margin-left: 0 !important;
  width: 100% !important;
}

.table-form-item .el-form-item__label {
  display: none !important;
}

.table-form-item .el-form-item__error {
  position: static !important;
  margin-top: 2px;
  padding-top: 0;
  font-size: 12px;
  line-height: 1.2;
  margin-left: 0 !important;
}

/* 修复伙伴信息表格中的间隙问题 */
.el-table .el-form-item__content {
  margin-left: 0 !important;
}

/* 更强力的修复：针对表格内所有表单项内容 */
.el-table .el-table__body .el-form-item__content {
  margin-left: 0 !important;
}

/* 针对伙伴信息表格的具体修复 */
.contract-form .el-table .el-form-item__content {
  margin-left: 0 !important;
}

/* 深度选择器修复 */
:deep(.el-table .el-form-item__content) {
  margin-left: 0 !important;
}


</style>
