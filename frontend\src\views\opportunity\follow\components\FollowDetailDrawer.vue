<template>
  <el-drawer
    v-model="visible"
    title="跟进记录详情"
    direction="rtl"
    size="60%"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="loading-container" element-loading-text="加载中...">
      <div v-if="followDetail" class="follow-detail">
        <!-- 基本信息 -->
        <el-card shadow="never" class="section-card">
          <template #header>
            <span class="text-lg font-medium">基本信息</span>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="跟进时间">
              {{ followDetail.followDate }}
            </el-descriptions-item>
            <el-descriptions-item label="跟进类型">
              <el-tag :type="getFollowTypeTagType(followDetail.followType)" effect="light">
                {{ followDetail.followTypeLabel }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="跟进时长">
              {{ followDetail.followDuration ? followDetail.followDuration + ' 分钟' : '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="联系人">
              {{ followDetail.contactPerson || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="跟进结果">
              <el-tag :type="getResultTagType(followDetail.followResult)" effect="light">
                {{ followDetail.followResultLabel }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="下次跟进时间">
              {{ followDetail.nextFollowDate || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="跟进人">
              {{ followDetail.followUserName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ followDetail.createTime }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 商机信息 -->
        <el-card shadow="never" class="section-card">
          <template #header>
            <span class="text-lg font-medium">关联商机</span>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="商机编号">
              {{ followDetail.opportunityCode || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="商机名称">
              {{ followDetail.opportunityName || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 跟进内容 -->
        <el-card shadow="never" class="section-card">
          <template #header>
            <span class="text-lg font-medium">跟进内容</span>
          </template>
          <div class="content-section">
            <div class="content-text">
              {{ followDetail.followContent || '暂无跟进内容' }}
            </div>
          </div>
        </el-card>

        <!-- 下次行动计划 -->
        <el-card shadow="never" class="section-card" v-if="followDetail.nextAction">
          <template #header>
            <span class="text-lg font-medium">下次行动计划</span>
          </template>
          <div class="content-section">
            <div class="content-text">
              {{ followDetail.nextAction }}
            </div>
          </div>
        </el-card>

        <!-- 备注信息 -->
        <el-card shadow="never" class="section-card" v-if="followDetail.remark">
          <template #header>
            <span class="text-lg font-medium">备注信息</span>
          </template>
          <div class="content-section">
            <div class="content-text">
              {{ followDetail.remark }}
            </div>
          </div>
        </el-card>

        <!-- 附件信息 -->
        <el-card shadow="never" class="section-card" v-if="followDetail.attachmentId">
          <template #header>
            <span class="text-lg font-medium">附件信息</span>
          </template>
          <div class="content-section">
            <div class="attachment-info">
              <el-icon><Paperclip /></el-icon>
              <span>附件ID: {{ followDetail.attachmentId }}</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Paperclip } from '@element-plus/icons-vue';
import OpportunityFollowAPI from '@/api/opportunity/follow.api';

interface Props {
  modelValue: boolean;
  followId?: number;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'edit', id: number): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  followId: undefined,
});

const emit = defineEmits<Emits>();

const visible = ref(false);
const loading = ref(false);
const followDetail = ref<any>(null);

// 跟进类型标签颜色映射
const followTypeTagMap: Record<string, "primary" | "success" | "info" | "warning" | "danger"> = {
  phone: 'primary',
  email: 'success',
  visit: 'info',
  wechat: 'warning',
  meeting: 'info',
  demo: 'info',
  proposal: 'info',
  quotation: 'info',
  other: 'info',
};

// 跟进结果标签颜色映射
const resultTagMap: Record<string, "primary" | "success" | "info" | "warning" | "danger"> = {
  positive: 'success',
  interested: 'primary',
  considering: 'info',
  need_more_info: 'warning',
  price_concern: 'danger',
  no_response: 'info',
  rejected: 'danger',
  postponed: 'warning',
  other: 'info',
};

function getFollowTypeTagType(type: string): "primary" | "success" | "info" | "warning" | "danger" {
  return followTypeTagMap[type] || 'info';
}

function getResultTagType(result: string): "primary" | "success" | "info" | "warning" | "danger" {
  return resultTagMap[result] || 'info';
}

function loadFollowDetail() {
  if (!props.followId) {
    ElMessage.error("跟进记录ID不能为空");
    handleClose();
    return;
  }

  loading.value = true;
  OpportunityFollowAPI.getDetail(props.followId)
    .then((response: any) => {
      console.log('跟进记录详情API响应:', response);
      // 处理不同的响应结构
      let data = response;
      if (response.data) {
        data = response.data;
      }
      followDetail.value = data;
      console.log('跟进记录详情:', followDetail.value);
    })
    .catch((error) => {
      console.error('获取跟进记录详情失败:', error);
      ElMessage.error("获取跟进记录详情失败");
      handleClose();
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleClose() {
  visible.value = false;
  followDetail.value = null;
}

function handleEdit() {
  if (props.followId) {
    emit('edit', props.followId);
    handleClose();
  }
}

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
    if (val && props.followId) {
      loadFollowDetail();
    }
  },
  { immediate: true }
);

watch(visible, (val) => {
  emit('update:modelValue', val);
});
</script>

<style scoped>
.loading-container {
  min-height: 200px;
}

.follow-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0 20px;
}

.section-card {
  border-radius: 8px;
}

.content-section {
  padding: 16px 0;
}

.content-text {
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-word;
}

.attachment-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #409eff;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid var(--el-border-color-light);
}
</style>
