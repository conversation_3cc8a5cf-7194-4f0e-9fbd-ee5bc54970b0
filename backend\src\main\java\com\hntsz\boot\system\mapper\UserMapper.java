package com.hntsz.boot.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.system.model.bo.UserBO;
import com.hntsz.boot.system.model.entity.User;
import com.hntsz.boot.system.model.query.UserPageQuery;
import com.hntsz.boot.system.model.form.UserForm;
import com.hntsz.boot.common.annotation.DataPermission;
import com.hntsz.boot.core.security.model.UserAuthCredentials;
import com.hntsz.boot.system.model.dto.UserExportDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户持久层接口
 *
 * <AUTHOR>
 * @since 2022/1/14
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 获取用户分页列表
     *
     * @param page        分页参数
     * @param queryParams 查询参数
     * @return 用户分页列表
     */
    @DataPermission(deptAlias = "u", userAlias = "u")
    Page<UserBO> getUserPage(Page<UserBO> page, UserPageQuery queryParams);

    /**
     * 获取用户表单详情
     *
     * @param userId 用户ID
     * @return 用户表单详情
     */
    UserForm getUserFormData(Long userId);

    /**
     * 根据用户名获取认证信息
     *
     * @param username 用户名
     * @return 认证信息
     */
    UserAuthCredentials getAuthCredentialsByUsername(String username);

    /**
     * 根据微信openid获取用户认证信息
     *
     * @param openid 微信openid
     * @return 认证信息
     */
    UserAuthCredentials getAuthCredentialsByOpenId(String openid);

    /**
     * 根据手机号获取用户认证信息
     *
     * @param mobile 手机号
     * @return 认证信息
     */
    UserAuthCredentials getAuthCredentialsByMobile(String mobile);

    /**
     * 获取导出用户列表
     *
     * @param queryParams 查询参数
     * @return 导出用户列表
     */
    @DataPermission(deptAlias = "u", userAlias = "u")
    List<UserExportDTO> listExportUsers(UserPageQuery queryParams);

    /**
     * 获取用户个人中心信息
     *
     * @param userId 用户ID
     * @return 用户个人中心信息
     */
    UserBO getUserProfile(Long userId);

    /**
     * 根据 OAuth 提供商和用户ID获取用户认证信息
     *
     * @param provider OAuth 提供商
     * @param oauthId OAuth 用户ID
     * @return 认证信息
     */
    UserAuthCredentials getAuthCredentialsByOAuth(String provider, String oauthId);

    /**
     * 获取用户选项列表（根据当前用户权限过滤ROOT用户）
     *
     * @param isRoot 当前用户是否为ROOT用户
     * @return 用户列表
     */
    List<User> listUserOptions(boolean isRoot);

}
