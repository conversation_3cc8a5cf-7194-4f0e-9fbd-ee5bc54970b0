package com.hntsz.boot.modules.opportunity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商机跟进记录Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface OpportunityFollowMapper extends BaseMapper<com.hntsz.boot.modules.opportunity.model.entity.OpportunityFollow> {

    /**
     * 分页查询商机跟进记录
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 跟进记录分页列表
     */
    Page<com.hntsz.boot.modules.opportunity.model.vo.OpportunityFollowVO> selectFollowPage(
        Page<com.hntsz.boot.modules.opportunity.model.vo.OpportunityFollowVO> page,
        @Param("query") com.hntsz.boot.modules.opportunity.model.query.OpportunityFollowQuery query);

    /**
     * 根据ID查询跟进记录详情
     *
     * @param id 跟进记录ID
     * @return 跟进记录详情
     */
    com.hntsz.boot.modules.opportunity.model.vo.OpportunityFollowVO selectFollowById(@Param("id") Long id);

    /**
     * 根据商机ID查询跟进记录列表
     *
     * @param opportunityId 商机ID
     * @return 跟进记录列表
     */
    List<com.hntsz.boot.modules.opportunity.model.vo.OpportunityFollowVO> selectFollowsByOpportunityId(@Param("opportunityId") Long opportunityId);

    /**
     * 根据商机ID删除跟进记录
     *
     * @param opportunityId 商机ID
     * @return 删除记录数
     */
    int deleteByOpportunityId(@Param("opportunityId") Long opportunityId);

    /**
     * 根据跟进人ID查询跟进记录数量
     *
     * @param followUserId 跟进人ID
     * @return 跟进记录数量
     */
    int countByFollowUserId(@Param("followUserId") Long followUserId);
}