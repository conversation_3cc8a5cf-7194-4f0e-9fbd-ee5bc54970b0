import { create } from "zustand";
import { persist } from "zustand/middleware";
import AuthAPI, { LoginFormData, LoginResult } from "@/api/auth";
import SystemUsersAPI, { IUserInfo } from "@/api/user";
import { Auth } from "@/utils/auth";

/**
 * 用户状态接口
 */
interface UserState {
  /** 用户信息 */
  userInfo: IUserInfo | null;
  /** 是否已登录 */
  isLoggedIn: boolean;
  /** 记住我状态 */
  rememberMe: boolean;
  /** 登录加载状态 */
  loginLoading: boolean;
  /** 用户信息加载状态 */
  userInfoLoading: boolean;
}

/**
 * 用户操作接口
 */
interface UserActions {
  /** 登录 */
  login: (formData: LoginFormData) => Promise<void>;
  /** oauth登录 */
  oauthLogin: (provider: string, code: string) => Promise<void>;
  /** 获取用户信息 */
  getUserInfo: () => Promise<void>;
  /** 退出登录 */
  logout: () => Promise<void>;
  /** 刷新Token */
  refreshToken: () => Promise<void>;
  /** 重置用户状态 */
  resetUserState: () => void;
  /** 检查登录状态 */
  checkLoginStatus: () => void;
}

type UserStore = UserState & UserActions;

/**
 * 用户状态管理Store
 * 使用Zustand进行状态管理，支持持久化存储
 */
export const useUserStore = create<UserStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      userInfo: null,
      isLoggedIn: false,
      rememberMe: false,
      loginLoading: false,
      userInfoLoading: false,

      /**
       * 用户登录
       * @param formData 登录表单数据
       */
      login: async (formData: LoginFormData) => {
        set({ loginLoading: true });
        try {
          // 调用登录API
          const loginResult: LoginResult = await AuthAPI.login(formData);
          const { accessToken, refreshToken } = loginResult;

          // 保存Token到本地存储
          Auth.setTokens(accessToken, refreshToken, formData.rememberMe);

          // 更新状态
          set({
            isLoggedIn: true,
            rememberMe: formData.rememberMe,
          });

          // 登录成功后获取用户信息
          await get().getUserInfo();
        } catch (error) {
          console.error("登录失败:", error);
          throw error;
        } finally {
          set({ loginLoading: false });
        }
      },

      /** oauth登录 */
      oauthLogin: async (provider: string = "oauth", code: string) => {
        set({ loginLoading: true });
        try {
          // 调用登录API
          const loginResult: LoginResult = await AuthAPI.oauthLogin(
            provider,
            code
          );
          const { accessToken, refreshToken } = loginResult;

          // 保存Token到本地存储
          Auth.setTokens(accessToken, refreshToken, false);

          // 更新状态
          set({
            isLoggedIn: true,
            rememberMe: false,
          });

          // 登录成功后获取用户信息
          await get().getUserInfo();
        } catch (error) {
          console.error("登录失败:", error);
          throw error;
        } finally {
          set({ loginLoading: false });
        }
      },

      /**
       * 获取用户信息
       */
      getUserInfo: async () => {
        set({ userInfoLoading: true });
        try {
          const userInfo = await SystemUsersAPI.getInfo();
          set({ userInfo });
        } catch (error) {
          console.error("获取用户信息失败:", error);
          throw error;
        } finally {
          set({ userInfoLoading: false });
        }
      },

      /**
       * 退出登录
       */
      logout: async () => {
        try {
          // 调用退出登录API
          await AuthAPI.logout();
        } catch (error) {
          console.error("退出登录API调用失败:", error);
          // 即使API调用失败，也要清除本地状态
        } finally {
          // 重置所有状态
          get().resetUserState();
        }
      },

      /**
       * 刷新Token
       */
      refreshToken: async () => {
        const refreshToken = Auth.getRefreshToken();
        if (!refreshToken) {
          throw new Error("没有有效的刷新令牌");
        }

        try {
          const loginResult: LoginResult = await AuthAPI.refreshToken(
            refreshToken
          );
          const { accessToken, refreshToken: newRefreshToken } = loginResult;

          // 更新Token，保持当前记住我状态
          Auth.setTokens(accessToken, newRefreshToken, Auth.getRememberMe());
        } catch (error) {
          console.error("刷新Token失败:", error);
          // 刷新失败，清除用户状态
          get().resetUserState();
          throw error;
        }
      },

      /**
       * 重置用户状态
       */
      resetUserState: () => {
        // 清除认证信息
        Auth.clearAuth();

        // 重置状态
        set({
          userInfo: null,
          isLoggedIn: false,
          rememberMe: false,
          loginLoading: false,
          userInfoLoading: false,
        });
      },

      /**
       * 检查登录状态
       * 应用启动时调用，检查本地是否有有效Token
       */
      checkLoginStatus: () => {
        const isLoggedIn = Auth.isLoggedIn();
        const rememberMe = Auth.getRememberMe();

        set({
          isLoggedIn,
          rememberMe,
        });

        // 如果已登录，获取用户信息
        if (isLoggedIn) {
          get()
            .getUserInfo()
            .catch(() => {
              // 获取用户信息失败，可能Token已过期，重置状态
              get().resetUserState();
            });
        }
      },
    }),
    {
      name: "user-store",
      // 只持久化部分状态，避免存储敏感信息
      partialize: (state) => ({
        rememberMe: state.rememberMe,
      }),
    }
  )
);
