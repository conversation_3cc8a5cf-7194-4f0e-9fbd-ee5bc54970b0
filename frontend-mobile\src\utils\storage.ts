/**
 * 统一的存储工具类
 * 提供localStorage和sessionStorage的封装
 */
export class Storage {
  /**
   * 设置localStorage数据
   * @param key 存储键
   * @param value 存储值
   */
  static set<T>(key: string, value: T): void {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('localStorage设置失败:', error);
    }
  }

  /**
   * 获取localStorage数据
   * @param key 存储键
   * @param defaultValue 默认值
   * @returns 存储的值或默认值
   */
  static get<T>(key: string, defaultValue: T): T {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error('localStorage获取失败:', error);
      return defaultValue;
    }
  }

  /**
   * 删除localStorage数据
   * @param key 存储键
   */
  static remove(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('localStorage删除失败:', error);
    }
  }

  /**
   * 设置sessionStorage数据
   * @param key 存储键
   * @param value 存储值
   */
  static sessionSet<T>(key: string, value: T): void {
    try {
      sessionStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('sessionStorage设置失败:', error);
    }
  }

  /**
   * 获取sessionStorage数据
   * @param key 存储键
   * @param defaultValue 默认值
   * @returns 存储的值或默认值
   */
  static sessionGet<T>(key: string, defaultValue: T): T {
    try {
      const item = sessionStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error('sessionStorage获取失败:', error);
      return defaultValue;
    }
  }

  /**
   * 删除sessionStorage数据
   * @param key 存储键
   */
  static sessionRemove(key: string): void {
    try {
      sessionStorage.removeItem(key);
    } catch (error) {
      console.error('sessionStorage删除失败:', error);
    }
  }

  /**
   * 清空所有存储
   */
  static clear(): void {
    try {
      localStorage.clear();
      sessionStorage.clear();
    } catch (error) {
      console.error('存储清空失败:', error);
    }
  }
}