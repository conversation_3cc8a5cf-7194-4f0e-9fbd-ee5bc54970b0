package com.hntsz.boot.core.security.extension.oauth;

import lombok.Data;

/**
 * OAuth 令牌响应
 * 
 * <AUTHOR>
 * @since 2.22.0
 */
@Data
public class OAuthTokenResponse {
    
    /**
     * 访问令牌
     */
    private String accessToken;
    
    /**
     * 令牌类型，通常为 "Bearer"
     */
    private String tokenType;
    
    /**
     * 访问令牌过期时间（秒）
     */
    private Integer expiresIn;
    
    /**
     * 刷新令牌
     */
    private String refreshToken;
    
    /**
     * 权限范围
     */
    private String scope;
    
    /**
     * 状态参数
     */
    private String state;
    
    /**
     * 错误代码
     */
    private String error;
    
    /**
     * 错误描述
     */
    private String errorDescription;
    
    /**
     * 错误URI
     */
    private String errorUri;
    
    /**
     * 检查响应是否有错误
     */
    public boolean hasError() {
        return error != null;
    }
    
    /**
     * 获取错误信息
     */
    public String getErrorMessage() {
        if (errorDescription != null) {
            return errorDescription;
        }
        if (error != null) {
            return error;
        }
        return "Unknown OAuth error";
    }
}