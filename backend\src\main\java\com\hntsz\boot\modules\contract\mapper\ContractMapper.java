package com.hntsz.boot.modules.contract.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hntsz.boot.modules.contract.model.entity.Contract;
import com.hntsz.boot.modules.contract.model.query.ContractQuery;
import com.hntsz.boot.modules.contract.model.vo.ContractStatisticsVO;
import com.hntsz.boot.modules.contract.model.vo.ContractVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合同主表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Mapper
public interface ContractMapper extends BaseMapper<Contract> {

    /**
     * 获取合同分页列表
     *
     * @param page 分页参数
     * @param query 查询参数
     * @return 合同分页列表
     */
    IPage<ContractVO> getContractPage(IPage<ContractVO> page, @Param("query") ContractQuery query);

    /**
     * 获取合同详情
     *
     * @param id 合同ID
     * @return 合同详情
     */
    ContractVO getContractDetail(@Param("id") Long id);

    /**
     * 根据合同编号查询合同
     *
     * @param contractNo 合同编号
     * @return 合同
     */
    Contract getByContractNo(@Param("contractNo") String contractNo);

    /**
     * 获取合同选项列表
     *
     * @return 合同选项列表
     */
    List<ContractVO> getContractOptions();

    /**
     * 根据伙伴ID获取相关合同列表
     *
     * @param partnerId 伙伴ID
     * @return 合同列表
     */
    List<ContractVO> getContractsByPartnerId(@Param("partnerId") Long partnerId);

    /**
     * 根据负责人ID获取合同列表
     *
     * @param responsibleUserId 负责人ID
     * @return 合同列表
     */
    List<ContractVO> getContractsByResponsibleUserId(@Param("responsibleUserId") Long responsibleUserId);

    /**
     * 根据部门ID获取合同列表
     *
     * @param deptId 部门ID
     * @return 合同列表
     */
    List<ContractVO> getContractsByDeptId(@Param("deptId") Long deptId);

    /**
     * 根据状态统计合同
     *
     * @param status 合同状态
     * @return 合同统计信息
     */
    ContractStatisticsVO getContractStatistics(@Param("status") String status);

    /**
     * 获取合同完整详情（包含商机名称）
     *
     * @param id 合同ID
     * @return 合同完整详情
     */
    ContractVO getContractFullDetail(@Param("id") Long id);
}
