import request from "@/utils/request";

/** 模块API接口前缀 */
const MODULE_API_PREFIX = "/api/v1/opportunity-follows";

/**
 * 商机模块的API
 */
const BizOpportunityFollowUpAPI = {
  /** 分页获取待跟进商机列表 */
  getUpOpportunityFollowUpList(params: { id: string }) {
    return request<typeof params, IOpportunityFollowDetail[]>({
      url: `${MODULE_API_PREFIX}/opportunity/${params.id}`,
      method: "get",
      params,
    });
  },
};

export default BizOpportunityFollowUpAPI;

/** 商机跟进详情数据类型 */
export interface IOpportunityFollowDetail {
  /**
   * 附件ID
   */
  attachmentId?: string;
  /**
   * 联系人
   */
  contactPerson?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 跟进内容
   */
  followContent?: string;
  /**
   * 跟进时间
   */
  followDate?: string;
  /**
   * 跟进时长(分钟)
   */
  followDuration?: number;
  /**
   * 跟进结果
   */
  followResult?: string;
  /**
   * 跟进结果标签
   */
  followResultLabel?: string;
  /**
   * 跟进方式
   */
  followType?: string;
  /**
   * 跟进方式标签
   */
  followTypeLabel?: string;
  /**
   * 跟进人ID
   */
  followUserId?: number;
  /**
   * 跟进人姓名
   */
  followUserName?: string;
  /**
   * 主键ID
   */
  id?: number;
  /**
   * 下一步行动计划
   */
  nextAction?: string;
  /**
   * 下次跟进日期
   */
  nextFollowDate?: string;
  /**
   * 商机编码
   */
  opportunityCode?: string;
  /**
   * 商机ID
   */
  opportunityId?: number;
  /**
   * 商机名称
   */
  opportunityName?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}
