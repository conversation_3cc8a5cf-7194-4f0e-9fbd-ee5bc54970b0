package ${package.Parent}.converter;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import ${package.Parent}.model.dto.${entity}DTO;
import ${package.Parent}.model.entity.${entity};
import ${package.Parent}.model.vo.${entity}PageVO;
import ${package.Parent}.model.form.${entity}Form;
import ${package.Parent}.model.bo.${entity}BO;

/**
 * $!{table.comment}转换器
 *
 * <AUTHOR>
 * @since ${date}
 */
@Mapper(componentModel = "spring")
public interface ${entity}Converter{

    ${entity}PageVO toPageVo(${entity}BO bo);

    Page<${entity}PageVO> toPageVo(Page<${entity}BO> bo);

    ${entity}Form toForm(${entity} entity);

    @InheritInverseConfiguration(name = "toForm")
    ${entity} toEntity(${entity}Form entity);
}