package com.hntsz.boot.modules.contract.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hntsz.boot.common.base.BaseEntityExtra;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;

/**
 * 业务伙伴实体对象
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@Setter
@TableName("partner")
public class Partner extends BaseEntityExtra {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 伙伴名称
     */
    private String partnerName;

    /**
     * 伙伴编码
     */
    private String partnerCode;

    /**
     * 是否我司或旗下企业(1-是 0-否)
     */
    private Boolean isOurCompany;

    /**
     * 伙伴类型(关联字典编码：partner_type)
     */
    private String partnerType;

    /**
     * 法定代表人
     */
    private String legalRepresentative;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 地址
     */
    private String address;

    /**
     * 证件类型(关联字典编码：certificate_type)
     */
    private String certificateType;

    /**
     * 证件号码
     */
    private String certificateNumber;

    /**
     * 税号
     */
    private String taxNumber;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 状态(active-正常 inactive-禁用)
     */
    private String status;

    /**
     * 备注
     */
    private String remark;
}
