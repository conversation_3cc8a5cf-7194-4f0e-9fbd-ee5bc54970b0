import { useEffect } from "react";
import { Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useOAuth } from "@/hooks/useOAuth";

/**
 * OAuth 回调处理页面
 * 处理从第三方授权服务器返回的授权码并完成登录
 */
export default function OAuthCallbackPage() {
  const {
    loading,
    statusMessage,
    statusType,
    hasError,
    handleOAuthCallback,
    goToLogin,
  } = useOAuth();

  /**
   * 页面加载时自动处理 OAuth 回调
   */
  useEffect(() => {
    handleOAuthCallback();
  }, [handleOAuthCallback]);

  /**
   * 根据状态获取对应的图标
   */
  const getStatusIcon = () => {
    if (hasError) {
      return <AlertCircle className="h-12 w-12 text-destructive" />;
    }
    if (statusType === "success") {
      return <CheckCircle className="h-12 w-12 text-green-600" />;
    }
    return <Loader2 className="h-12 w-12 text-primary animate-spin" />;
  };

  /**
   * 根据状态类型获取对应的文本颜色
   */
  const getStatusTextColor = () => {
    switch (statusType) {
      case "success":
        return "text-green-600";
      case "warning":
        return "text-yellow-600";
      case "danger":
        return "text-destructive";
      default:
        return "text-muted-foreground";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 via-background to-accent/10 flex flex-col justify-center items-center p-4">
      <div className="w-full max-w-md">
        {/* 回调处理卡片 */}
        <div className="bg-card rounded-lg shadow-lg p-8 text-center space-y-6">
          {/* 状态图标 */}
          <div className="flex justify-center">
            {getStatusIcon()}
          </div>

          {/* 标题和状态信息 */}
          <div className="space-y-2">
            <h1 className="text-2xl font-bold text-foreground">
              {hasError ? "登录失败" : loading ? "正在登录" : "登录成功"}
            </h1>
            <p className={`text-sm ${getStatusTextColor()}`}>
              {statusMessage || "正在处理OAuth登录..."}
            </p>
          </div>

          {/* 错误时显示重试按钮 */}
          {hasError && (
            <div className="space-y-3">
              <Button
                onClick={goToLogin}
                className="w-full"
                variant="default"
              >
                返回登录页面
              </Button>
            </div>
          )}

          {/* 成功状态提示 */}
          {statusType === "success" && !loading && (
            <div className="text-xs text-muted-foreground">
              <p>登录成功！正在跳转到主页...</p>
            </div>
          )}

          {/* 加载状态提示 */}
          {loading && !hasError && (
            <div className="text-xs text-muted-foreground">
              <p>正在验证授权信息，请稍候...</p>
            </div>
          )}
        </div>

        {/* 版本信息 */}
        <div className="text-center mt-4 text-xs text-muted-foreground">
          <p>探数者综合管理平台 v1.0.0</p>
        </div>
      </div>
    </div>
  );
}