<template>
  <div>
    <PageLight ref="pageLightRef" :config="config" @add-click="handleAddClick">
      <template #filePath="{ row }">
        <div v-if="row.fileType === 'IMAGE'">
          <el-image
            style="width: 100px; height: 100px"
            :src="FileAPI.getFullUrl(row.filePath)"
            fit="cover"
          />
        </div>
        <div v-else-if="row.fileType === 'VIDEO'">
          <video
            style="width: 100px; height: 100px"
            :src="FileAPI.getFullUrl(row.filePath)"
            controls
            preload="none"
          />
        </div>
        <div v-else-if="row.fileType === 'AUDIO'">
          <audio
            style="width: 100px; height: 100px"
            :src="FileAPI.getFullUrl(row.filePath)"
            controls
          />
        </div>
        <div v-else>
          <a :href="FileAPI.getFullUrl(row.filePath)" target="_blank">下载</a>
        </div>
      </template>
      <template #fileType="{ row }">
        <div v-if="row.fileType === 'IMAGE'" class="flex items-center justify-center gap-2">
          <el-icon :size="16" color="#409EFF">
            <Picture />
          </el-icon>
          <span>图片</span>
        </div>
        <div v-else-if="row.fileType === 'VIDEO'" class="flex items-center justify-center gap-2">
          <el-icon :size="16" color="#409EFF">
            <VideoCamera />
          </el-icon>
          <span>视频</span>
        </div>
        <div v-else-if="row.fileType === 'DOCUMENT'" class="flex items-center justify-center gap-2">
          <el-icon :size="16" color="#409EFF">
            <Document />
          </el-icon>
          <span>文档</span>
        </div>
        <div v-else-if="row.fileType === 'AUDIO'" class="flex items-center justify-center gap-2">
          <el-icon :size="16" color="#409EFF">
            <VideoPlay />
          </el-icon>
          <span>音频</span>
        </div>
        <div v-else class="flex items-center justify-center gap-2">
          <el-icon :size="16" color="#409EFF">
            <InfoFilled />
          </el-icon>
          <span>其他</span>
        </div>
      </template>
      <template #fileSize="{ row }">
        <div>{{ (row.fileSize / (1024 * 1024)).toFixed(2) }} MB</div>
      </template>
    </PageLight>
    <AttachmentUploader
      :dialog-visible="dialogVisible"
      @success="handleSuccess"
      @close="handleClose"
    />
  </div>
</template>

<script setup lang="ts">
import type { PageLightProps } from "@/components/CURDLight/types";
import AttachmentAPI from "@/api/attachment/attachment.api";
import PageLight from "@/components/CURDLight/PageLight.vue";
import FileAPI from "@/api/file.api";
import AttachmentUploader from "@/components/Attachment/uploader.vue";

const pageLightRef = ref<InstanceType<typeof PageLight>>();
const dialogVisible = ref(false);

const handleAddClick = () => {
  dialogVisible.value = true;
};

const handleSuccess = () => {
  pageLightRef.value?.fetchPageData();
  dialogVisible.value = false;
};

const handleClose = () => {
  dialogVisible.value = false;
};

const config: PageLightProps = {
  subjectName: "附件管理",
  editModalUseAddModal: true,
  editModelSubmit: () => Promise.resolve(),
  contentConfig: {
    permPrefix: "sys:attachment",
    table: {
      border: true,
      highlightCurrentRow: true,
    },
    indexAction: (params) => AttachmentAPI.getList({ ...params }),
    deleteAction: (ids) => AttachmentAPI.deleteByIds(ids),
    pk: "id",
    toolbar: ["add", "delete"],
    defaultToolbar: ["refresh", "filter"],
    cols: [
      { type: "selection", width: 50, align: "center" },
      { label: "Id", align: "center", prop: "id", width: 100, show: true },
      { label: "内容", align: "center", prop: "filePath", templet: "custom" },
      { label: "文件名", align: "center", prop: "fileName" },
      { label: "文件类型", align: "center", prop: "fileType", templet: "custom" },
      { label: "文件大小", align: "center", prop: "fileSize", templet: "custom" },
      {
        label: "操作",
        align: "center",
        fixed: "right",
        templet: "tool",
        operat: ["delete"],
      },
    ],
  },
};
</script>
