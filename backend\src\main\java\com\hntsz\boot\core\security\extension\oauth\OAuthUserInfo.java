package com.hntsz.boot.core.security.extension.oauth;

import lombok.Data;

/**
 * OAuth 用户信息
 * 
 * <AUTHOR>
 * @since 2.22.0
 */
@Data
public class OAuthUserInfo {
    
    /**
     * OAuth 提供商用户ID
     */
    private String oauthId;
    
    /**
     * OAuth 提供商
     */
    private String provider;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 昵称
     */
    private String nickname;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 头像URL
     */
    private String avatar;
    
    /**
     * 手机号
     */
    private String mobile;
    
    /**
     * 性别 (1-男 2-女 0-保密)
     */
    private Integer gender;

    /**
     * 默认部门ID
     */
    private Long defaultDeptId;

    /**
     * 默认角色ID
     */
    private Long defaultRoleId;
    
    /**
     * 访问令牌
     */
    private String accessToken;
    
    /**
     * 令牌类型
     */
    private String tokenType;
    
    /**
     * 访问令牌过期时间（秒）
     */
    private Integer expiresIn;
    
    /**
     * 刷新令牌
     */
    private String refreshToken;
    
    /**
     * 权限范围
     */
    private String scope;
}