import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { ArrowLeft, Building2, User, Phone, Mail, MapPin, FileText, CreditCard, Landmark, Loader2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { MobileHeader } from '@/components/mobile/MobileHeader';
import PartnerAPI, { PartnerVO } from '@/api/partner';
import { toast } from 'sonner';

const getPartnerTypeLabel = (type: string) => {
  const types: { [key: string]: string } = {
    'customer': '客户',
    'supplier': '供应商',
    'partner': '合作伙伴',
    'other': '其他'
  };
  return types[type] || type;
};

const getPartnerTypeColor = (type: string) => {
  const colors: { [key: string]: string } = {
    'customer': 'bg-blue-100 text-blue-800',
    'supplier': 'bg-green-100 text-green-800',
    'partner': 'bg-purple-100 text-purple-800',
    'other': 'bg-gray-100 text-gray-800'
  };
  return colors[type] || 'bg-gray-100 text-gray-800';
};

const getStatusLabel = (status: string) => {
  return status === 'active' ? '正常' : '禁用';
};

const getStatusColor = (status: string) => {
  return status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
};

const getCertificateTypeLabel = (type: string) => {
  const types: { [key: string]: string } = {
    'business_license': '营业执照',
    'organization_code': '组织机构代码',
    'tax_registration': '税务登记证',
    'other': '其他'
  };
  return types[type] || type;
};

const PartnerDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [partner, setPartner] = useState<PartnerVO | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPartnerDetail = async () => {
      if (!id) {
        setError('业务伙伴ID不能为空');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const partnerId = parseInt(id, 10);
        if (isNaN(partnerId)) {
          throw new Error('无效的业务伙伴ID');
        }
        
        const response = await PartnerAPI.getPartnerDetail(partnerId);
        setPartner(response);
      } catch (err: any) {
        const errorMessage = err?.message || '获取业务伙伴详情失败';
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchPartnerDetail();
  }, [id]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  // 加载状态
  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <MobileHeader 
          title="伙伴详情" 
          showBack 
          onBack={() => navigate(-1)}
          showLogo={false}
        />
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center gap-2 text-muted-foreground">
            <Loader2 className="w-5 h-5 animate-spin" />
            <span>加载中...</span>
          </div>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error || !partner) {
    return (
      <div className="min-h-screen bg-background">
        <MobileHeader 
          title="伙伴详情" 
          showBack 
          onBack={() => navigate(-1)}
          showLogo={false}
        />
        <div className="flex flex-col items-center justify-center h-64 p-4">
          <div className="text-center">
            <div className="text-lg font-medium text-foreground mb-2">加载失败</div>
            <div className="text-muted-foreground mb-4">{error || '业务伙伴不存在'}</div>
            <button 
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
            >
              重新加载
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <MobileHeader 
        title="伙伴详情" 
        showBack 
        onBack={() => navigate(-1)}
        showLogo={false}
      />

      <div className="p-4 space-y-6">
        {/* 基本信息卡片 */}
        <div className="bg-card rounded-lg p-4 shadow-soft">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <h1 className="text-xl font-semibold text-foreground mb-2">
                {partner.partnerName}
              </h1>
              <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                <span>伙伴编码: {partner.partnerCode || '暂无'}</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge className={getPartnerTypeColor(partner.partnerType)}>
                  {partner.partnerTypeLabel || getPartnerTypeLabel(partner.partnerType)}
                </Badge>
                <Badge className={getStatusColor(partner.status)}>
                  {getStatusLabel(partner.status)}
                </Badge>
                {partner.isOurCompany && (
                  <Badge className="bg-amber-100 text-amber-800">
                    我司企业
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 联系信息 */}
        <div className="bg-card rounded-lg p-4 shadow-soft">
          <h2 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
            <User className="w-5 h-5" />
            联系信息
          </h2>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Building2 className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">法定代表人</div>
                <div className="text-foreground">{partner.legalRepresentative || '暂无'}</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <User className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">联系人</div>
                <div className="text-foreground">{partner.contactPerson || '暂无'}</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Phone className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">联系电话</div>
                <div className="text-foreground">{partner.contactPhone || '暂无'}</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Mail className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">联系邮箱</div>
                <div className="text-foreground">{partner.contactEmail || '暂无'}</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <MapPin className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">地址</div>
                <div className="text-foreground">{partner.address || '暂无'}</div>
              </div>
            </div>
          </div>
        </div>

        {/* 证件信息 */}
        <div className="bg-card rounded-lg p-4 shadow-soft">
          <h2 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
            <FileText className="w-5 h-5" />
            证件信息
          </h2>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <CreditCard className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">证件类型</div>
                <div className="text-foreground">
                  {partner.certificateTypeLabel || getCertificateTypeLabel(partner.certificateType || '')}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <FileText className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">证件号码</div>
                <div className="text-foreground">{partner.certificateNumber || '暂无'}</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <FileText className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">税号</div>
                <div className="text-foreground">{partner.taxNumber || '暂无'}</div>
              </div>
            </div>
          </div>
        </div>

        {/* 银行信息 */}
        <div className="bg-card rounded-lg p-4 shadow-soft">
          <h2 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
            <Landmark className="w-5 h-5" />
            银行信息
          </h2>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Landmark className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">开户银行</div>
                <div className="text-foreground">{partner.bankName || '暂无'}</div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <CreditCard className="w-4 h-4 text-muted-foreground" />
              <div>
                <div className="text-sm text-muted-foreground">银行账号</div>
                <div className="text-foreground">{partner.bankAccount || '暂无'}</div>
              </div>
            </div>
          </div>
        </div>

        {/* 备注信息 */}
        {partner.remark && (
          <div className="bg-card rounded-lg p-4 shadow-soft">
            <h2 className="text-lg font-semibold text-foreground mb-4">备注</h2>
            <div className="text-muted-foreground">{partner.remark}</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PartnerDetailPage;