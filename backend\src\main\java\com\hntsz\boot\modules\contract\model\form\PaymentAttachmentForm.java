package com.hntsz.boot.modules.contract.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * 付款文件关联表单
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Getter
@Setter
@Schema(description = "付款文件关联表单")
public class PaymentAttachmentForm {

    @Schema(description = "付款ID")
    @NotNull(message = "付款ID不能为空")
    private Long paymentId;

    @Schema(description = "附件ID")
    @NotNull(message = "附件ID不能为空")
    private Long attachmentId;

    @Schema(description = "排序")
    private Integer sort;
}