package com.hntsz.boot.system.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * OAuth 令牌实体
 *
 * <AUTHOR>
 * @since 2.22.0
 */
@Data
@TableName("oauth_token")
public class OAuthToken {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * OAuth 提供商
     */
    private String provider;

    /**
     * OAuth 用户ID
     */
    private String oauthId;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 令牌类型
     */
    private String tokenType;

    /**
     * 过期时间（秒）
     */
    private Integer expiresIn;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 权限范围
     */
    private String scope;

    /**
     * 过期时间
     */
    private LocalDateTime expiresAt;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}