package com.hntsz.boot.common.base;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hntsz.boot.common.result.Result;

import com.hntsz.boot.common.result.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.web.bind.annotation.*;
import com.hntsz.boot.common.contract.ISortOrder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hntsz.boot.common.contract.ITopOrder;
import java.util.Arrays;
/**
 * 通用 CRUD 控制器
 *
 * @param <T> 实体类型
 * @param <V> VO类型
 * @param <CF> Create Form类型
 * @param <UF> Update Form类型
 * @param <M> Mapper 类型
 * @param <C> Converter 类型
 */
public abstract class BaseCrudController<T, Q extends BasePageQuery, V, D, CF, UF, M extends BaseMapper<T>> 
    extends ContractCrudController<T, Q, V, D, CF, UF> {

    protected final M baseMapper;

    protected BaseCrudController(M baseMapper) {
        this.baseMapper = baseMapper;
    }

    @Override
    protected PageResult<V> listImplement(Q query) {

        Page<T> page = new Page<>(query.getPageNum(), query.getPageSize());
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();

        buildSortOrder(queryWrapper);

        LambdaQueryWrapper<T> lambdaQueryWrapper = queryWrapper.lambda();
        
        if(query != null) {
            buildQueryWrapper(lambdaQueryWrapper, query);
        }

        IPage<T> dataPage = baseMapper.selectPage(page, lambdaQueryWrapper);
        return PageResult.success(convertToVOPage(dataPage));
    }


    @Override
    protected Result<D> getImplement(Long id) {
        T entity = baseMapper.selectById(id);
        return Result.success(convertToDetail(entity));
    }

    @Override
    protected Result<?> addImplement(CF form) {
        T entity = convertCfToEntity(form);
        int result = baseMapper.insert(entity);
        return Result.judge(result > 0);
    }

    @Override
    protected Result<?> updateImplement(Long id, UF form) {
        T entity = convertUfToEntity(form);
        setId(entity, id);
        int result = baseMapper.updateById(entity);
        return Result.judge(result > 0);
    }

    @Override
    protected Result<?> deleteImplement(Long[] ids) {
        int result = baseMapper.deleteBatchIds(Arrays.asList(ids));
        return Result.judge(result > 0);
    }

    /**
     * 构建查询条件
     *
     * @param queryWrapper 查询包装器
     * @param keyword 关键字
     */
    protected void buildQueryWrapper(LambdaQueryWrapper<T> queryWrapper, Q query) {
        // 子类可以重写此方法来自定义查询条件
    }

    /**
     * 构建排序条件
     * @param queryWrapper 查询包装器
     */
    protected void buildSortOrder(QueryWrapper<T> queryWrapper) {
        // 获取实体类的Class对象
        @SuppressWarnings("unchecked")
        Class<T> entityClass = (Class<T>) ((java.lang.reflect.ParameterizedType) getClass()
                .getGenericSuperclass()).getActualTypeArguments()[0];
        
        // 如果实体实现了ITopOrder接口，则按照置顶字段排序
        if (ITopOrder.class.isAssignableFrom(entityClass)) {
            queryWrapper.orderByDesc("is_top");
        }

        // 如果实体实现了ISortOrder接口，则按照排序字段排序
        if (ISortOrder.class.isAssignableFrom(entityClass)) {
            queryWrapper.orderByAsc("sort_order");
        }
    }

    /**
     * 设置实体ID
     *
     * @param entity 实体对象
     * @param id ID
     */
    protected void setId(T entity, Long id) {

        // 获取实体类的Class对象
        @SuppressWarnings("unchecked")
        Class<T> entityClass = (Class<T>) ((java.lang.reflect.ParameterizedType) getClass()
                .getGenericSuperclass()).getActualTypeArguments()[0];

        if(BaseEntity.class.isAssignableFrom(entityClass)) {

            try {
                BaseEntity baseEntity = (BaseEntity) entity;
                baseEntity.setId(id);
            } catch (Exception e) {
                throw new RuntimeException("设置实体ID失败", e);
            }

        } else {
            throw new RuntimeException("实体类无法设置ID");
        }
    }

    /**
     * 将实体转换为VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    protected abstract V convertToVO(T entity);

    /**
     * 将实体转换为详情
     *
     * @param entity 实体对象
     * @return 详情对象
     */
    protected abstract D convertToDetail(T entity);

    /**
     * 将Create Form转换为实体
     *
     * @param form Form对象
     * @return 实体对象
     */
    protected abstract T convertCfToEntity(CF form);

    /**
     * 将Update Form转换为实体
     *
     * @param form Update Form对象
     * @return 实体对象
     */
    protected abstract T convertUfToEntity(UF form);
    
    /**
     * 将实体分页转换为VO分页
     *
     * @param entityPage 实体分页
     * @return VO分页
     */
    protected IPage<V> convertToVOPage(IPage<T> entityPage) {
        return entityPage.convert(this::convertToVO);
    }
} 