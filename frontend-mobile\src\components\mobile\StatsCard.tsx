import { LucideIcon } from "lucide-react";

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  gradient?: boolean;
  onClick?: () => void;
}

export function StatsCard({ title, value, icon: Icon, trend, gradient, onClick }: StatsCardProps) {
  return (
    <div 
      className={`
        rounded-lg p-4 shadow-soft 
        ${gradient ? 'bg-gradient-secondary text-white' : 'bg-card'}
        ${onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''}
      `}
      onClick={onClick}
    >
      <div className="flex items-center justify-between mb-3">
        <div className={`p-2 rounded-lg ${gradient ? 'bg-white/20' : 'bg-primary/10'}`}>
          <Icon className={`h-5 w-5 ${gradient ? 'text-white' : 'text-primary'}`} />
        </div>
        {trend && (
          <div className={`text-sm font-medium ${
            gradient 
              ? 'text-white/90' 
              : trend.isPositive 
                ? 'text-success' 
                : 'text-destructive'
          }`}>
            {trend.isPositive ? '+' : ''}{trend.value}%
          </div>
        )}
      </div>
      <div className="space-y-1">
        <p className={`text-2xl font-bold ${gradient ? 'text-white' : 'text-foreground'}`}>
          {value}
        </p>
        <p className={`text-sm ${gradient ? 'text-white/80' : 'text-muted-foreground'}`}>
          {title}
        </p>
      </div>
    </div>
  );
}