package com.hntsz.boot.modules.opportunity.controller;

import com.hntsz.boot.common.result.PageResult;
import com.hntsz.boot.common.result.Result;
import com.hntsz.boot.modules.opportunity.model.form.OpportunityFollowForm;
import com.hntsz.boot.modules.opportunity.model.query.OpportunityFollowQuery;
import com.hntsz.boot.modules.opportunity.model.vo.OpportunityFollowVO;
import com.hntsz.boot.modules.opportunity.service.OpportunityFollowService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 商机跟进记录控制器
 *
 * <AUTHOR>
 */
@Tag(name = "商机跟进记录管理")
@RestController
@RequestMapping("/api/v1/opportunity-follows")
@RequiredArgsConstructor
@Validated
public class OpportunityFollowController {

    private final OpportunityFollowService opportunityFollowService;

    @Operation(summary = "分页查询跟进记录")
    @GetMapping("/page")
    @PreAuthorize("@ss.hasPerm('opportunity:follow:query')")
    public PageResult<OpportunityFollowVO> getFollowPage(OpportunityFollowQuery queryParams) {
        return opportunityFollowService.getFollowPage(queryParams);
    }

    @Operation(summary = "获取跟进记录详情")
    @GetMapping("/{id}")
    @PreAuthorize("@ss.hasPerm('opportunity:follow:query')")
    public Result<OpportunityFollowVO> getFollowDetail(
            @Parameter(description = "跟进记录ID") @PathVariable Long id) {
        OpportunityFollowVO follow = opportunityFollowService.getFollowById(id);
        return Result.success(follow);
    }

    @Operation(summary = "获取跟进记录表单数据")
    @GetMapping("/{id}/form")
    @PreAuthorize("@ss.hasPerm('opportunity:follow:edit')")
    public Result<OpportunityFollowForm> getFollowFormData(
            @Parameter(description = "跟进记录ID") @PathVariable Long id) {
        OpportunityFollowForm formData = opportunityFollowService.getFollowFormData(id);
        return Result.success(formData);
    }

    @Operation(summary = "新增跟进记录")
    @PostMapping
    @PreAuthorize("@ss.hasPerm('opportunity:follow:add')")
    public Result<Void> createFollow(@Valid @RequestBody OpportunityFollowForm formData) {
        boolean result = opportunityFollowService.saveFollow(formData);
        return Result.judge(result);
    }

    @Operation(summary = "修改跟进记录")
    @PutMapping("/{id}")
    @PreAuthorize("@ss.hasPerm('opportunity:follow:edit')")
    public Result<Void> updateFollow(
            @Parameter(description = "跟进记录ID") @PathVariable Long id,
            @Valid @RequestBody OpportunityFollowForm formData) {
        boolean result = opportunityFollowService.updateFollow(id, formData);
        return Result.judge(result);
    }

    @Operation(summary = "删除跟进记录")
    @DeleteMapping("/{ids}")
    @PreAuthorize("@ss.hasPerm('opportunity:follow:delete')")
    public Result<Void> deleteFollows(
            @Parameter(description = "跟进记录ID，多个以英文逗号(,)分割") @PathVariable String ids) {
        boolean result = opportunityFollowService.deleteFollows(ids);
        return Result.judge(result);
    }

    @Operation(summary = "根据商机ID查询跟进记录列表")
    @GetMapping("/opportunity/{opportunityId}")
    @PreAuthorize("@ss.hasPerm('opportunity:follow:query')")
    public Result<List<OpportunityFollowVO>> getFollowsByOpportunityId(
            @Parameter(description = "商机ID") @PathVariable Long opportunityId) {
        List<OpportunityFollowVO> follows = opportunityFollowService.getFollowsByOpportunityId(opportunityId);
        return Result.success(follows);
    }
}