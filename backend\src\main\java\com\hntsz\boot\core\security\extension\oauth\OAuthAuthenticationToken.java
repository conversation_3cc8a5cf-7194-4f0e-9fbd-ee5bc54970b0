package com.hntsz.boot.core.security.extension.oauth;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * OAuth 认证 Token
 * 
 * <AUTHOR>
 * @since 2.22.0
 */
public class OAuthAuthenticationToken extends AbstractAuthenticationToken {

    private final Object principal;
    private final String provider;
    private final String authorizationCode;

    /**
     * 创建未认证的 OAuth Token（用于认证请求）
     * 
     * @param provider OAuth 提供商
     * @param authorizationCode 授权码
     */
    public OAuthAuthenticationToken(String provider, String authorizationCode) {
        super(null);
        this.provider = provider;
        this.authorizationCode = authorizationCode;
        this.principal = null;
        setAuthenticated(false);
    }

    /**
     * 创建已认证的 OAuth Token（认证成功后）
     * 
     * @param principal 用户主体
     * @param authorities 权限集合
     * @param provider OAuth 提供商
     */
    public OAuthAuthenticationToken(Object principal, Collection<? extends GrantedAuthority> authorities, String provider) {
        super(authorities);
        this.principal = principal;
        this.provider = provider;
        this.authorizationCode = null;
        setAuthenticated(true);
    }

    /**
     * 静态工厂方法：创建已认证的 Token
     */
    public static OAuthAuthenticationToken authenticated(Object principal, Collection<? extends GrantedAuthority> authorities, String provider) {
        return new OAuthAuthenticationToken(principal, authorities, provider);
    }

    @Override
    public Object getCredentials() {
        return authorizationCode;
    }

    @Override
    public Object getPrincipal() {
        return principal;
    }

    public String getProvider() {
        return provider;
    }

    public String getAuthorizationCode() {
        return authorizationCode;
    }

    @Override
    public void eraseCredentials() {
        super.eraseCredentials();
        // 清除敏感信息
    }
}