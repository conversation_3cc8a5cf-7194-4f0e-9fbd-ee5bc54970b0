<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="关键字" prop="keywords">
          <el-input
            v-model="queryParams.keywords"
            placeholder="合同编号/合同名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="合同类型" prop="contractType">
          <el-select v-model="queryParams.contractType" placeholder="请选择" clearable style="width: 150px">
            <el-option
              v-for="item in contractTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="合同状态" prop="contractStatus">
          <el-select v-model="queryParams.contractStatus" placeholder="请选择" clearable style="width: 150px">
            <el-option label="草稿" value="draft" />
            <el-option label="待签署" value="pending" />
            <el-option label="已生效" value="active" />
            <el-option label="已完成" value="completed" />
            <el-option label="已终止" value="terminated" />
            <el-option label="已作废" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <template #header>
        <div class="flex justify-between">
          <div class="flex items-center space-x-2">
            <el-button type="success" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              新增
            </el-button>
            <el-button type="danger" :disabled="ids.length === 0" @click="handleDelete()">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="合同编号" prop="contractNo" width="150" />
        <el-table-column label="合同名称" prop="contractName" min-width="200" />
        <el-table-column label="合同类型" prop="contractTypeLabel" width="120" />
        <el-table-column label="合同金额" prop="contractAmount" width="120" align="right">
          <template #default="scope">
            <span v-if="scope.row.contractAmount">
              ¥{{ scope.row.contractAmount.toLocaleString() }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="关键日期" width="150">
          <template #default="scope">
            <div class="contract-dates">
              <div v-if="scope.row.signingDate" class="date-item">
                <span class="date-label">签署:</span>
                <span class="date-value">{{ scope.row.signingDate }}</span>
              </div>
              <div v-if="scope.row.effectiveDate" class="date-item">
                <span class="date-label">生效:</span>
                <span class="date-value">{{ scope.row.effectiveDate }}</span>
              </div>
              <div v-if="scope.row.expiryDate" class="date-item">
                <span class="date-label">到期:</span>
                <span class="date-value">{{ scope.row.expiryDate }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="合同状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.contractStatus)">
              {{ getStatusLabel(scope.row.contractStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="关联商机" width="150">
          <template #default="scope">
            <div class="opportunity-info">
              <el-link 
                v-if="scope.row.opportunityName"
                type="primary" 
                size="small"
                @click="handleViewOpportunity(scope.row.opportunityId)"
                :title="scope.row.opportunityName"
              >
                {{ scope.row.opportunityName }}
              </el-link>
              <span v-else class="text-gray-400">暂无</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="收付款" width="120" align="center">
          <template #default="scope">
            <div class="payment-amount">
              <el-button 
                link 
                :type="getPaymentAmountType(scope.row.totalPaymentAmount)"
                size="small" 
                @click="handlePayment(scope.row.id)"
              >
                {{ formatPaymentAmount(scope.row.totalPaymentAmount) }}
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="伙伴" width="200">
          <template #default="scope">
            <div class="contract-parties">
              <el-tag
                v-for="(partner, index) in scope.row.parties"
                :key="partner.id || index"
                type="info"
                size="small"
                class="partner-tag"
                :title="`${partner.partnerName} (${partner.partnerRoleLabel || partner.partnerRole})`"
              >
                {{ partner.partnerName }}
              </el-tag>
              <span v-if="!scope.row.parties || scope.row.parties.length === 0" class="text-gray-400">
                暂无
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="负责人" prop="responsibleUserName" width="100" />
        <el-table-column label="创建时间" prop="createTime" width="180" />
        <el-table-column label="操作" width="280" align="center" fixed="right">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="handleEdit(scope.row.id)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button link type="primary" size="small" @click="handleDetail(scope.row.id)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button link type="success" size="small" @click="handlePayment(scope.row.id)">
              <el-icon><Money /></el-icon>
              付款记录
            </el-button>
            <el-button link type="danger" size="small" @click="handleDelete(scope.row.id)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <!-- 合同表单抽屉 -->
    <contract-form-drawer
      v-model="formDrawerVisible"
      :contract-id="currentContractId"
      @success="handleQuery"
    />

    <!-- 合同详情抽屉 -->
    <contract-detail-drawer
      v-model="detailDrawerVisible"
      :contract-id="currentContractId"
      @edit="handleEdit"
      @delete="handleDelete"
      @payment="handlePayment"
    />

  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import {
  Search,
  Refresh,
  Plus,
  Delete,
  Edit,
  View,
  Money,
} from "@element-plus/icons-vue";
import {
  ContractPageQuery,
  ContractPageVO,
} from "@/api/contract/contract.api";
import ContractAPI from "@/api/contract/contract.api";
import ContractFormDrawer from "./ContractFormDrawer.vue"; // 引入表单抽屉组件
import ContractDetailDrawer from "./ContractDetailDrawer.vue"; // 引入详情抽屉组件
import { useDictStore } from "@/store/modules/dict.store";

defineOptions({
  name: "Contract",
  inheritAttrs: false,
});

const router = useRouter();
const dictStore = useDictStore();

const queryFormRef = ref(ElForm);

const loading = ref(false);
const ids = ref<string[]>([]);
const total = ref(0);

const queryParams = reactive<ContractPageQuery>({
  pageNum: 1,
  pageSize: 10,
});

const pageData = ref<ContractPageVO[]>([]);

const formDrawerVisible = ref(false); // 控制表单抽屉显示/隐藏
const detailDrawerVisible = ref(false); // 控制详情抽屉显示/隐藏
const currentContractId = ref<string | undefined>(undefined); // 当前编辑的合同ID

const contractTypeOptions = ref<OptionType[]>([]);

function getStatusType(status: string): "info" | "warning" | "success" | "danger" {
  const statusMap: Record<string, "info" | "warning" | "success" | "danger"> = {
    draft: "info",
    pending: "warning",
    active: "success",
    completed: "success",
    terminated: "danger",
    cancelled: "danger",
  };
  return statusMap[status] || "info";
}

function getStatusLabel(status: string) {
  const statusMap: Record<string, string> = {
    draft: "草稿",
    pending: "待签署",
    active: "已生效",
    completed: "已完成",
    terminated: "已终止",
    cancelled: "已作废",
  };
  return statusMap[status] || status;
}

function formatPaymentAmount(amount: number): string {
  if (!amount) return "¥0";
  
  const absAmount = Math.abs(amount);
  const prefix = amount >= 0 ? "+" : "-";
  
  return `${prefix}¥${absAmount.toLocaleString()}`;
}

function getPaymentAmountType(amount: number): "success" | "danger" | "info" {
  if (!amount) return "info";
  return amount > 0 ? "success" : "danger";
}

function handleQuery() {
  loading.value = true;
  ContractAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

function resetQuery() {
  queryFormRef.value.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}

function handleSelectionChange(selection: ContractPageVO[]) {
  ids.value = selection.map((item) => item.id);
}

function handleAdd() {
  currentContractId.value = undefined; // 新增时清空ID
  formDrawerVisible.value = true; // 显示表单抽屉
}

function handleEdit(id: string) {
  currentContractId.value = id; // 编辑时设置ID
  formDrawerVisible.value = true; // 显示表单抽屉
}


function handleDelete(id?: string) {
  const deleteIds = id ? [id] : ids.value;
  if (!deleteIds.length) {
    ElMessage.warning("请选择删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    ContractAPI.delete(deleteIds.join(",")).then(() => {
      ElMessage.success("删除成功");
      handleQuery();
    });
  });
}

function handleDetail(id: string) {
  currentContractId.value = id; // 设置当前合同ID
  detailDrawerVisible.value = true; // 显示详情抽屉
}


function handlePayment(contractId: string) {
  router.push(`/contract/payment/${contractId}`);
}

function handleViewOpportunity(opportunityId: string) {
  if (opportunityId) {
    router.push(`/opportunity/follow/${opportunityId}`);
  }
}


onMounted(async () => {
  await dictStore.loadDictItems("contract_type");
  contractTypeOptions.value = dictStore.getDictItems("contract_type");
  handleQuery();
});
</script>

<style scoped>
.contract-dates {
  display: flex;
  flex-direction: column;
  gap: 1px;
  line-height: 1.2;
}

.date-item {
  display: flex;
  align-items: center;
  font-size: 11px;
  padding: 1px 0;
}

.date-label {
  color: #666;
  font-weight: 500;
  margin-right: 3px;
  width: 28px;
  flex-shrink: 0;
}

.date-value {
  color: #333;
  font-size: 11px;
}

.contract-parties {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: flex-start;
}

.partner-tag {
  margin: 0;
  max-width: 100%;
  word-break: break-all;
}

.payment-amount {
  text-align: center;
}
</style>
