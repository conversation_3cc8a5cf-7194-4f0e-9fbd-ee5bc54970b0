/** 跟进结果枚举 */
export const enum OpportunityFollowResult {
  /** 积极响应 */
  POSITIVE = "positive",
  /** 有兴趣 */
  INTERESTED = "interested",
  /** 考虑中 */
  CONSIDERING = "considering",
  /** 需要更多信息 */
  NEED_MORE_INFO = "need_more_info",
  /** 价格有异议 */
  PRICE_CONCERN = "price_concern",
  /** 无回应 */
  NO_RESPONSE = "no_response",
  /** 拒绝 */
  REJECTED = "rejected",
  /** 延期 */
  POSTPONED = "postponed",
  /** 其他 */
  OTHER = "other",
}

/** 跟进结果对应颜色配置 */
export const OpportunityFollowResultColorMap = {
  [OpportunityFollowResult.POSITIVE]: "success",
  [OpportunityFollowResult.INTERESTED]: "primary",
  [OpportunityFollowResult.CONSIDERING]: "info",
  [OpportunityFollowResult.NEED_MORE_INFO]: "warning",
  [OpportunityFollowResult.PRICE_CONCERN]: "danger",
  [OpportunityFollowResult.NO_RESPONSE]: "info",
  [OpportunityFollowResult.REJECTED]: "destructive",
  [OpportunityFollowResult.POSTPONED]: "warning",
  [OpportunityFollowResult.OTHER]: "info",
};
