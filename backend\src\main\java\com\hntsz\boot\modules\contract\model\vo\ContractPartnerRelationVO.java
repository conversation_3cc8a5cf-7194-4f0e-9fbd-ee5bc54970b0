package com.hntsz.boot.modules.contract.model.vo;

import com.hntsz.boot.common.base.BaseVO;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 合同伙伴关联表视图对象
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@Setter
public class ContractPartnerRelationVO extends BaseVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 合同ID
     */
    private Long contractId;

    /**
     * 伙伴ID
     */
    private Long partnerId;

    /**
     * 伙伴名称
     */
    private String partnerName;

    /**
     * 伙伴编码
     */
    private String partnerCode;

    /**
     * 伙伴类型
     */
    private String partnerType;

    /**
     * 伙伴类型标签
     */
    private String partnerTypeLabel;

    /**
     * 伙伴角色(关联字典编码：partner_role)
     */
    private String partnerRole;

    /**
     * 伙伴角色标签
     */
    private String partnerRoleLabel;

    /**
     * 角色描述(关联字典编码：partner_role_desc)
     */
    private String partnerRoleDesc;

    /**
     * 角色描述标签
     */
    private String partnerRoleDescLabel;

    /**
     * 签署人
     */
    private String signingPerson;

    /**
     * 签署人职务
     */
    private String signingPersonTitle;

    /**
     * 签署日期
     */
    private LocalDate signingDate;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
