import request from '@/utils/request';

const PARTNER_BASE_URL = "/api/v1/partners";

const PartnerAPI = {
  /**
   * 获取业务伙伴分页列表
   */
  getPartnerPage(params: PartnerQuery) {
    return request<any, PageResult<PartnerVO>>({
      url: `${PARTNER_BASE_URL}/page`,
      method: "get",
      params,
    });
  },

  /**
   * 获取业务伙伴详情
   */
  getPartnerDetail(id: number) {
    return request<any, PartnerVO>({
      url: `${PARTNER_BASE_URL}/${id}`,
      method: "get",
    });
  },

  /**
   * 获取业务伙伴选项列表
   */
  getPartnerOptions() {
    return request<any, PartnerVO[]>({
      url: `${PARTNER_BASE_URL}/options`,
      method: "get",
    });
  },
};

export default PartnerAPI;

/** 业务伙伴查询参数 */
export interface PartnerQuery {
  pageNum?: number;
  pageSize?: number;
  keywords?: string;
  partnerType?: string;
  isOurCompany?: boolean;
  status?: string;
  certificateType?: string;
}

/** 分页结果 */
export interface PageResult<T> {
  list: T[];
  total: number;
  pageNum: number;
  pageSize: number;
  pages: number;
}

/** 业务伙伴VO */
export interface PartnerVO {
  id: number;
  partnerName: string;
  partnerCode?: string;
  isOurCompany: boolean;
  partnerType: string;
  partnerTypeLabel?: string;
  legalRepresentative?: string;
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  address?: string;
  certificateType?: string;
  certificateTypeLabel?: string;
  certificateNumber?: string;
  taxNumber?: string;
  bankName?: string;
  bankAccount?: string;
  status: string;
  remark?: string;
  createTime: string;
  updateTime: string;
}