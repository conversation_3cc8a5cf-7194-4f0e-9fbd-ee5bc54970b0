package com.hntsz.boot.core.security.extension.oauth;

/**
 * OAuth 用户信息服务接口
 * 
 * <AUTHOR>
 * @since 2.22.0
 */
public interface OAuthUserInfoService {
    
    /**
     * 根据授权码获取用户信息
     * 
     * @param provider OAuth 提供商
     * @param authorizationCode 授权码
     * @return 用户信息
     */
    OAuthUserInfo getUserInfo(String provider, String authorizationCode);
    
    /**
     * 是否支持该提供商
     * 
     * @param provider OAuth 提供商
     * @return 是否支持
     */
    boolean supports(String provider);
}