import request from "@/utils/request";
import { ContractAttachmentVO } from "@/api/contract/contract-attachment.api";

const CONTRACT_BASE_URL = "/api/v1/contracts";

const ContractAPI = {
  /**
   * 获取合同分页列表
   *
   * @param queryParams 查询参数
   * @returns 合同分页列表
   */
  getPage(queryParams: ContractPageQuery) {
    return request<any, PageResult<ContractPageVO[]>>({
      url: `${CONTRACT_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 获取合同详情
   *
   * @param id 合同ID
   * @returns 合同详情
   */
  getDetail(id: string) {
    return request<any, ContractVO>({
      url: `${CONTRACT_BASE_URL}/${id}`,
      method: "get",
    });
  },

  /**
   * 获取合同表单数据
   *
   * @param id 合同ID
   * @returns 合同表单数据
   */
  getFormData(id: string) {
    return request<any, ContractForm>({
      url: `${CONTRACT_BASE_URL}/${id}/form`,
      method: "get",
    });
  },

  /**
   * 新增合同
   *
   * @param data 合同表单数据
   * @returns 请求结果
   */
  create(data: ContractForm) {
    return request<any, string>({
      url: `${CONTRACT_BASE_URL}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 修改合同
   *
   * @param id 合同ID
   * @param data 合同表单数据
   * @returns 请求结果
   */
  update(id: string, data: ContractForm) {
    return request({
      url: `${CONTRACT_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 删除合同
   *
   * @param ids 合同ID，多个以英文逗号(,)分割
   * @returns 请求结果
   */
  delete(ids: string) {
    return request({
      url: `${CONTRACT_BASE_URL}/${ids}`,
      method: "delete",
    });
  },

  /**
   * 获取合同选项列表
   *
   * @returns 合同选项列表
   */
  getOptions() {
    return request<any, OptionType[]>({
      url: `${CONTRACT_BASE_URL}/options`,
      method: "get",
    });
  },

  /**
   * 更新合同状态
   *
   * @param id 合同ID
   * @param status 合同状态
   * @returns 请求结果
   */
  updateStatus(id: string, status: string) {
    return request({
      url: `${CONTRACT_BASE_URL}/${id}/status`,
      method: "put",
      params: { status },
    });
  },

  /**
   * 根据伙伴ID获取相关合同列表
   *
   * @param partnerId 伙伴ID
   * @returns 合同列表
   */
  getByPartnerId(partnerId: string) {
    return request<any, ContractVO[]>({
      url: `${CONTRACT_BASE_URL}/by-partner/${partnerId}`,
      method: "get",
    });
  },
};

export default ContractAPI;

/** 合同分页查询参数 */
export interface ContractPageQuery extends PageQuery {
  /** 搜索关键字 */
  keywords?: string;
  /** 合同类型 */
  contractType?: string;
  /** 合同分类 */
  contractCategory?: string;
  /** 合同状态 */
  contractStatus?: string;
  /** 负责人ID */
  responsibleUserId?: string;
  /** 所属部门ID */
  deptId?: string;
  /** 签署日期范围 */
  signingDateRange?: [string, string];
  /** 生效日期范围 */
  effectiveDateRange?: [string, string];
  /** 到期日期范围 */
  expiryDateRange?: [string, string];
  /** 合同金额范围 */
  contractAmountRange?: [number, number];
  /** 伙伴ID */
  partnerId?: string;
  /** 商机ID */
  opportunityId?: string;
}

/** 合同分页VO */
export interface ContractPageVO {
  /** 主键ID */
  id: string;
  /** 合同编号 */
  contractNo: string;
  /** 合同名称 */
  contractName: string;
  /** 合同类型 */
  contractType?: string;
  /** 合同类型标签 */
  contractTypeLabel?: string;
  /** 合同分类 */
  contractCategory?: string;
  /** 合同分类标签 */
  contractCategoryLabel?: string;
  /** 合同金额 */
  contractAmount?: number;
  /** 签署日期 */
  signingDate?: string;
  /** 生效日期 */
  effectiveDate?: string;
  /** 到期日期 */
  expiryDate?: string;
  /** 合同状态 */
  contractStatus: string;
  /** 负责人姓名 */
  responsibleUserName?: string;
  /** 所属部门名称 */
  deptName?: string;
  /** 收付款总金额 */
  totalPaymentAmount?: number;
  /** 伙伴列表 */
  parties?: ContractPartnerRelationVO[];
  /** 关联商机ID */
  opportunityId?: string;
  /** 关联商机名称 */
  opportunityName?: string;
  /** 创建时间 */
  createTime: string;
}

/** 合同详情VO */
export interface ContractVO {
  /** 主键ID */
  id: string;
  /** 合同编号 */
  contractNo: string;
  /** 合同名称 */
  contractName: string;
  /** 合同类型 */
  contractType?: string;
  /** 合同类型标签 */
  contractTypeLabel?: string;
  /** 合同分类 */
  contractCategory?: string;
  /** 合同分类标签 */
  contractCategoryLabel?: string;
  /** 合同金额 */
  contractAmount?: number;
  /** 关联商机ID */
  opportunityId?: string;
  /** 关联商机名称 */
  opportunityName?: string;
  /** 签署日期 */
  signingDate?: string;
  /** 生效日期 */
  effectiveDate?: string;
  /** 到期日期 */
  expiryDate?: string;
  /** 合同状态 */
  contractStatus: string;
  /** 付款方式 */
  paymentMethod?: string;
  /** 付款方式标签 */
  paymentMethodLabel?: string;
  /** 签署地点 */
  signingLocation?: string;
  /** 负责人ID */
  responsibleUserId?: string;
  /** 负责人姓名 */
  responsibleUserName?: string;
  /** 所属部门ID */
  deptId?: string;
  /** 所属部门名称 */
  deptName?: string;
  /** 备注 */
  remark?: string;
  /** 合同伙伴列表 */
  parties?: ContractPartnerRelationVO[];
  /** 合同文件列表 */
  attachments?: ContractAttachmentVO[];
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
}

/** 合同伙伴关联VO */
export interface ContractPartnerRelationVO {
  /** 主键ID */
  id: string;
  /** 合同ID */
  contractId: string;
  /** 伙伴ID */
  partnerId: string;
  /** 伙伴名称 */
  partnerName: string;
  /** 伙伴角色 */
  partnerRole: string;
  /** 伙伴角色标签 */
  partnerRoleLabel?: string;
  /** 角色描述 */
  partnerRoleDesc?: string;
  /** 角色描述标签 */
  partnerRoleDescLabel?: string;
  /** 签署人 */
  signingPerson?: string;
  /** 签署人职务 */
  signingPersonTitle?: string;
  /** 签署日期 */
  signingDate?: string;
  /** 排序 */
  sort?: number;
  /** 备注 */
  remark?: string;
}

/** 合同表单对象 */
export interface ContractForm {
  /** 主键ID */
  id?: string;
  /** 合同编号 */
  contractNo: string;
  /** 合同名称 */
  contractName: string;
  /** 合同类型 */
  contractType?: string;
  /** 合同分类 */
  contractCategory?: string;
  /** 合同金额 */
  contractAmount?: number;
  /** 关联商机ID */
  opportunityId?: string;
  /** 签署日期 */
  signingDate?: string;
  /** 生效日期 */
  effectiveDate?: string;
  /** 到期日期 */
  expiryDate?: string;
  /** 合同状态 */
  contractStatus?: string;
  /** 付款方式 */
  paymentMethod?: string;
  /** 合同文件Id */
  attachmentId?: string;
  /** 签署地点 */
  signingLocation?: string;
  /** 负责人ID */
  responsibleUserId?: string;
  /** 所属部门ID */
  deptId?: string;
  /** 备注 */
  remark?: string;
  /** 合同伙伴关联信息 */
  parties?: ContractPartnerRelationForm[];
}

/** 合同伙伴关联表单对象 */
export interface ContractPartnerRelationForm {
  /** 主键ID */
  id?: string;
  /** 合同ID */
  contractId?: string;
  /** 伙伴ID */
  partnerId: string;
  /** 伙伴角色 */
  partnerRole: string;
  /** 角色描述 */
  partnerRoleDesc?: string;
  /** 签署人 */
  signingPerson?: string;
  /** 签署人职务 */
  signingPersonTitle?: string;
  /** 签署日期 */
  signingDate?: string;
  /** 排序 */
  sort?: number;
  /** 备注 */
  remark?: string;
}
