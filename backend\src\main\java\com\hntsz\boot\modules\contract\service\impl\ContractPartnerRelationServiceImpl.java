package com.hntsz.boot.modules.contract.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hntsz.boot.modules.contract.converter.ContractPartnerRelationConverter;
import com.hntsz.boot.modules.contract.mapper.ContractPartnerRelationMapper;
import com.hntsz.boot.modules.contract.model.entity.ContractPartnerRelation;
import com.hntsz.boot.modules.contract.model.form.ContractPartnerRelationForm;
import com.hntsz.boot.modules.contract.model.vo.ContractPartnerRelationVO;
import com.hntsz.boot.modules.contract.model.vo.ContractPartnerRelationDetailVO;
import com.hntsz.boot.modules.contract.service.ContractPartnerRelationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 合同伙伴关联表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Service
@RequiredArgsConstructor
public class ContractPartnerRelationServiceImpl extends ServiceImpl<ContractPartnerRelationMapper, ContractPartnerRelation> implements ContractPartnerRelationService {

    private final ContractPartnerRelationMapper contractPartnerRelationMapper;

    @Override
    public List<ContractPartnerRelationVO> getByContractId(Long contractId) {
        return contractPartnerRelationMapper.getByContractId(contractId);
    }

    @Override
    public Map<Long, List<ContractPartnerRelationVO>> getByContractIds(List<Long> contractIds) {
        if (contractIds == null || contractIds.isEmpty()) {
            return Map.of();
        }
        
        List<ContractPartnerRelationVO> relations = contractPartnerRelationMapper.getByContractIds(contractIds);
        return relations.stream()
                .collect(Collectors.groupingBy(ContractPartnerRelationVO::getContractId));
    }

    @Override
    public List<ContractPartnerRelationDetailVO> getDetailsByContractId(Long contractId) {
        return contractPartnerRelationMapper.getDetailsByContractId(contractId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveContractPartnerRelations(Long contractId, List<ContractPartnerRelationForm> relations) {
        if (relations == null || relations.isEmpty()) {
            return true;
        }

        List<ContractPartnerRelation> entities = relations.stream()
                .map(form -> {
                    ContractPartnerRelation entity = ContractPartnerRelationConverter.INSTANCE.formToEntity(form);
                    entity.setContractId(contractId);
                    return entity;
                })
                .toList();

        return this.saveBatch(entities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByContractId(Long contractId) {
        LambdaQueryWrapper<ContractPartnerRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ContractPartnerRelation::getContractId, contractId);
        return this.remove(wrapper);
    }
}
