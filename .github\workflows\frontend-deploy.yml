name: 部署到测试环境(前端)
on:
  workflow_dispatch:
  push:
    branches: [ staging ]
    paths:
      - .github/workflows/frontend-deploy.yml
      - "frontend/**"

jobs:
  deploy:
    runs-on: ubuntu-latest

    defaults:
      run:
        shell: bash
        working-directory: frontend


    steps:
    - name: Checkout
      uses: actions/checkout@v3
      
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'

    - name: Install pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 10
        
    - name: Install dependencies
      run: pnpm install
      
    - name: Build project
      run: pnpm run build:staging
          
    - name: Upload build files
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SIT_HOST }}
        username: ${{ secrets.SIT_USERNAME }}
        password: ${{ secrets.SIT_PASSWORD }}
        source: "frontend/dist/"
        target: "/opt/tsz-integrated-platform-staging/frontend/upload-dist"

    - name: Finish deployment
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.SIT_HOST }}
        username: ${{ secrets.SIT_USERNAME }}
        password: ${{ secrets.SIT_PASSWORD }}
        script: |
          cd /opt/tsz-integrated-platform-staging/frontend
          rm -rf dist
          cp -r upload-dist/frontend/dist .
          rm -rf upload-dist
          docker compose down frontend && docker compose up -d frontend