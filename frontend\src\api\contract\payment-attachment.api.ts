import request from '@/utils/request'

export interface PaymentAttachmentVO {
  id: string
  paymentId: string
  attachmentId: string
  sort: number
  fileName: string
  filePath: string
  fileType: string
  fileSize: number
}

export interface PaymentAttachmentForm {
  paymentId: string
  attachmentId: string
  sort?: number
}

export const paymentAttachmentApi = {
  /**
   * 获取付款文件列表
   */
  getList: (paymentId: string): Promise<PaymentAttachmentVO[]> => {
    return request({
      url: `/api/v1/contract-payments/${paymentId}/attachments`,
      method: 'get'
    })
  },

  /**
   * 添加付款文件
   */
  add: (paymentId: string, data: PaymentAttachmentForm): Promise<void> => {
    return request({
      url: `/api/v1/contract-payments/${paymentId}/attachments`,
      method: 'post',
      data
    })
  },

  /**
   * 批量添加付款文件
   */
  batchAdd: (paymentId: string, attachmentIds: string[]): Promise<void> => {
    return request({
      url: `/api/v1/contract-payments/${paymentId}/attachments/batch`,
      method: 'post',
      data: attachmentIds
    })
  },

  /**
   * 删除付款文件
   */
  delete: (paymentId: string, attachmentId: string): Promise<void> => {
    return request({
      url: `/api/v1/contract-payments/${paymentId}/attachments/${attachmentId}`,
      method: 'delete'
    })
  }
}