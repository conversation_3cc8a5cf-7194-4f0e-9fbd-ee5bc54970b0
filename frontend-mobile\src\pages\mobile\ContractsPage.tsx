import { useState, useEffect, useRef, useCallback } from "react";
import { MobileHeader } from "@/components/mobile/MobileHeader";
import { BottomNavigation } from "@/components/mobile/BottomNavigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import {
  Search,
  Filter,
  Plus,
  FileText,
  Calendar,
  DollarSign,
  Building,
  Loader2
} from "lucide-react";
import ContractAPI, { ContractVO, ContractQuery } from "@/api/contract";
import { toast } from "@/components/ui/use-toast";
import { DictUtils } from '@/utils/dictionaries';

// 格式化金额
const formatAmount = (amount: number): string => {
  return `¥${amount.toLocaleString()}`;
};

// 获取主要合作伙伴名称
const getPartnerNames = (parties?: ContractVO['parties']): string[] => {
  if (!parties || parties.length === 0) return ["暂无"];
  return parties.map(p => p.partnerName || "暂无");
};

export default function ContractsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [contracts, setContracts] = useState<ContractVO[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [statistics, setStatistics] = useState({
    total: 0,
    active: 0,
    pending: 0
  });
  const [pageNum, setPageNum] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  // 加载合同列表
  const loadContracts = async (query?: string, page: number = 1, append: boolean = false) => {
    try {
      if (append) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      const params: ContractQuery = {
        pageNum: page,
        pageSize: 10, 
        keywords: query || undefined
      };

      const result = await ContractAPI.getContractPage(params);
      
      // 更新合同列表
      if (append) {
        setContracts(prev => [...prev, ...result.list]);
      } else {
        setContracts(result.list);
      }

      // 更新是否还有更多数据
      setHasMore(page * params.pageSize < result.total);

      // 只在首次加载或重新搜索时更新统计数据
      if (page === 1) {
        await loadStatistics(result.total);
      }
    } catch (error) {
      console.error('加载合同列表失败:', error);
      toast({
        variant: "destructive",
        title: "加载失败",
        description: "无法加载合同列表，请稍后重试",
      });
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // 加载统计数据
  const loadStatistics = async (totalFromPage?: number) => {
    try {
      const [totalStats, activeStats, pendingStats] = await Promise.all([
        ContractAPI.getStatistics(), // 不传参数，获取总数
        ContractAPI.getStatistics('active'),
        ContractAPI.getStatistics('pending')
      ]);
      
      setStatistics({
        total: totalStats.count, // 使用统计接口返回的总数
        active: activeStats.count,
        pending: pendingStats.count
      });
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  };

  // 处理滚动加载
  const handleScroll = useCallback(() => {
    if (!containerRef.current || loading || loadingMore || !hasMore) return;

    const container = containerRef.current;
    const scrollTop = container.scrollTop;
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;

    // 当滚动到距离底部150px时加载更多
    if (scrollHeight - scrollTop - clientHeight < 150) {
      setPageNum(prev => {
        const nextPage = prev + 1;
        return nextPage;
      });
    }
  }, [loading, loadingMore, hasMore]);

  // 监听页码变化，加载更多数据
  useEffect(() => {
    if (pageNum === 1) return;
    loadContracts(searchQuery, pageNum, true);
  }, [pageNum]);

  // 监听搜索查询变化，重新加载数据
  useEffect(() => {
    loadContracts(searchQuery, 1, false);
  }, [searchQuery]);

  // 添加滚动事件监听
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('scroll', handleScroll);
    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true);
    setPageNum(1);
    setHasMore(true);
    try {
      await Promise.all([
        loadContracts(searchQuery, 1, false),
        loadStatistics()
      ]);
    } finally {
      setRefreshing(false);
    }
  };

  // 搜索处理
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setPageNum(1);
    setHasMore(true);
  };

  const getStatusColor = (status: string) => {
    const config = DictUtils.getContractStatusConfig(status);
    return config?.color || "secondary";
  };

  const getStatusText = (status: string) => {
    return DictUtils.getContractStatusLabel(status);
  };

  const filteredContracts = contracts;

  return (
    <div className="min-h-screen bg-background pb-20" ref={containerRef} style={{ overflowY: 'auto', height: '100vh' }}>
      <MobileHeader
        title="合同管理"
        showSearch={true}
        searchPlaceholder="搜索合同..."
        onSearch={handleSearch}
        searchValue={searchQuery}
        showRefresh={true}
        onRefresh={handleRefresh}
        refreshing={refreshing}
      />

      <div className="p-4 space-y-4">
        {/* 刷新指示器 */}
        {refreshing && (
          <div className="flex items-center justify-center py-2">
            <Loader2 className="h-4 w-4 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground text-sm">刷新中...</span>
          </div>
        )}

        {/* 统计卡片 */}
        <div className="grid grid-cols-3 gap-3">
          <div className="bg-card rounded-lg p-3 text-center shadow-soft">
            <p className="text-2xl font-bold text-primary">{statistics.total}</p>
            <p className="text-xs text-muted-foreground">总数</p>
          </div>
          <div className="bg-card rounded-lg p-3 text-center shadow-soft">
            <p className="text-2xl font-bold text-success">{statistics.active}</p>
            <p className="text-xs text-muted-foreground">已生效</p>
          </div>
          <div className="bg-card rounded-lg p-3 text-center shadow-soft">
            <p className="text-2xl font-bold text-warning">{statistics.pending}</p>
            <p className="text-xs text-muted-foreground">待签署</p>
          </div>
        </div>

        {/* 合同列表 */}
        {loading && contracts.length === 0 ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">加载中...</span>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredContracts.map((contract) => (
              <div
                key={contract.id}
                className="bg-card rounded-lg p-4 shadow-soft cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => {
                  navigate(`/contracts/${contract.id}`);
                }}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <FileText className="h-4 w-4 text-primary" />
                      <h3 className="font-semibold text-sm">{contract.contractName}</h3>
                    </div>
                    <p className="text-xs text-muted-foreground mb-1">
                      编号: {contract.contractNo}
                    </p>
                  </div>
                  <Badge variant={getStatusColor(contract.contractStatus) as any}>
                    {getStatusText(contract.contractStatus)}
                  </Badge>
                </div>

                <div className="space-y-2">
                  <div className="space-y-1">
                    {getPartnerNames(contract.parties).map((name, idx) => (
                      <div key={idx} className="flex items-center gap-2 text-sm">
                        <Building className="h-3 w-3 text-muted-foreground" />
                        <span className="text-muted-foreground">{name}</span>
                      </div>
                    ))}
                  </div>

                  <div className="flex items-center gap-2 text-sm">
                    <DollarSign className="h-3 w-3 text-muted-foreground" />
                    <span className="font-medium text-primary">{formatAmount(contract.contractAmount)}</span>
                  </div>

                  {contract.signingDate && (
                    <div className="flex items-center gap-2 text-sm">
                      <Calendar className="h-3 w-3 text-muted-foreground" />
                      <span className="text-muted-foreground">
                        {contract.signingDate} - {contract.expiryDate}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {!loading && !loadingMore && filteredContracts.length === 0 && (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">未找到相关合同</p>
          </div>
        )}

        {/* 加载更多状态 */}
        {loadingMore && (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-4 w-4 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground text-sm">加载更多...</span>
          </div>
        )}

        {!loading && !loadingMore && !hasMore && contracts.length > 0 && (
          <div className="text-center py-4 text-muted-foreground text-sm">
            没有更多数据了
          </div>
        )}
      </div>

      <BottomNavigation />
    </div>
  );
}