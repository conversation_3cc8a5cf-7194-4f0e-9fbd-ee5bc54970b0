package com.hntsz.boot.modules.contract.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hntsz.boot.modules.contract.model.entity.PaymentAttachment;
import com.hntsz.boot.modules.contract.model.form.PaymentAttachmentForm;
import com.hntsz.boot.modules.contract.model.vo.PaymentAttachmentVO;

import java.util.List;

/**
 * 付款文件关联服务接口
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
public interface PaymentAttachmentService extends IService<PaymentAttachment> {

    /**
     * 根据付款ID获取文件列表
     *
     * @param paymentId 付款ID
     * @return 文件列表
     */
    List<PaymentAttachmentVO> getByPaymentId(Long paymentId);

    /**
     * 保存付款文件关联
     *
     * @param form 表单数据
     * @return 是否成功
     */
    boolean savePaymentAttachment(PaymentAttachmentForm form);

    /**
     * 批量保存付款文件关联
     *
     * @param paymentId     付款ID
     * @param attachmentIds 附件ID列表
     * @return 是否成功
     */
    boolean batchSavePaymentAttachments(Long paymentId, List<Long> attachmentIds);

    /**
     * 根据付款ID删除文件关联
     *
     * @param paymentId 付款ID
     * @return 是否成功
     */
    boolean deleteByPaymentId(Long paymentId);

    /**
     * 根据附件ID删除文件关联
     *
     * @param attachmentId 附件ID
     * @return 是否成功
     */
    boolean deleteByAttachmentId(Long attachmentId);

    /**
     * 删除特定的付款文件关联
     *
     * @param paymentId    付款ID
     * @param attachmentId 附件ID
     * @return 是否成功
     */
    boolean deletePaymentAttachment(Long paymentId, Long attachmentId);
}