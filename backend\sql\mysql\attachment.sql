DROP TABLE IF EXISTS `tsz_attachment`;
CREATE TABLE `tsz_attachment` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `file_name` varchar(255) NOT NULL COMMENT '文件名',
    `file_path` varchar(255) NOT NULL COMMENT '文件路径',
    `file_type` varchar(255) NOT NULL COMMENT '文件类型(IMAGE: 图片, VIDEO: 视频, DOCUMENT: 文档, AUDIO: 音频, OTHER: 其他)',
    `file_size` bigint DEFAULT NULL COMMENT '文件大小',
    `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` bigint DEFAULT NULL COMMENT '更新人ID',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除（0: 未删除, 1: 已删除）',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '附件表';

INSERT INTO `sys_menu` VALUES (200, 1, '0,1', '附件管理', 1, NULL, 'attachments', 'attachment/index', NULL, NULL, NULL, 1, 200, '', '', '2025-03-31 14:14:49', '2025-03-31 14:14:56', NULL);

INSERT INTO `sys_role_menu` VALUES (2, 200);