import { useEffect } from "react";
import { Loader2, AlertCircle, ArrowLeft } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useOAuth } from "@/hooks/useOAuth";

/**
 * OAuth 登录页面
 * 显示 OAuth 登录加载状态和处理授权流程
 */
export default function OAuthLoginPage() {
  const {
    loading,
    statusMessage,
    statusType,
    hasError,
    startOAuthLogin,
    goToLogin,
  } = useOAuth();

  /**
   * 页面加载时自动开始 OAuth 登录流程
   */
  useEffect(() => {
    startOAuthLogin();
  }, [startOAuthLogin]);

  /**
   * 根据状态类型获取对应的图标
   */
  const getStatusIcon = () => {
    if (hasError) {
      return <AlertCircle className="h-12 w-12 text-destructive" />;
    }
    return <Loader2 className="h-12 w-12 text-primary animate-spin" />;
  };

  /**
   * 根据状态类型获取对应的文本颜色
   */
  const getStatusTextColor = () => {
    switch (statusType) {
      case "success":
        return "text-green-600";
      case "warning":
        return "text-yellow-600";
      case "danger":
        return "text-destructive";
      default:
        return "text-muted-foreground";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 via-background to-accent/10 flex flex-col justify-center items-center p-4">
      <div className="w-full max-w-md">
        {/* OAuth 登录卡片 */}
        <div className="bg-card rounded-lg shadow-lg p-8 text-center space-y-6">
          {/* 状态图标 */}
          <div className="flex justify-center">
            {getStatusIcon()}
          </div>

          {/* 标题 */}
          <div className="space-y-2">
            <h1 className="text-2xl font-bold text-foreground">
              第三方登录
            </h1>
            <p className={`text-sm ${getStatusTextColor()}`}>
              {statusMessage || "正在初始化OAuth登录..."}
            </p>
          </div>

          {/* 错误时显示返回按钮 */}
          {hasError && (
            <div className="space-y-3">
              <Button
                onClick={goToLogin}
                className="w-full"
                variant="default"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                返回登录页面
              </Button>
            </div>
          )}

          {/* 加载状态提示 */}
          {loading && !hasError && (
            <div className="text-xs text-muted-foreground">
              <p>请稍候，正在为您跳转到授权页面...</p>
              <p className="mt-1">如果长时间未响应，请检查网络连接</p>
            </div>
          )}
        </div>

        {/* 版本信息 */}
        <div className="text-center mt-4 text-xs text-muted-foreground">
          <p>探数者综合管理平台 v1.0.0</p>
        </div>
      </div>
    </div>
  );
}