<template>
  <el-tooltip
    v-if="props.value"
    :content="useDateFormat(props.value, 'YYYY-MM-DD HH:mm:ss').value"
    placement="top"
  >
    <span class="text-xs">
      {{ formatTimeAgo(props.value) }}
    </span>
  </el-tooltip>
</template>

<script setup lang="ts">
const props = defineProps<{
  value?: Date | string;
}>();

function formatTimeAgo(date: Date | string): string {
  const now = new Date();
  const diffMs = now.getTime() - new Date(date).getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  const diffMonth = Math.floor(diffDay / 30);
  const diffYear = Math.floor(diffDay / 365);

  if (diffSec < 60) {
    return "刚刚";
  } else if (diffMin < 60) {
    return `${diffMin}分钟前`;
  } else if (diffHour < 24) {
    return `${diffHour}小时前`;
  } else if (diffDay < 30) {
    return `${diffDay}天前`;
  } else if (diffMonth < 12) {
    return `${diffMonth}个月前`;
  } else {
    return `${diffYear}年前`;
  }
}
</script>
