<template>
  <div class="oauth-callback-container">
    <div class="loading-wrapper">
      <el-icon class="is-loading" size="40">
        <Loading />
      </el-icon>
      <p class="loading-text">正在处理OAuth登录...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { Loading } from "@element-plus/icons-vue";
import { useRoute, useRouter } from "vue-router";
import AuthAPI from "@/api/auth.api";
import { useUserStore } from "@/store";
import { Auth } from "@/utils/auth";

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

// 处理 OAuth 回调
async function handleOAuthCallback() {
  try {
    const code = route.query.code as string;
    const state = route.query.state as string;
    
    if (!code || !state) {
      throw new Error('缺少必要的OAuth参数');
    }

    // 验证 state 参数
    const savedState = sessionStorage.getItem('oauth_state');
    if (state !== savedState) {
      throw new Error('无效的state参数，可能存在安全风险');
    }
    
    // 清除 state
    sessionStorage.removeItem('oauth_state');

    // 调用 OAuth 登录 API
    const loginResult = await AuthAPI.oauthLogin('oauth', code);
    
    // 存储 token
    Auth.setTokens(loginResult.accessToken, loginResult.refreshToken, false); // OAuth 登录默认不记住

    // 获取用户信息
    await userStore.getUserInfo();

    // 跳转到首页
    await router.push('/');
    
    ElMessage.success('OAuth登录成功');
  } catch (error: any) {
    console.error('OAuth 登录失败:', error);
    const errorMessage = error?.message || error?.toString() || '未知错误';
    ElMessage.error(`OAuth 登录失败: ${errorMessage}`);
    
    // 登录失败时跳转回登录页
    await router.push('/login');
  }
}

// 页面加载时立即处理回调
onMounted(() => {
  handleOAuthCallback();
});
</script>

<style scoped>
.oauth-callback-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.loading-wrapper {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.loading-text {
  margin-top: 1rem;
  color: #666;
  font-size: 16px;
}
</style>