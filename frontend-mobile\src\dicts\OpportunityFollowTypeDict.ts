// 38	follow_type	phone	电话跟进	primary	1	1	电话跟进客户	2025-07-16 03:22:28	1	2025-07-16 03:22:28	1
// 39	follow_type	email	邮件跟进	success	1	2	邮件跟进客户	2025-07-16 03:22:28	1	2025-07-16 03:22:28	1
// 40	follow_type	visit	上门拜访	info	1	3	上门拜访客户	2025-07-16 03:22:28	1	2025-07-16 03:22:28	1
// 41	follow_type	wechat	微信沟通	warning	1	4	微信沟通	2025-07-16 03:22:28	1	2025-07-16 03:22:28	1
// 42	follow_type	meeting	会议沟通		1	5	会议沟通	2025-07-16 03:22:28	1	2025-07-16 03:22:28	1
// 43	follow_type	demo	产品演示		1	6	产品演示	2025-07-16 03:22:28	1	2025-07-16 03:22:28	1
// 44	follow_type	proposal	方案提交		1	7	提交解决方案	2025-07-16 03:22:28	1	2025-07-16 03:22:28	1
// 45	follow_type	quotation	报价		1	8	提交报价	2025-07-16 03:22:28	1	2025-07-16 03:22:28	1
// 46	follow_type	other	其他		1	99	其他跟进方式	2025-07-16 03:22:28	1	2025-07-16 03:22:28	1
/** 跟进方式枚举 */
export const enum OpportunityFollowType {
  /** 电话跟进 */
  PHONE = "phone",
  /** 邮件跟进 */
  EMAIL = "email",
  /** 上门拜访 */
  VISIT = "visit",
  /** 微信沟通 */
  WECHAT = "wechat",
  /** 会议沟通 */
  MEETING = "meeting",
  /** 产品演示 */
  DEMO = "demo",
  /** 方案提交 */
  PROPOSAL = "proposal",
  /** 报价 */
  QUOTATION = "quotation",
  /** 其他 */
  OTHER = "other",
}

/** 跟进结果对应颜色配置 */
export const OpportunityFollowTypeColorMap = {
  [OpportunityFollowType.PHONE]: "primary",
  [OpportunityFollowType.EMAIL]: "primary",
  [OpportunityFollowType.VISIT]: "primary",
  [OpportunityFollowType.WECHAT]: "success",
  [OpportunityFollowType.MEETING]: "primary",
  [OpportunityFollowType.DEMO]: "primary",
  [OpportunityFollowType.PROPOSAL]: "warning",
  [OpportunityFollowType.QUOTATION]: "warning",
  [OpportunityFollowType.OTHER]: "primary",
};
