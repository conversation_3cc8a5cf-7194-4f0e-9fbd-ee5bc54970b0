import request from "@/utils/request";

const CONTRACT_PAYMENT_BASE_URL = "/api/v1/contract-payments";

const ContractPaymentAPI = {
  /**
   * 获取合同付款记录分页列表
   *
   * @param queryParams 查询参数
   * @returns 合同付款记录分页列表
   */
  getPage(queryParams: ContractPaymentPageQuery) {
    return request<any, PageResult<ContractPaymentPageVO[]>>({
      url: `${CONTRACT_PAYMENT_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 获取合同付款记录详情
   *
   * @param id 付款记录ID
   * @returns 合同付款记录详情
   */
  getDetail(id: string) {
    return request<any, ContractPaymentVO>({
      url: `${CONTRACT_PAYMENT_BASE_URL}/${id}`,
      method: "get",
    });
  },

  /**
   * 获取合同付款记录表单数据
   *
   * @param id 付款记录ID
   * @returns 合同付款记录表单数据
   */
  getFormData(id: string) {
    return request<any, ContractPaymentForm>({
      url: `${CONTRACT_PAYMENT_BASE_URL}/${id}/form`,
      method: "get",
    });
  },

  /**
   * 新增合同付款记录
   *
   * @param data 合同付款记录表单数据
   * @returns 请求结果
   */
  create(data: ContractPaymentForm) {
    return request({
      url: `${CONTRACT_PAYMENT_BASE_URL}`,
      method: "post",
      data: data,
    });
  },

  /**
   * 修改合同付款记录
   *
   * @param id 付款记录ID
   * @param data 合同付款记录表单数据
   * @returns 请求结果
   */
  update(id: string, data: ContractPaymentForm) {
    return request({
      url: `${CONTRACT_PAYMENT_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 删除合同付款记录
   *
   * @param ids 付款记录ID，多个以英文逗号(,)分割
   * @returns 请求结果
   */
  delete(ids: string) {
    return request({
      url: `${CONTRACT_PAYMENT_BASE_URL}/${ids}`,
      method: "delete",
    });
  },

  /**
   * 根据合同ID获取付款记录列表
   *
   * @param contractId 合同ID
   * @returns 付款记录列表
   */
  getByContractId(contractId: string) {
    return request<any, ContractPaymentVO[]>({
      url: `${CONTRACT_PAYMENT_BASE_URL}/by-contract/${contractId}`,
      method: "get",
    });
  },

  /**
   * 根据合同ID统计付款总额
   *
   * @param contractId 合同ID
   * @returns 付款总额
   */
  getTotalAmountByContractId(contractId: string) {
    return request<any, number>({
      url: `${CONTRACT_PAYMENT_BASE_URL}/total-amount/${contractId}`,
      method: "get",
    });
  },

  /**
   * 更新付款状态
   *
   * @param id 付款记录ID
   * @param status 付款状态
   * @returns 请求结果
   */
  updatePaymentStatus(id: string, status: string) {
    return request({
      url: `${CONTRACT_PAYMENT_BASE_URL}/${id}/payment-status`,
      method: "put",
      params: { status },
    });
  },

  /**
   * 更新发票状态
   *
   * @param id 付款记录ID
   * @param invoiceStatus 发票状态
   * @returns 请求结果
   */
  updateInvoiceStatus(id: string, invoiceStatus: string) {
    return request({
      url: `${CONTRACT_PAYMENT_BASE_URL}/${id}/invoice-status`,
      method: "put",
      params: { invoiceStatus },
    });
  },
};

export default ContractPaymentAPI;

/** 合同付款记录分页查询参数 */
export interface ContractPaymentPageQuery extends PageQuery {
  /** 搜索关键字 */
  keywords?: string;
  /** 合同ID */
  contractId?: string;
  /** 付款类型 */
  paymentType?: string;
  /** 付款方式 */
  paymentMethod?: string;
  /** 付款方ID */
  payerPartnerId?: string;
  /** 收款方ID */
  payeePartnerId?: string;
  /** 付款状态 */
  paymentStatus?: string;
  /** 发票状态 */
  invoiceStatus?: string;
  /** 计划付款日期范围 */
  plannedDateRange?: [string, string];
  /** 实际付款日期范围 */
  actualDateRange?: [string, string];
  /** 计划金额范围 */
  plannedAmountRange?: [number, number];
  /** 实际金额范围 */
  actualAmountRange?: [number, number];
  /** 币种 */
  currency?: string;
}

/** 合同付款记录分页VO */
export interface ContractPaymentPageVO {
  /** 主键ID */
  id: string;
  /** 合同ID */
  contractId: string;
  /** 合同编号 */
  contractNo: string;
  /** 合同名称 */
  contractName: string;
  /** 付款单号 */
  paymentNo?: string;
  /** 付款类型 */
  paymentType: string;
  /** 付款类型标签 */
  paymentTypeLabel?: string;
  /** 付款方式 */
  paymentMethod?: string;
  /** 付款方式标签 */
  paymentMethodLabel?: string;
  /** 付款方名称 */
  payerPartnerName?: string;
  /** 收款方名称 */
  payeePartnerName?: string;
  /** 计划金额 */
  plannedAmount?: number;
  /** 实际金额 */
  actualAmount?: number;
  /** 币种 */
  currency?: string;
  /** 计划付款日期 */
  plannedDate?: string;
  /** 实际付款日期 */
  actualDate?: string;
  /** 付款状态 */
  paymentStatus: string;
  /** 发票状态 */
  invoiceStatus: string;
  /** 发票号码 */
  invoiceNo?: string;
  /** 发票金额 */
  invoiceAmount?: number;
  /** 创建时间 */
  createTime: string;
}

/** 合同付款记录详情VO */
export interface ContractPaymentVO {
  /** 主键ID */
  id: string;
  /** 合同ID */
  contractId: string;
  /** 合同编号 */
  contractNo: string;
  /** 合同名称 */
  contractName: string;
  /** 付款单号 */
  paymentNo?: string;
  /** 付款类型 */
  paymentType: string;
  /** 付款类型标签 */
  paymentTypeLabel?: string;
  /** 付款方式 */
  paymentMethod?: string;
  /** 付款方式标签 */
  paymentMethodLabel?: string;
  /** 付款方ID */
  payerPartnerId?: string;
  /** 付款方名称 */
  payerPartnerName?: string;
  /** 收款方ID */
  payeePartnerId?: string;
  /** 收款方名称 */
  payeePartnerName?: string;
  /** 计划金额 */
  plannedAmount?: number;
  /** 实际金额 */
  actualAmount?: number;
  /** 币种 */
  currency?: string;
  /** 计划付款日期 */
  plannedDate?: string;
  /** 实际付款日期 */
  actualDate?: string;
  /** 付款状态 */
  paymentStatus: string;
  /** 付款银行 */
  bankName?: string;
  /** 付款账号 */
  bankAccount?: string;
  /** 交易流水号 */
  transactionNo?: string;
  /** 付款凭证文件Id */
  voucherAttachmentId?: string;
  /** 发票状态 */
  invoiceStatus: string;
  /** 发票号码 */
  invoiceNo?: string;
  /** 发票金额 */
  invoiceAmount?: number;
  /** 开票日期 */
  invoiceDate?: string;
  /** 备注 */
  remark?: string;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
}

/** 合同付款记录表单对象 */
export interface ContractPaymentForm {
  /** 主键ID */
  id?: string;
  /** 合同ID */
  contractId: string;
  /** 付款单号 */
  paymentNo?: string;
  /** 付款类型 */
  paymentType: string;
  /** 付款方式 */
  paymentMethod?: string;
  /** 付款方ID */
  payerPartnerId?: string;
  /** 收款方ID */
  payeePartnerId?: string;
  /** 计划金额 */
  plannedAmount?: number;
  /** 实际金额 */
  actualAmount?: number;
  /** 币种 */
  currency?: string;
  /** 计划付款日期 */
  plannedDate?: string;
  /** 实际付款日期 */
  actualDate?: string;
  /** 付款状态 */
  paymentStatus?: string;
  /** 付款银行 */
  bankName?: string;
  /** 付款账号 */
  bankAccount?: string;
  /** 交易流水号 */
  transactionNo?: string;
  /** 付款凭证文件Id */
  voucherAttachmentId?: string;
  /** 发票状态 */
  invoiceStatus?: string;
  /** 发票号码 */
  invoiceNo?: string;
  /** 发票金额 */
  invoiceAmount?: number;
  /** 开票日期 */
  invoiceDate?: string;
  /** 备注 */
  remark?: string;
}
