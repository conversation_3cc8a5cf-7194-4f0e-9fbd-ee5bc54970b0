<template>
  <el-drawer
    v-model="visible"
    title="业务伙伴详情"
    direction="rtl"
    size="800px"
    @close="handleClose"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 详情内容 -->
    <div v-else-if="partnerDetail" class="partner-detail">
      <!-- 基本信息 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>基本信息</h3>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="伙伴名称">
            {{ partnerDetail.partnerName }}
          </el-descriptions-item>
          <el-descriptions-item label="伙伴编码">
            {{ partnerDetail.partnerCode || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="伙伴类型">
            {{ partnerDetail.partnerTypeLabel || partnerDetail.partnerType }}
          </el-descriptions-item>
          <el-descriptions-item label="是否我司">
            <el-tag :type="partnerDetail.isOurCompany ? 'success' : 'info'">
              {{ partnerDetail.isOurCompany ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="法定代表人">
            {{ partnerDetail.legalRepresentative || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="partnerDetail.status === 'active' ? 'success' : 'danger'">
              {{ partnerDetail.status === 'active' ? '正常' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 联系信息 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>联系信息</h3>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="联系人">
            {{ partnerDetail.contactPerson || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{ partnerDetail.contactPhone || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="联系邮箱">
            {{ partnerDetail.contactEmail || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="地址">
            {{ partnerDetail.address || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 证件信息 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>证件信息</h3>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="证件类型">
            {{ partnerDetail.certificateTypeLabel || partnerDetail.certificateType || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="证件号码">
            {{ partnerDetail.certificateNumber || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="税号">
            {{ partnerDetail.taxNumber || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 银行信息 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>银行信息</h3>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="开户银行">
            {{ partnerDetail.bankName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="银行账号">
            {{ partnerDetail.bankAccount || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 备注信息 -->
      <el-card v-if="partnerDetail.remark" shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>备注信息</h3>
          </div>
        </template>
        <div class="remark-content">{{ partnerDetail.remark }}</div>
      </el-card>

      <!-- 其他信息 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>其他信息</h3>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="创建时间">
            {{ partnerDetail.createTime || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ partnerDetail.updateTime || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from "vue";
import { PartnerVO } from "@/api/contract/partner.api";
import PartnerAPI from "@/api/contract/partner.api";

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  partnerId: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["update:modelValue"]);

const visible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit("update:modelValue", val);
  },
});

const loading = ref(false);
const partnerDetail = ref<PartnerVO | null>(null);

// 监听抽屉显示状态
watch(
  () => props.modelValue,
  (val) => {
    if (val && props.partnerId) {
      loadPartnerDetail();
    }
  }
);

// 监听伙伴ID变化
watch(
  () => props.partnerId,
  (val) => {
    if (visible.value && val) {
      loadPartnerDetail();
    }
  }
);

// 加载伙伴详情
function loadPartnerDetail() {
  if (!props.partnerId) {
    ElMessage.error("业务伙伴ID不能为空");
    handleClose();
    return;
  }

  loading.value = true;
  PartnerAPI.getDetail(props.partnerId)
    .then((data) => {
      partnerDetail.value = data;
    })
    .catch(() => {
      ElMessage.error("获取业务伙伴详情失败");
      handleClose();
    })
    .finally(() => {
      loading.value = false;
    });
}

// 关闭抽屉
function handleClose() {
  visible.value = false;
  partnerDetail.value = null;
}

onMounted(() => {
  if (visible.value && props.partnerId) {
    loadPartnerDetail();
  }
});
</script>

<style lang="scss" scoped>
.loading-container {
  padding: 20px;
}

.partner-detail {
  padding: 0 20px;
}

.section-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }
  }
}

.remark-content {
  padding: 10px;
  white-space: pre-wrap;
  line-height: 1.5;
}
</style>
