<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.system.mapper.UserMapper">

    <!-- 用户分页列表 -->
    <select id="getUserPage" resultType="com.hntsz.boot.system.model.bo.UserBO">
        SELECT
            u.id,
            u.username,
            u.nickname,
            u.mobile,
            u.gender,
            u.avatar,
            u.STATUS,
            u.email,
            d.NAME AS dept_name,
            GROUP_CONCAT( r.NAME ) AS roleNames,
            u.create_time
        FROM
            sys_user u
                LEFT JOIN sys_dept d ON u.dept_id = d.id
                LEFT JOIN sys_user_role sur ON u.id = sur.user_id
                LEFT JOIN sys_role r ON sur.role_id = r.id
        <where>
            u.is_deleted = 0
            <!-- 非超级管理员用户限制查看超级管理员  -->
            <if test="!queryParams.isRoot">
                AND NOT EXISTS (
                SELECT
                    1
                FROM sys_user_role sur
                    INNER JOIN sys_role r ON sur.role_id = r.id
                WHERE
                    sur.user_id = u.id
                    AND r.code = '${@com.hntsz.boot.common.constant.SystemConstants@ROOT_ROLE_CODE}'
                )
            </if>
            <if test='queryParams.keywords!=null and queryParams.keywords.trim() neq ""'>
                AND (
                    u.username LIKE CONCAT('%',#{queryParams.keywords},'%')
                    OR u.nickname LIKE CONCAT('%',#{queryParams.keywords},'%')
                    OR u.mobile LIKE CONCAT('%',#{queryParams.keywords},'%')
                    )
            </if>
            <if test='queryParams.status!=null'>
                AND u.status = #{queryParams.status}
            </if>
            <if test='queryParams.deptId!=null'>
                AND concat(',',concat(d.tree_path,',',d.id),',') like concat('%,',#{queryParams.deptId},',%')
            </if>
            <if test="queryParams.createTime != null and queryParams.createTime.size > 0">
                <if test="queryParams.createTime[0] != null and queryParams.createTime[0] != ''">
                    <bind name="startDate" value="queryParams.createTime[0].length() == 10 ? queryParams.createTime[0] + ' 00:00:00' : queryParams.createTime[0]"/>
                    AND u.create_time &gt;= #{startDate}
                </if>
                <if test="queryParams.createTime[1] != null and queryParams.createTime[1] != ''">
                    <bind name="endDate" value="queryParams.createTime[1].length() == 10 ? queryParams.createTime[1] + ' 23:59:59' : queryParams.createTime[1]"/>
                    AND u.create_time &lt;= #{endDate}
                </if>
            </if>
            <if test="queryParams.roleIds != null and queryParams.roleIds.size() > 0">
                AND sur.role_id IN
                <foreach item="roleId" collection="queryParams.roleIds" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
            </if>
        </where>
        GROUP BY
            u.id
        <choose>
            <!-- 如果排序参数都传入 -->
            <when test="queryParams.field != null and queryParams.field != '' and queryParams.direction != null">
                ORDER BY u.${queryParams.field} ${queryParams.direction}
            </when>
            <!-- 默认排序 -->
            <otherwise>
                ORDER BY u.update_time DESC, u.create_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 用户表单信息映射 -->
    <resultMap id="UserFormMap" type="com.hntsz.boot.system.model.form.UserForm">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="username" column="username" jdbcType="VARCHAR"/>
        <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="TINYINT"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="BOOLEAN"/>
        <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
        <collection
                property="roleIds"
                column="id"
                select="com.hntsz.boot.system.mapper.UserRoleMapper.listRoleIdsByUserId" >
            <result column="role_id" />
        </collection>
    </resultMap>

    <!-- 根据用户ID获取用户详情 -->
    <select id="getUserFormData" resultMap="UserFormMap">
        SELECT
            id,
            username,
            nickname,
            mobile,
            gender,
            avatar,
            email,
            status,
            dept_id
        FROM
            sys_user
        WHERE
            id = #{userId} AND is_deleted = 0
    </select>

    <!-- 用户认证信息映射 -->
    <resultMap id="AuthCredentialsMap" type="com.hntsz.boot.core.security.model.UserAuthCredentials">
        <id property="userId" column="userId" jdbcType="BIGINT"/>
        <result property="username" column="username" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="BOOLEAN"/>
        <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
        <collection property="roles" ofType="string" javaType="java.util.Set">
            <result column="code" />
        </collection>
    </resultMap>

    <!-- 根据用户名获取用户的认证信息 -->
    <select id="getAuthCredentialsByUsername" resultMap="AuthCredentialsMap">
        SELECT
            t1.id userId,
            t1.username,
            t1.nickname,
            t1.PASSWORD,
            t1.STATUS,
            t1.dept_id ,
            t3.CODE
        FROM
            sys_user t1
                LEFT JOIN sys_user_role t2 ON t2.user_id = t1.id
                LEFT JOIN sys_role t3 ON t3.id = t2.role_id
        WHERE
            t1.username = #{username} AND t1.is_deleted = 0
    </select>

    <!-- 根据微信openid获取用户的认证信息  -->
    <select id="getAuthCredentialsByOpenId" resultMap="AuthCredentialsMap">
        SELECT
            t1.id userId,
            t1.username,
            t1.nickname,
            t1.PASSWORD,
            t1.STATUS,
            t1.dept_id ,
            t3.CODE
        FROM
            sys_user t1
                LEFT JOIN sys_user_role t2 ON t2.user_id = t1.id
                LEFT JOIN sys_role t3 ON t3.id = t2.role_id
        WHERE
            t1.openid = #{openid} AND t1.is_deleted = 0
    </select>

    <!-- 根据手机号获取用户的认证信息 -->
    <select id="getAuthCredentialsByMobile" resultMap="AuthCredentialsMap">
        SELECT
            t1.id userId,
            t1.username,
            t1.nickname,
            t1.STATUS,
            t1.dept_id ,
            t3.CODE
        FROM
            sys_user t1
                LEFT JOIN sys_user_role t2 ON t2.user_id = t1.id
                LEFT JOIN sys_role t3 ON t3.id = t2.role_id
        WHERE
            t1.mobile = #{mobile} AND t1.is_deleted = 0
    </select>

    <!-- 获取用户导出列表 -->
    <select id="listExportUsers" resultType="com.hntsz.boot.system.model.dto.UserExportDTO">
        SELECT
            u.username,
            u.nickname,
            u.mobile,
            u.email,
            u.gender,
            d.NAME AS dept_name,
            u.create_time
        FROM
            sys_user u
                LEFT JOIN sys_dept d ON u.dept_id = d.id
        <where>
            u.is_deleted = 0
            <if test="!isRoot">
            AND NOT EXISTS (
                SELECT
                    1
                FROM sys_user_role sur
                    INNER JOIN sys_role r ON sur.role_id = r.id
                WHERE
                    sur.user_id = u.id
                    AND r.code = '${@com.hntsz.boot.common.constant.SystemConstants@ROOT_ROLE_CODE}'
            )
            </if>
            <if test='keywords!=null and keywords.trim() neq ""'>
                AND (u.username LIKE CONCAT('%',#{keywords},'%')
                OR u.nickname LIKE CONCAT('%',#{keywords},'%')
                OR u.mobile LIKE CONCAT('%',#{keywords},'%'))
            </if>
            <if test='status!=null'>
                AND u.status = #{status}
            </if>
            <if test='deptId!=null'>
                AND concat(',',concat(d.tree_path,',',d.id),',') like concat('%,',#{deptId},',%')
            </if>
        </where>
        GROUP BY u.id
    </select>

    <!-- 根据用户ID获取用户详情 -->
    <select id="getUserProfile" resultType="com.hntsz.boot.system.model.bo.UserBO">
        SELECT
            u.id,
            u.username,
            u.nickname,
            u.mobile,
            u.gender,
            u.avatar,
            u.STATUS,
            u.email,
            d.NAME AS deptName,
            GROUP_CONCAT(r.NAME) AS roleNames,
            u.create_time
        FROM
            sys_user u
                LEFT JOIN sys_dept d ON u.dept_id = d.id
                LEFT JOIN sys_user_role sur ON u.id = sur.user_id
                LEFT JOIN sys_role r ON sur.role_id = r.id
        WHERE
            u.id = #{userId} AND u.is_deleted = 0
    </select>

    <!-- 根据 OAuth 提供商和用户ID获取用户认证信息 -->
    <select id="getAuthCredentialsByOAuth" resultMap="AuthCredentialsMap">
        SELECT
            t1.id userId,
            t1.username,
            t1.nickname,
            t1.PASSWORD,
            t1.STATUS,
            t1.dept_id ,
            t3.CODE
        FROM
            sys_user t1
                LEFT JOIN sys_user_role t2 ON t2.user_id = t1.id
                LEFT JOIN sys_role t3 ON t3.id = t2.role_id
        WHERE
            t1.oauth_provider = #{provider} 
            AND t1.oauth_id = #{oauthId} 
            AND t1.is_deleted = 0
    </select>

    <!-- 获取用户选项列表（根据当前用户权限过滤ROOT用户） -->
    <select id="listUserOptions" resultType="com.hntsz.boot.system.model.entity.User">
        SELECT
            id,
            username,
            nickname
        FROM
            sys_user
        <where>
            is_deleted = 0
            AND status = 1
            <!-- 非超级管理员用户限制查看超级管理员  -->
            <if test="!isRoot">
                AND NOT EXISTS (
                SELECT
                    1
                FROM sys_user_role sur
                    INNER JOIN sys_role r ON sur.role_id = r.id
                WHERE
                    sur.user_id = sys_user.id
                    AND r.code = '${@com.hntsz.boot.common.constant.SystemConstants@ROOT_ROLE_CODE}'
                )
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
