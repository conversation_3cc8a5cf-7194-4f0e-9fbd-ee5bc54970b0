package com.hntsz.boot.modules.contract.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hntsz.boot.modules.contract.model.entity.ContractPartnerRelation;
import com.hntsz.boot.modules.contract.model.form.ContractPartnerRelationForm;
import com.hntsz.boot.modules.contract.model.vo.ContractPartnerRelationVO;
import com.hntsz.boot.modules.contract.model.vo.ContractPartnerRelationDetailVO;

import java.util.List;
import java.util.Map;

/**
 * 合同伙伴关联表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public interface ContractPartnerRelationService extends IService<ContractPartnerRelation> {

    /**
     * 根据合同ID获取伙伴关联信息
     *
     * @param contractId 合同ID
     * @return 伙伴关联信息列表
     */
    List<ContractPartnerRelationVO> getByContractId(Long contractId);

    /**
     * 批量根据合同ID获取伙伴关联信息
     *
     * @param contractIds 合同ID列表
     * @return 合同ID与伙伴关联信息的映射
     */
    Map<Long, List<ContractPartnerRelationVO>> getByContractIds(List<Long> contractIds);

    /**
     * 根据合同ID获取伙伴关联详情信息
     *
     * @param contractId 合同ID
     * @return 伙伴关联详情信息列表
     */
    List<ContractPartnerRelationDetailVO> getDetailsByContractId(Long contractId);

    /**
     * 批量保存合同伙伴关联信息
     *
     * @param contractId 合同ID
     * @param relations 伙伴关联信息列表
     * @return 是否成功
     */
    boolean batchSaveContractPartnerRelations(Long contractId, List<ContractPartnerRelationForm> relations);

    /**
     * 根据合同ID删除伙伴关联信息
     *
     * @param contractId 合同ID
     * @return 是否成功
     */
    boolean deleteByContractId(Long contractId);
}
