import { useEffect, useState, useCallback } from "react";
import { MobileHeader } from "@/components/mobile/MobileHeader";
import { BottomNavigation } from "@/components/mobile/BottomNavigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useNavigate } from "react-router-dom";
import {
  Search,
  Filter,
  Plus,
  Target,
  Building,
  User,
  DollarSign,
  Calendar,
  TrendingUp,
  Loader2,
} from "lucide-react";
import BizOpportunitiesAPI, {
  IOpportunity,
  IOpportunityStatistic,
} from "@/api/biz-opportunities";
import { OpportunityStatusEnum } from "@/dicts/OpportunityStatusDict";

export default function OpportunitiesPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const navigate = useNavigate();

  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // 商机列表
  const [opportunities, setOpportunities] = useState<IOpportunity[]>([]);
  // 商机总数
  const [opportunitiesCount, setOpportunitiesCount] = useState(0);

  /**
   * 获取商机列表
   * @param page 页码
   * @param isRefresh 是否为刷新操作
   */
  const fetchOpportunities = async (
    page: number = 1,
    isRefresh: boolean = false
  ) => {
    if (loading) return;

    setLoading(true);
    try {
      const res = await BizOpportunitiesAPI.getOpportunitiesPage({
        pageNum: page,
        pageSize: pageSize,
      });

      if (isRefresh) {
        // 刷新时替换数据
        setOpportunities(res.list);
        setCurrentPage(1);
      } else {
        // 加载更多时追加数据
        setOpportunities((prev) =>
          page === 1 ? res.list : [...prev, ...res.list]
        );
      }

      setOpportunitiesCount(res.total);

      // 判断是否还有更多数据
      const totalPages = Math.ceil(res.total / pageSize);
      setHasMore(page < totalPages);
    } catch (error) {
      console.error("获取商机列表失败:", error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 触底加载更多
   */
  const loadMore = useCallback(() => {
    if (!hasMore || loading) return;

    const nextPage = currentPage + 1;
    setCurrentPage(nextPage);
    fetchOpportunities(nextPage, false);
  }, [currentPage, hasMore, loading]);

  /**
   * 下拉刷新
   */
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // 同时刷新统计数据和商机列表
      await Promise.all([
        fetchOpportunityStatistic(),
        fetchOpportunities(1, true),
      ]);
    } finally {
      setRefreshing(false);
    }
  }, []);

  /**
   * 监听滚动事件，实现触底加载
   */
  useEffect(() => {
    const handleScroll = () => {
      // 获取滚动位置
      const scrollTop =
        window.pageYOffset || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;

      // 距离底部100px时触发加载
      const threshold = 100;

      if (scrollTop + windowHeight >= documentHeight - threshold) {
        loadMore();
      }
    };

    // 添加滚动监听
    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [loadMore]);

  /**
   * 下拉刷新手势监听
   */
  useEffect(() => {
    let startY = 0;
    let currentY = 0;
    let isPulling = false;

    const handleTouchStart = (e: TouchEvent) => {
      if (window.scrollY === 0) {
        startY = e.touches[0].clientY;
        isPulling = true;
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (!isPulling) return;

      currentY = e.touches[0].clientY;
      const pullDistance = currentY - startY;

      // 下拉距离超过80px时触发刷新
      if (pullDistance > 80 && window.scrollY === 0) {
        e.preventDefault();
      }
    };

    const handleTouchEnd = () => {
      if (!isPulling) return;

      const pullDistance = currentY - startY;

      // 下拉距离超过80px时执行刷新
      if (pullDistance > 80 && window.scrollY === 0) {
        handleRefresh();
      }

      isPulling = false;
      startY = 0;
      currentY = 0;
    };

    document.addEventListener("touchstart", handleTouchStart, {
      passive: true,
    });
    document.addEventListener("touchmove", handleTouchMove, { passive: false });
    document.addEventListener("touchend", handleTouchEnd, { passive: true });

    return () => {
      document.removeEventListener("touchstart", handleTouchStart);
      document.removeEventListener("touchmove", handleTouchMove);
      document.removeEventListener("touchend", handleTouchEnd);
    };
  }, [handleRefresh]);

  /** 商机统计数据 */
  const [opportunityStatistic, setOpportunityStatistic] =
    useState<IOpportunityStatistic>({
      totalCount: "0",
      wonCount: "0",
      lostCount: "0",
      activeCount: "0",
      archivedCount: "0",
      cancelledCount: "0",
    });

  /** 获取商机统计数据 */
  const fetchOpportunityStatistic = async () => {
    try {
      const res = await BizOpportunitiesAPI.getOpportunityStatistic();
      setOpportunityStatistic(res);
    } catch (error) {
      console.error("获取商机统计数据失败:", error);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchOpportunityStatistic();
    fetchOpportunities(1, true);
  }, []);

  const getStageColor = (stage: string) => {
    switch (stage) {
      case "initial":
        return "secondary";
      case "interested":
        return "warning";
      case "proposal":
        return "accent";
      case "negotiation":
        return "primary";
      case "closed_won":
        return "success";
      case "closed_lost":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "destructive";
      case "medium":
        return "warning";
      case "low":
        return "secondary";
      default:
        return "secondary";
    }
  };

  /** 过滤商机 */
  const filteredOpportunities = opportunities.filter(
    (opp) =>
      opp.opportunityName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      opp.opportunityCode?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      opp.partnerName?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-background pb-20">
      {/* 下拉刷新指示器 */}
      {refreshing && (
        <div className="fixed top-0 left-0 right-0 z-50 bg-primary/10 backdrop-blur-sm">
          <div className="flex items-center justify-center py-3">
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            <span className="text-sm text-primary">正在刷新...</span>
          </div>
        </div>
      )}

      <MobileHeader
        title="商机管理"
        showSearch={true}
        searchPlaceholder="搜索商机..."
        onSearch={(query) => setSearchQuery(query)}
        searchValue={searchQuery}
      />

      <div className="p-4 space-y-4">
        {/* 统计卡片 */}
        <div className="grid grid-cols-3 gap-2">
          <div className="bg-card rounded-lg p-3 text-center shadow-soft">
            <p className="text-2xl font-bold text-primary">
              {opportunityStatistic.totalCount}
            </p>
            <p className="text-xs text-muted-foreground">总数</p>
          </div>
          <div className="bg-card rounded-lg p-3 text-center shadow-soft">
            <p className="text-2xl font-bold text-success">
              {opportunityStatistic.wonCount}
            </p>
            <p className="text-xs text-muted-foreground">成交</p>
          </div>
          <div className="bg-card rounded-lg p-3 text-center shadow-soft">
            <p className="text-2xl font-bold text-warning">
              {opportunityStatistic.activeCount}
            </p>
            <p className="text-xs text-muted-foreground">跟进中</p>
          </div>
        </div>

        {/* 商机列表 */}
        <div className="space-y-3">
          {filteredOpportunities.map((opportunity) => (
            <div
              key={opportunity.id}
              className="bg-card rounded-lg p-4 shadow-soft cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => {
                navigate(`/opportunities/${opportunity.id}`);
              }}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <Target className="h-4 w-4 text-primary" />
                    <h3 className="font-semibold text-sm">
                      {opportunity.opportunityName}
                    </h3>
                  </div>
                  <p className="text-xs text-muted-foreground mb-1">
                    编号: {opportunity.opportunityCode}
                  </p>
                </div>
                <div className="flex flex-col gap-1">
                  {opportunity.opportunityStageLabel && (
                    <Badge
                      variant={
                        getStageColor(opportunity.opportunityStage) as any
                      }
                      className="text-xs"
                    >
                      {opportunity.opportunityStageLabel}
                    </Badge>
                  )}
                  {opportunity.priorityLabel && (
                    <Badge
                      variant={getPriorityColor(opportunity.priority) as any}
                      className="text-xs w-auto flex items-center justify-center"
                    >
                      {opportunity.priority === "high"
                        ? "高"
                        : opportunity.priority === "medium"
                        ? "中"
                        : "低"}
                    </Badge>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <Building className="h-3 w-3 text-muted-foreground" />
                  <span className="text-muted-foreground">
                    {opportunity.partnerName}
                  </span>
                </div>

                <div className="flex items-center gap-2 text-sm">
                  <User className="h-3 w-3 text-muted-foreground" />
                  <span className="text-muted-foreground">
                    {opportunity.contactPerson}
                  </span>
                </div>

                <div className="flex items-center gap-2 text-sm">
                  <DollarSign className="h-3 w-3 text-muted-foreground" />
                  <span className="font-medium text-primary">
                    {opportunity.estimatedAmount}
                  </span>
                </div>

                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-3 w-3 text-muted-foreground" />
                  <span className="text-muted-foreground">
                    预计成交: {opportunity.estimatedCloseDate}
                  </span>
                </div>

                {/* 成单概率 */}
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground flex items-center gap-1">
                      <TrendingUp className="h-3 w-3" />
                      成单概率
                    </span>
                    <span className="font-medium">
                      {opportunity.winProbability}%
                    </span>
                  </div>
                  <Progress
                    value={opportunity.winProbability}
                    className="h-2"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 加载更多指示器 */}
        {loading && !refreshing && (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            <span className="text-sm text-muted-foreground">加载中...</span>
          </div>
        )}

        {/* 没有更多数据提示 */}
        {!hasMore && opportunities.length > 0 && (
          <div className="text-center py-4">
            <p className="text-sm text-muted-foreground">没有更多数据了</p>
          </div>
        )}

        {filteredOpportunities.length === 0 && !loading && (
          <div className="text-center py-8">
            <Target className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">未找到相关商机</p>
          </div>
        )}
      </div>

      <BottomNavigation />
    </div>
  );
}
