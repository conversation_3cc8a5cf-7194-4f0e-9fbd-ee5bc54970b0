-- OAuth 完整迁移脚本
-- 此脚本包含 OAuth 功能所需的所有数据库结构变更

-- 1. 为 sys_user 表添加 OAuth 字段
-- 添加 oauth_provider 字段
ALTER TABLE `sys_user` ADD COLUMN `oauth_provider` varchar(32) COMMENT 'OAuth 提供商类型 (google, github, gitlab, etc.)' AFTER `openid`;

-- 添加 oauth_id 字段
ALTER TABLE `sys_user` ADD COLUMN `oauth_id` varchar(128) COMMENT 'OAuth 提供商用户ID' AFTER `oauth_provider`;

-- 2. 创建 OAuth 令牌表
CREATE TABLE `oauth_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `provider` varchar(32) NOT NULL COMMENT 'OAuth 提供商',
  `oauth_id` varchar(128) NOT NULL COMMENT 'OAuth 用户ID',
  `access_token` text NOT NULL COMMENT '访问令牌',
  `token_type` varchar(32) DEFAULT 'Bearer' COMMENT '令牌类型',
  `expires_in` int(11) DEFAULT NULL COMMENT '过期时间（秒）',
  `refresh_token` text COMMENT '刷新令牌',
  `scope` varchar(255) COMMENT '权限范围',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OAuth 令牌表';