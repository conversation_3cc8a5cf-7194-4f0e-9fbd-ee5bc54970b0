package com.hntsz.boot.modules.contract.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 合同付款记录移动端视图对象
 */
@Data
@Schema(description = "合同付款记录移动端视图对象")
public class ContractPaymentMobileVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "付款单号")
    private String paymentNo;

    @Schema(description = "付款类型标签")
    private String paymentTypeLabel;

    @Schema(description = "付款状态")
    private String paymentStatus;

    @Schema(description = "发票状态")
    private String invoiceStatus;

    @Schema(description = "实际金额")
    private BigDecimal actualAmount;

    @Schema(description = "付款方式标签")
    private String paymentMethodLabel;

    @Schema(description = "付款方式")
    private String paymentMethod;

    @Schema(description = "计划金额")
    private BigDecimal plannedAmount;

    @Schema(description = "计划日期")
    private String plannedDate;

    @Schema(description = "实际日期")
    private String actualDate;

    @Schema(description = "发票号码")
    private String invoiceNo;

    @Schema(description = "备注")
    private String remark;
}