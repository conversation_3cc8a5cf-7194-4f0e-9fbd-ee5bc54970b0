package com.hntsz.boot.modules.opportunity.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hntsz.boot.common.base.BaseEntityExtra;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 商机线索实体类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("opportunity")
public class Opportunity extends BaseEntityExtra {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商机编码(自动生成)
     */
    private String opportunityCode;

    /**
     * 商机名称
     */
    private String opportunityName;

    /**
     * 商机类型(关联字典编码：opportunity_type)
     */
    private String opportunityType;

    /**
     * 商机来源(关联字典编码：opportunity_source)
     */
    private String opportunitySource;

    /**
     * 关联客户ID(关联partner表)
     */
    private Long partnerId;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 商机阶段(initial-初步接触 interested-有意向 proposal-方案阶段 negotiation-谈判阶段 closed_won-成交 closed_lost-失败)
     */
    private String opportunityStage;

    /**
     * 成单概率(%)
     */
    private Integer winProbability;

    /**
     * 预估金额
     */
    private BigDecimal estimatedAmount;

    /**
     * 预计成交日期
     */
    private LocalDate estimatedCloseDate;

    /**
     * 实际成交日期
     */
    private LocalDate actualCloseDate;

    /**
     * 商机状态(active-进行中 won-已成交 lost-已失败 cancelled-已取消 archived-已归档)
     */
    private String opportunityStatus;

    /**
     * 失败原因(关联字典编码：lost_reason)
     */
    private String lostReason;

    /**
     * 优先级(high-高 medium-中 low-低)
     */
    private String priority;

    /**
     * 感兴趣的产品/服务
     */
    private String productInterest;

    /**
     * 客户需求描述
     */
    private String requirements;

    /**
     * 竞争对手信息
     */
    private String competitionInfo;

    /**
     * 下一步行动计划
     */
    private String nextAction;

    /**
     * 下次跟进日期
     */
    private LocalDate nextFollowDate;

    /**
     * 负责人ID(关联sys_user表)
     */
    private Long responsibleUserId;

    /**
     * 所属部门ID(关联sys_dept表)
     */
    private Long deptId;

    /**
     * 标签(多个标签用逗号分隔)
     */
    private String tags;

    /**
     * 归档原因
     */
    private String archiveReason;

    /**
     * 备注
     */
    private String remark;
}