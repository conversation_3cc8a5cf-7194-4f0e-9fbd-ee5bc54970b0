import { useState, useCallback } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import AuthAPI, { type OAuthConfig } from "@/api/auth";
import { useUserStore } from "@/store";
import { toast } from "@/components/ui/sonner";

/**
 * OAuth 第三方登录状态类型
 */
interface OAuthState {
  loading: boolean;
  statusMessage: string;
  statusType: "success" | "warning" | "danger" | "info";
  hasError: boolean;
}

/**
 * OAuth 第三方登录 Hook
 * 提供完整的第三方登录功能，包括配置获取、授权跳转、回调处理等
 */
export const useOAuth = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login: userLogin, oauthLogin: userOAuthLogin } = useUserStore();

  const [oauthState, setOauthState] = useState<OAuthState>({
    loading: false,
    statusMessage: "",
    statusType: "info",
    hasError: false,
  });

  /**
   * 更新 OAuth 状态
   */
  const updateOAuthState = useCallback((updates: Partial<OAuthState>) => {
    setOauthState((prev) => ({ ...prev, ...updates }));
  }, []);

  /**
   * 第三方授权登录
   * 获取 OAuth 配置并跳转到授权页面
   */
  const startOAuthLogin = useCallback(async () => {
    if (oauthState.loading) return;

    updateOAuthState({
      loading: true,
      statusMessage: "正在加载OAuth配置...",
      statusType: "info",
      hasError: false,
    });

    try {
      // 从后端获取 OAuth 配置
      const oauthConfig: OAuthConfig = await AuthAPI.getOAuthConfig();

      // 检查是否启用 OAuth
      if (!oauthConfig.enabled) {
        updateOAuthState({
          statusMessage: "OAuth登录功能已禁用",
          statusType: "warning",
          hasError: true,
        });
        toast.warning("OAuth登录功能已禁用");
        return;
      }

      // 检查配置是否完整
      if (!oauthConfig.clientId || !oauthConfig.authorizationUrl) {
        updateOAuthState({
          statusMessage: "OAuth配置不完整",
          statusType: "danger",
          hasError: true,
        });
        toast.error("OAuth配置不完整");
        return;
      }

      // 生成 state 参数用于安全验证
      const state = Math.random().toString(36).substring(2, 15);
      sessionStorage.setItem("oauth_state", state);

      // 保存重定向地址
      const redirect = new URLSearchParams(location.search).get("redirect");
      if (redirect) {
        sessionStorage.setItem("oauth_redirect", redirect);
      } else {
        sessionStorage.setItem("oauth_redirect", "/");
      }

      // 构建 OAuth 授权 URL
      const authUrl = new URL(oauthConfig.authorizationUrl);
      authUrl.searchParams.append("client_id", oauthConfig.clientId);
      authUrl.searchParams.append(
        "redirect_uri",
        // 这里直接使用 /auth/callback
        `${window.location.origin}/auth/callback`
        // oauthConfig.redirectUri || `${window.location.origin}/auth/callback`
      );
      authUrl.searchParams.append("response_type", "code");
      authUrl.searchParams.append(
        "scope",
        oauthConfig.scopes || "openid profile email"
      );
      authUrl.searchParams.append("state", state);

      updateOAuthState({
        statusMessage: "正在跳转到授权页面...",
        statusType: "success",
      });
      toast.success("正在跳转到授权页面...");

      // 跳转到 OAuth 授权页面
      window.location.href = authUrl.toString();
    } catch (error) {
      console.error("获取 OAuth 配置失败:", error);
      updateOAuthState({
        statusMessage: "OAuth配置加载失败",
        statusType: "danger",
        hasError: true,
      });
      toast.error("OAuth配置加载失败");
    } finally {
      // 延迟重置加载状态，因为页面可能即将跳转
      setTimeout(() => {
        updateOAuthState({ loading: false });
      }, 3000);
    }
  }, [oauthState.loading, location.search, updateOAuthState]);

  /**
   * 处理 OAuth 回调
   * 处理从第三方授权服务器返回的授权码
   */
  const handleOAuthCallback = useCallback(async () => {
    updateOAuthState({
      loading: true,
      statusMessage: "正在处理OAuth登录...",
      statusType: "info",
      hasError: false,
    });

    try {
      const urlParams = new URLSearchParams(location.search);
      const code = urlParams.get("code");
      const state = urlParams.get("state");

      if (!code || !state) {
        throw new Error("缺少必要的OAuth参数");
      }

      // 验证 state 参数
      const savedState = sessionStorage.getItem("oauth_state");
      if (state !== savedState) {
        throw new Error("无效的state参数，可能存在安全风险");
      }

      // 清除 state
      sessionStorage.removeItem("oauth_state");

      // 调用 OAuth 登录 API
      await userOAuthLogin("oauth", code);

      updateOAuthState({
        statusMessage: "OAuth登录成功",
        statusType: "success",
      });

      // 获取重定向地址并跳转
      const savedRedirect = sessionStorage.getItem("oauth_redirect");
      sessionStorage.removeItem("oauth_redirect");

      const redirectPath = savedRedirect || "/";
      navigate(redirectPath, { replace: true });
    } catch (error: any) {
      console.error("OAuth 登录失败:", error);
      const errorMessage = error?.message || error?.toString() || "未知错误";

      updateOAuthState({
        statusMessage: `OAuth登录失败: ${errorMessage}`,
        statusType: "danger",
        hasError: true,
      });
      toast.error(`OAuth登录失败: ${errorMessage}`);

      // 登录失败时跳转回登录页
      navigate("/login", { replace: true });
    } finally {
      updateOAuthState({ loading: false });
    }
  }, [location.search, navigate, userLogin, updateOAuthState]);

  /**
   * 返回登录页面
   */
  const goToLogin = useCallback(() => {
    const urlParams = new URLSearchParams(location.search);
    const redirect = urlParams.get("redirect");

    if (redirect) {
      navigate(`/login?redirect=${encodeURIComponent(redirect)}`);
    } else {
      navigate("/login");
    }
  }, [location.search, navigate]);

  return {
    // 状态
    ...oauthState,

    // 方法
    startOAuthLogin,
    handleOAuthCallback,
    goToLogin,
  };
};
