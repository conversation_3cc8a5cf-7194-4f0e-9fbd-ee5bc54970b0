package com.hntsz.boot.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hntsz.boot.core.security.extension.oauth.OAuthUserInfo;
import com.hntsz.boot.system.model.entity.OAuthToken;

/**
 * OAuth 令牌服务接口
 *
 * <AUTHOR>
 * @since 2.22.0
 */
public interface OAuthTokenService extends IService<OAuthToken> {

    /**
     * 保存或更新OAuth令牌
     *
     * @param userId 用户ID
     * @param oAuthUserInfo OAuth用户信息（包含令牌信息）
     */
    void saveOrUpdateToken(Long userId, OAuthUserInfo oAuthUserInfo);

    /**
     * 根据用户ID和提供商获取令牌
     *
     * @param userId 用户ID
     * @param provider OAuth提供商
     * @return OAuth令牌
     */
    OAuthToken getTokenByUserAndProvider(Long userId, String provider);

    /**
     * 删除用户的OAuth令牌
     *
     * @param userId 用户ID
     * @param provider OAuth提供商
     */
    void removeToken(Long userId, String provider);
}