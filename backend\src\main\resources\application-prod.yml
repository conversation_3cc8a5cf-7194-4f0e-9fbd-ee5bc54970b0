server:
  port: 8989

spring:
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************************************************
    username: root
    password: 9591
  data:
    redis:
      database: 0
      host: 127.0.0.1
      port: 6379
      timeout: 10s
      lettuce:
        pool:
          # 连接池最大连接数 默认8 ，负数表示没有限制
          max-active: 8
          # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认-1
          max-wait: -1
          # 连接池中的最大空闲连接 默认8
          max-idle: 8
          # 连接池中的最小空闲连接 默认0
          min-idle: 0
  cache:
    enabled: false
    # 缓存类型 redis、none(不使用缓存)
    type: redis
    # 缓存时间(单位：ms)
    redis:
      time-to-live: 3600000
      # 缓存null值，防止缓存穿透
      cache-null-values: true
    caffeine:
      spec: initialCapacity=50,maximumSize=1000,expireAfterWrite=600s
  # 邮件配置
  mail:
    host: smtp.hntsz.com
    port: 587
    username: <EMAIL>
    password: 123456
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
    # 邮件发送者
    from: <EMAIL>
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  global-config:
    db-config:
      # 主键ID类型
      id-type: none
      # 逻辑删除对应的全局属性名（注意：须是对象属性名，不能是表字段名，如 isDeleted 而非 is_deleted，否则逻辑删除失效）
      logic-delete-field: isDeleted
      # 逻辑删除-删除值
      logic-delete-value: 1
      # 逻辑删除-未删除值
      logic-not-delete-value: 0
  configuration:
    # 驼峰下划线转换
    map-underscore-to-camel-case: true
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 安全配置
security:
  session:
    type: jwt # 会话方式 [jwt|redis-token]
    access-token-time-to-live: 7200   # 访问令牌 有效期(单位：秒)，默认 2 小时，-1 表示永不过期
    refresh-token-time-to-live: 604800   # 刷新令牌有效期(单位：秒)，默认 7 天，-1 表示永不过期
    jwt:
      secret-key: SecretKey012345678901234567890123456789012345678901234567890123456789 # JWT密钥(HS256算法至少32字符)
    redis-token:
      allow-multi-login: true # 是否允许多设备登录
  oauth:
    client-id: 499a8e20754ca09fd44d
    client-secret: 3140e5426ec04bae16c7cb26e333c0ad66a4c53a
    authorization-url: https://sso.hntsz.com/login/oauth/authorize
    token-url: https://sso.hntsz.com/api/login/oauth/access_token
    user-info-url: https://sso.hntsz.com/api/userinfo
    redirect-uri: https://tp.hntsz.com/#/auth/callback
    logout-url: 
    scopes: openid profile email address phone
    user-identifier: name
    auth-style: client_secret_post
    default-dept-id: 1
    default-role-id: 3
  # 安全白名单路径（完全绕过安全过滤器）
  ignore-urls:
    - /api/v1/auth/login/**       # 登录接口(账号密码登录、手机验证码登录、微信登录和OAuth登录)
    - /api/v1/auth/captcha        # 验证码获取接口
    - /api/v1/auth/refresh-token  # 刷新令牌接口
    - /api/v1/auth/oauth/**       # OAuth登录接口
    - /ws/**                      # WebSocket接口
  # 非安全端点路径（允许匿名访问的API）
  unsecured-urls:
    - ${springdoc.swagger-ui.path}
    - /doc.html
    - /swagger-ui/**
    - /v3/api-docs/**
    - /webjars/**
    - /favicon.ico

# 文件存储配置
oss:
  # OSS 类型 (目前支持aliyun、minio)
  type: minio
  # MinIO 对象存储服务
  minio:
    # 服务Endpoint
    endpoint: http://localhost:9000
    # 访问凭据
    access-key: minioadmin
    # 凭据密钥
    secret-key: minioadmin
    # 存储桶名称
    bucket-name: public
    # (可选)自定义域名，如果配置了域名，生成的文件URL是域名格式，未配置则URL则是IP格式 (eg: https://oss.hntsz.com)
    custom-domain:
  # 阿里云OSS对象存储服务
  aliyun:
    # 服务Endpoint
    endpoint: oss-cn-hangzhou.aliyuncs.com
    # 访问凭据
    access-key-id: your-access-key-id
    # 凭据密钥
    access-key-secret: your-access-key-secret
    # 存储桶名称
    bucket-name: default
  # 本地存储
  local:
    # 文件存储路径 请注意下，mac用户请使用 /Users/<USER>/your-path/，否则会有权限问题，windows用户请使用 D:/your-path/
    storage-path: /Users/<USER>/home/
# 短信配置
sms:
  # 阿里云短信
  aliyun:
    accessKeyId: LTAI5tSMgfxxxxxxdiBJLyR
    accessKeySecret: SoOWRqpjtS7xxxxxxZ2PZiMTJOVC
    domain: dysmsapi.aliyuncs.com
    regionId: cn-shanghai
    signName: hntsz
    templates:
      #  注册短信验证码模板
      register: SMS_22xxx771
      # 登录短信验证码模板
      login: SMS_22xxx772
      # 修改手机号短信验证码模板
      change-mobile: SMS_22xxx773

# springdoc配置： https://springdoc.org/properties.html
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: alpha
    tags-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: '系统管理'
      paths-to-match: "/**"
      packages-to-scan:
        - com.hntsz.boot.system.controller
        - com.hntsz.boot.shared.auth.controller
        - com.hntsz.boot.shared.file.controller
        - com.hntsz.boot.shared.codegen.controller
  default-flat-param-object: true

# knife4j 接口文档配置
knife4j:
  # 是否开启 Knife4j 增强功能
  enable: true  # 设置为 true 表示开启增强功能
  # 生产环境配置
  production: false  # 设置为 true 表示在生产环境中不显示文档，为 false 表示显示文档（通常在开发环境中使用）
  setting:
    language: zh_cn

# xxl-job 定时任务配置
xxl:
  job:
    # 定时任务开关
    enabled: false
    admin:
      # 多个地址使用,分割
      addresses: http://127.0.0.1:8686/xxl-job-admin
    accessToken: default_token
    executor:
      appname: xxl-job-executor-${spring.application.name}
      address:
      ip:
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30

# 验证码配置
captcha:
  # 验证码类型 circle-圆圈干扰验证码|gif-Gif验证码|line-干扰线验证码|shear-扭曲干扰验证码
  type: circle
  # 验证码宽度
  width: 120
  # 验证码高度
  height: 40
  # 验证码干扰元素个数
  interfere-count: 2
  # 文本透明度(0.0-1.0)
  text-alpha: 0.8
  # 验证码字符配置
  code:
    # 验证码字符类型 math-算术|random-随机字符
    type: random
    # 验证码字符长度，type=算术时，表示运算位数(1:个位数运算 2:十位数运算)；type=随机字符时，表示字符个数
    length: 4
  # 验证码字体
  font:
    # 字体名称 Dialog|DialogInput|Monospaced|Serif|SansSerif
    name: SansSerif
    # 字体样式 0-普通|1-粗体|2-斜体
    weight: 1
    # 字体大小
    size: 24
  # 验证码有效期(秒)
  expire-seconds: 120

# 微信小程配置
wx:
  miniapp:
    app-id: xxxxxx
    app-secret: xxxxxx