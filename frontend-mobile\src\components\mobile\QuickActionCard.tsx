import { LucideIcon } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface QuickActionCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  onClick: () => void;
  variant?: "primary" | "secondary" | "accent";
}

export function QuickActionCard({ 
  title, 
  description, 
  icon: Icon, 
  onClick,
  variant = "primary"
}: QuickActionCardProps) {
  const variants = {
    primary: "bg-gradient-primary text-white",
    secondary: "bg-gradient-secondary text-white", 
    accent: "bg-gradient-card text-foreground border border-border"
  };

  return (
    <Button
      onClick={onClick}
      className={`
        h-auto p-4 rounded-lg shadow-soft transition-all duration-200
        hover:shadow-medium hover:scale-[1.02] active:scale-[0.98]
        ${variants[variant]}
      `}
      variant="ghost"
    >
      <div className="flex items-center gap-3 w-full">
        <div className={`
          p-2 rounded-lg 
          ${variant === "accent" ? "bg-primary/10" : "bg-white/20"}
        `}>
          <Icon className={`h-5 w-5 ${variant === "accent" ? "text-primary" : "text-white"}`} />
        </div>
        <div className="flex-1 text-left">
          <h3 className="font-semibold text-sm mb-1">{title}</h3>
          <p className={`text-xs ${
            variant === "accent" ? "text-muted-foreground" : "text-white/80"
          }`}>
            {description}
          </p>
        </div>
      </div>
    </Button>
  );
}