import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { MobileHeader } from "@/components/mobile/MobileHeader";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DictUtils, PaymentStatus, InvoiceStatus, ContractStatus } from '@/utils/dictionaries';
import {
  FileText,
  Building,
  User,
  Phone,
  Mail,
  DollarSign,
  Calendar,
  MapPin,
  Users,
  Briefcase,
  CreditCard,
  Receipt,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Download,
  Eye,
  Paperclip,
  File,
  FileSpreadsheet,
  Star,
  Loader2
} from "lucide-react";
import ContractAPI, {
  ContractVO,
  ContractPartnerRelationVO,
  ContractPaymentMobileVO,
  ContractAttachmentVO
} from "@/api/contract";

export default function ContractDetailPage() {
  const { id } = useParams();
  const navigate = useNavigate();

  // 状态管理
  const [loading, setLoading] = useState(true);
  const [contractDetail, setContractDetail] = useState<ContractVO | null>(null);
  const [contractPartners, setContractPartners] = useState<ContractPartnerRelationVO[]>([]);
  const [paymentRecords, setPaymentRecords] = useState<ContractPaymentMobileVO[]>([]);
  const [contractAttachments, setContractAttachments] = useState<ContractAttachmentVO[]>([]);

  // 加载合同详情数据
  useEffect(() => {
    if (id) {
      loadContractDetail();
    }
  }, [id]);

  const loadContractDetail = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const contractId = parseInt(id);

      // 并行加载所有数据
      const [detailData, partnerDetailsData, paymentsData] = await Promise.all([
        ContractAPI.getContractFullDetail(contractId),
        ContractAPI.getContractPartnerDetails(contractId),
        ContractAPI.getContractPaymentDetails(contractId)
      ]);

      setContractDetail(detailData);
      setContractPartners(partnerDetailsData);
      setPaymentRecords(paymentsData);
      setContractAttachments(detailData.attachments || []);
    } catch (error) {
      console.error('加载合同详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 格式化金额显示
  const formatAmount = (amount?: number) => {
    if (!amount) return "¥0";
    return `¥${amount.toLocaleString()}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "draft": return "secondary";
      case "pending": return "warning";
      case "active": return "success";
      case "completed": return "primary";
      case "terminated": return "destructive";
      case "cancelled": return "destructive";
      default: return "secondary";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "draft": return <Clock className="h-4 w-4" />;
      case "pending": return <AlertCircle className="h-4 w-4" />;
      case "active": return <CheckCircle className="h-4 w-4" />;
      case "completed": return <CheckCircle className="h-4 w-4" />;
      case "terminated": return <XCircle className="h-4 w-4" />;
      case "cancelled": return <XCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "pending":
      case "待付款": return "warning";
      case "paid":
      case "已付款": return "success";
      case "partial":
      case "部分付款": return "default";
      case "overdue":
      case "已逾期": return "destructive";
      case "cancelled":
      case "已取消": return "secondary";
      default: return "secondary";
    }
  };

  const getInvoiceStatusColor = (status: string) => {
    switch (status) {
      case "not_issued":
      case "未开票": return "secondary";
      case "issued":
      case "已开票": return "success";
      case "received":
      case "已收票": return "default";
      default: return "secondary";
    }
  };

  const getFileTypeColor = (fileType: string) => {
    switch (fileType) {
      case "IMAGE": return "success";
      case "VIDEO": return "warning";
      case "DOCUMENT": return "primary";
      case "AUDIO": return "info";
      case "OTHER": return "secondary";
      default: return "secondary";
    }
  };

  const getFileTypeLabel = (fileType: string) => {
    switch (fileType) {
      case "IMAGE": return "图片";
      case "VIDEO": return "视频";
      case "DOCUMENT": return "文档";
      case "AUDIO": return "音频";
      case "OTHER": return "其他";
      default: return fileType;
    }
  };

  const getFileIcon = (fileName: string, fileType: string) => {
    if (fileType === "VIDEO") {
      return <FileText className="h-4 w-4 text-warning" />;
    }
    if (fileType === "IMAGE") {
      return <FileText className="h-4 w-4 text-success" />;
    }

    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case "pdf": return <FileText className="h-4 w-4 text-red-500" />;
      case "doc":
      case "docx": return <File className="h-4 w-4 text-blue-500" />;
      case "xls":
      case "xlsx": return <FileSpreadsheet className="h-4 w-4 text-green-500" />;
      default: return <Paperclip className="h-4 w-4" />;
    }
  };

  const formatFileSize = (size: number) => {
    if (!size) return "0 B";

    const units = ["B", "KB", "MB", "GB"];
    let index = 0;
    let fileSize = size;

    while (fileSize >= 1024 && index < units.length - 1) {
      fileSize /= 1024;
      index++;
    }

    return `${fileSize.toFixed(1)} ${units[index]}`;
  };

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <MobileHeader
          title="合同详情"
          showBack
          onBack={() => navigate(-1)}
        />
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  // 如果没有数据，显示错误状态
  if (!contractDetail) {
    return (
      <div className="min-h-screen bg-background">
        <MobileHeader
          title="合同详情"
          showBack
          onBack={() => navigate(-1)}
        />
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">合同详情加载失败</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <MobileHeader
        title="合同详情"
        showBack
        onBack={() => navigate(-1)}
      />

      <div className="p-4 space-y-4">
        {/* 合同基本信息卡片 */}
        <div className="bg-card rounded-lg p-4 shadow-soft">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <FileText className="h-5 w-5 text-primary" />
                <h1 className="text-lg font-bold">{contractDetail.contractName}</h1>
              </div>
              <p className="text-sm text-muted-foreground mb-2">
                编号: {contractDetail.contractNo}
              </p>
              <div className="flex items-center gap-2 mb-3">
                <Badge variant={getStatusColor(contractDetail.contractStatus) as any} className="flex items-center gap-1">
                  {getStatusIcon(contractDetail.contractStatus)}
                  {DictUtils.getContractStatusLabel(contractDetail.contractStatus)}
                </Badge>
                <Badge variant="outline">
                  {contractDetail.contractTypeLabel || contractDetail.contractType}
                </Badge>
                <Badge variant="outline">
                  {contractDetail.contractCategoryLabel || contractDetail.contractCategory}
                </Badge>
              </div>
            </div>
          </div>

          {/* 合同金额 */}
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <DollarSign className="h-5 w-5 text-success" />
              <span className="text-sm text-muted-foreground">合同金额</span>
            </div>
            <p className="text-2xl font-bold text-success">{formatAmount(contractDetail.contractAmount)}</p>
          </div>

          {/* 时间信息 */}
          <div className="grid grid-cols-1 gap-3">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div className="flex-1">
                <p className="text-xs text-muted-foreground">签署日期</p>
                <p className="font-medium">{contractDetail.signingDate}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div className="flex-1">
                <p className="text-xs text-muted-foreground">生效日期</p>
                <p className="font-medium">{contractDetail.effectiveDate}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div className="flex-1">
                <p className="text-xs text-muted-foreground">到期日期</p>
                <p className="font-medium">{contractDetail.expiryDate}</p>
              </div>
            </div>
          </div>
        </div>

        {/* 相关商机信息 */}
        {contractDetail.opportunityId && (
          <div className="bg-card rounded-lg p-4 shadow-soft">
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <Briefcase className="h-4 w-4" />
              相关商机
            </h3>
            <div
              className="p-3 bg-primary/5 rounded-lg cursor-pointer hover:bg-primary/10 transition-colors"
              onClick={() => navigate(`/opportunities/${contractDetail.opportunityId}`)}
            >
              <p className="font-medium text-primary">{contractDetail.opportunityName}</p>
              <p className="text-xs text-muted-foreground">点击查看商机详情</p>
            </div>
          </div>
        )}

        {/* 标签页内容 */}
        <Tabs defaultValue="details" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="details">详细信息</TabsTrigger>
            <TabsTrigger value="files">合同文件</TabsTrigger>
            <TabsTrigger value="partners">合同方</TabsTrigger>
            <TabsTrigger value="payments">付款记录</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4">
            {/* 合同详情 */}
            <div className="bg-card rounded-lg p-4 shadow-soft">
              <h3 className="font-semibold mb-3">合同详情</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">付款方式</p>
                    <p className="font-medium">{contractDetail.paymentMethodLabel || contractDetail.paymentMethod}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">签署地点</p>
                    <p className="font-medium">{contractDetail.signingLocation}</p>
                  </div>
                </div>
                {contractDetail.remark && (
                  <div>
                    <p className="text-sm text-muted-foreground">备注</p>
                    <p className="font-medium text-sm leading-relaxed">{contractDetail.remark}</p>
                  </div>
                )}
              </div>
            </div>

            {/* 负责人信息 */}
            <div className="bg-card rounded-lg p-4 shadow-soft">
              <h3 className="font-semibold mb-3">负责人信息</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">负责人</p>
                  <p className="font-medium">{contractDetail.responsibleUserName}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">所属部门</p>
                  <p className="font-medium">{contractDetail.deptName}</p>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="partners" className="space-y-4">
            {/* 合同方信息 */}
            <div className="space-y-3">
              {contractPartners.map((partner) => (
                <div key={partner.id} className="bg-card rounded-lg p-4 shadow-soft">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Building className="h-4 w-4 text-primary" />
                        <h4 className="font-semibold">{partner.partnerName}</h4>
                      </div>
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="default" className="text-xs">
                          {partner.partnerRoleLabel || partner.partnerRole}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {partner.partnerRoleDescLabel || partner.partnerRoleDesc}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <Users className="h-3 w-3 text-muted-foreground" />
                      <div>
                        <span className="text-muted-foreground">签署人: </span>
                        <span className="font-medium">{partner.signingPerson}</span>
                        <span className="text-muted-foreground"> ({partner.signingPersonTitle})</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 text-sm">
                      <Calendar className="h-3 w-3 text-muted-foreground" />
                      <div>
                        <span className="text-muted-foreground">签署日期: </span>
                        <span className="font-medium">{partner.signingDate}</span>
                      </div>
                    </div>

                    {/* 联系信息 */}
                    {partner.contactPerson && (
                      <div className="flex items-center gap-2 text-sm">
                        <User className="h-3 w-3 text-muted-foreground" />
                        <div>
                          <span className="text-muted-foreground">联系人: </span>
                          <span className="font-medium">{partner.contactPerson}</span>
                        </div>
                      </div>
                    )}

                    {partner.contactPhone && (
                      <div className="flex items-center gap-2 text-sm">
                        <Phone className="h-3 w-3 text-muted-foreground" />
                        <div>
                          <span className="text-muted-foreground">电话: </span>
                          <span className="font-medium">
                            <a href={`tel:${partner.contactPhone}`} className="text-primary hover:underline">
                              {partner.contactPhone}
                            </a>
                          </span>
                        </div>
                      </div>
                    )}

                    {partner.contactEmail && (
                      <div className="flex items-center gap-2 text-sm">
                        <Mail className="h-3 w-3 text-muted-foreground" />
                        <div>
                          <span className="text-muted-foreground">邮箱: </span>
                          <span className="font-medium">
                            <a href={`mailto:${partner.contactEmail}`} className="text-primary hover:underline">
                              {partner.contactEmail}
                            </a>
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="payments" className="space-y-4">
            {/* 付款记录列表 */}
            <div className="space-y-3">
              {paymentRecords.map((payment) => (
                <div
                  key={payment.id}
                  className="bg-card rounded-lg p-4 shadow-soft cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => navigate(`/contracts/${contractDetail.id}/payments/${payment.id}`)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Receipt className="h-4 w-4 text-primary" />
                        <span className="font-semibold text-sm">{payment.paymentNo}</span>
                      </div>
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant={getPaymentStatusColor(payment.paymentTypeLabel || '') as any} className="text-xs">
                          {payment.paymentTypeLabel}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {DictUtils.getPaymentStatusLabel( payment.paymentStatus)}
                        </Badge>
                        <Badge variant={getInvoiceStatusColor(payment.invoiceStatus || '') as any} className="text-xs">
                          {DictUtils.getInvoiceStatusLabel(payment.invoiceStatus || '')}
                        </Badge>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-lg text-success">{formatAmount(payment.actualAmount)}</p>
                      <p className="text-xs text-muted-foreground">{payment.paymentMethodLabel || payment.paymentMethod}</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">计划金额:</span>
                      <span className="font-medium">{formatAmount(payment.plannedAmount)}</span>
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">计划日期:</span>
                      <span className="font-medium">{payment.plannedDate}</span>
                    </div>

                    {payment.actualDate && (
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">实际日期:</span>
                        <span className="font-medium">{payment.actualDate}</span>
                      </div>
                    )}

                    {payment.invoiceNo && (
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">发票号码:</span>
                        <span className="font-medium">{payment.invoiceNo}</span>
                      </div>
                    )}

                    {payment.remark && (
                      <div className="pt-2 border-t border-border">
                        <p className="text-xs text-muted-foreground">{payment.remark}</p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="files" className="space-y-4">
            {/* 合同附件列表 */}
            <div className="bg-card rounded-lg p-4 shadow-soft">
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <FileText className="h-4 w-4" />
                合同附件
              </h3>
              <div className="space-y-2">
                {contractAttachments.map((attachment) => (
                  <div
                    key={attachment.id}
                    className="flex items-center gap-2 p-2 bg-muted/50 rounded-lg cursor-pointer hover:bg-muted transition-colors"
                    onClick={() => window.open(attachment.filePath || '#', '_blank')}
                  >
                    {getFileIcon(attachment.fileName || '', attachment.fileType || 'DOCUMENT')}
                    <span className="text-sm flex-1">{attachment.fileName}</span>
                    <span className="text-xs text-muted-foreground">
                      {formatFileSize(Number(attachment.fileSize) || 0)}
                    </span>
                  </div>
                ))}
              </div>
              {contractAttachments.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-muted-foreground text-sm">暂无文件</p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
