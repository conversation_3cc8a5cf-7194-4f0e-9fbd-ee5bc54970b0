package com.hntsz.boot.modules.contract.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;

/**
 * 合同伙伴关联表表单对象
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@Setter
@Schema(description = "合同伙伴关联表表单对象")
public class ContractPartnerRelationForm {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 合同ID
     */
    @Schema(description = "合同ID")
    private Long contractId;

    /**
     * 伙伴ID
     */
    @Schema(description = "伙伴ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "伙伴ID不能为空")
    private Long partnerId;

    /**
     * 伙伴角色(关联字典编码：partner_role)
     */
    @Schema(description = "伙伴角色", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "伙伴角色不能为空")
    private String partnerRole;

    /**
     * 角色描述(关联字典编码：partner_role_desc)
     */
    @Schema(description = "角色描述")
    private String partnerRoleDesc;

    /**
     * 签署人
     */
    @Schema(description = "签署人")
    @Size(max = 100, message = "签署人长度不能超过100个字符")
    private String signingPerson;

    /**
     * 签署人职务
     */
    @Schema(description = "签署人职务")
    @Size(max = 100, message = "签署人职务长度不能超过100个字符")
    private String signingPersonTitle;

    /**
     * 签署日期
     */
    @Schema(description = "签署日期")
    private LocalDate signingDate;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
