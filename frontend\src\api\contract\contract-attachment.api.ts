import request from "@/utils/request";

const CONTRACT_BASE_URL = "/api/v1/contracts";

const ContractAttachmentAPI = {
  /**
   * 获取合同文件列表
   *
   * @param contractId 合同ID
   * @returns 合同文件列表
   */
  getList(contractId: string) {
    return request<any, ContractAttachmentVO[]>({
      url: `${CONTRACT_BASE_URL}/${contractId}/attachments`,
      method: "get",
    });
  },

  /**
   * 添加合同文件
   *
   * @param contractId 合同ID
   * @param data 合同文件表单数据
   * @returns 请求结果
   */
  add(contractId: string, data: ContractAttachmentForm) {
    return request({
      url: `${CONTRACT_BASE_URL}/${contractId}/attachments`,
      method: "post",
      data: data,
    });
  },

  /**
   * 批量添加合同文件
   *
   * @param contractId 合同ID
   * @param attachmentIds 附件ID列表
   * @returns 请求结果
   */
  batchAdd(contractId: string, attachmentIds: string[]) {
    return request({
      url: `${CONTRACT_BASE_URL}/${contractId}/attachments/batch`,
      method: "post",
      data: attachmentIds,
    });
  },

  /**
   * 删除合同文件
   *
   * @param contractId 合同ID
   * @param attachmentId 附件ID
   * @returns 请求结果
   */
  delete(contractId: string, attachmentId: string) {
    return request({
      url: `${CONTRACT_BASE_URL}/${contractId}/attachments/${attachmentId}`,
      method: "delete",
    });
  },
};

export default ContractAttachmentAPI;

/** 合同文件表单对象 */
export interface ContractAttachmentForm {
  /** 合同ID */
  contractId?: string;
  /** 附件ID */
  attachmentId: string;
  /** 排序 */
  sort?: number;
}

/** 合同文件VO对象 */
export interface ContractAttachmentVO {
  /** 主键 */
  id: string;
  /** 合同ID */
  contractId: string;
  /** 附件ID */
  attachmentId: string;
  /** 文件名称 */
  fileName: string;
  /** 排序 */
  sort: number;
  /** 文件路径 */
  filePath: string;
  /** 文件类型 */
  fileType: string;
  /** 文件大小 */
  fileSize: number;
}
