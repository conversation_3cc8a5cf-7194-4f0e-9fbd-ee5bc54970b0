{"name": "tsz-admin-template", "description": "tsz-admin-template", "version": "2.29.2", "private": true, "type": "module", "scripts": {"dev": "vite", "build:staging": "vue-tsc --noEmit & vite build --mode staging", "build": "vue-tsc --noEmit & vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint:eslint": "eslint --cache \"src/**/*.{vue,ts,js}\" --fix", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "lint": "npm run lint:eslint && npm run lint:prettier && npm run lint:stylelint", "preinstall": "npx only-allow pnpm", "prepare": "husky", "commit": "git-cz"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,css}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@stomp/stompjs": "^7.1.1", "@vueuse/core": "^12.8.2", "@wangeditor-next/editor": "^5.6.34", "@wangeditor-next/editor-for-vue": "^5.1.14", "animate.css": "^4.1.1", "axios": "^1.9.0", "codemirror": "^5.65.19", "codemirror-editor-vue3": "^2.8.0", "default-passive-events": "^2.0.0", "echarts": "^5.6.0", "element-plus": "^2.9.10", "exceljs": "^4.4.0", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^8.2.0", "pinia": "^3.0.2", "qs": "^6.14.0", "sortablejs": "^1.15.6", "vue": "^3.5.13", "vue-draggable-plus": "^0.6.0", "vue-i18n": "^11.1.3", "vue-router": "^4.5.1"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.26.0", "@iconify/utils": "^2.3.0", "@types/codemirror": "^5.60.15", "@types/lodash-es": "^4.17.12", "@types/node": "^22.15.18", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qs": "^6.9.18", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.21", "commitizen": "^4.3.1", "cz-git": "^1.11.1", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-vue": "^10.1.0", "globals": "^15.15.0", "husky": "^9.1.7", "lint-staged": "^15.5.2", "postcss": "^8.5.3", "postcss-html": "^1.8.0", "postcss-scss": "^4.0.9", "prettier": "^3.5.3", "sass": "^1.88.0", "stylelint": "^16.19.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-recommended": "^15.0.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-prettier": "^5.0.3", "terser": "^5.39.1", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1", "unocss": "65.4.3", "unplugin-auto-import": "^19.2.0", "unplugin-vue-components": "^28.5.0", "vite": "6.3.2", "vite-plugin-mock-dev-server": "^1.8.7", "vue-eslint-parser": "^10.1.3", "vue-tsc": "^2.2.10"}, "engines": {"node": ">=18.0.0"}, "repository": "https://github.com/hntsz/tsz-admin-template.git", "author": "海南探数者", "license": "MIT"}