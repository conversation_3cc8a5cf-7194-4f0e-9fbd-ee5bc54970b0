# 探数者综合管理平台 - 移动端

一个基于 React + TypeScript + Tailwind CSS 的现代化移动端企业管理平台，专注于合同管理、商机跟进和业务伙伴管理。

## 🎯 项目概述

本项目是探数者综合管理平台的移动端实现，提供了完整的企业级业务管理功能，包括：
- 📋 合同管理和详情查看
- 🎯 商机跟进和管理
- 👥 业务伙伴管理
- 💰 付款记录管理
- 👤 用户个人中心
- 🔐 安全登录系统

## 🚀 技术栈

### 核心技术
- **React 18.3.1** - 现代化 React 框架
- **TypeScript 5.5.3** - 类型安全的 JavaScript
- **Vite 5.4.1** - 极速开发构建工具
- **React Router 6.26.2** - 客户端路由管理

### UI 和样式
- **Tailwind CSS 3.4.11** - 原子化 CSS 框架
- **Radix UI** - 高质量无障碍组件库
- **Lucide React** - 现代化图标库
- **shadcn/ui** - 基于 Radix UI 的组件系统

### 状态管理和数据
- **TanStack Query 5.56.2** - 服务器状态管理
- **React Hook Form 7.53.0** - 表单管理
- **Zod 3.23.8** - 类型安全的数据验证

### 开发工具
- **ESLint 9.9.0** - 代码质量检查
- **TypeScript ESLint 8.0.1** - TypeScript 代码规范
- **PostCSS + Autoprefixer** - CSS 后处理

## 📁 项目结构

```
frontend-mobile/
├── public/                    # 静态资源
│   ├── logo.png              # 品牌 Logo
│   └── ...
├── src/
│   ├── components/            # React 组件
│   │   ├── mobile/           # 移动端专用组件
│   │   │   ├── MobileHeader.tsx      # 移动端头部导航
│   │   │   ├── BottomNavigation.tsx  # 底部导航栏
│   │   │   ├── StatsCard.tsx         # 统计卡片
│   │   │   └── QuickActionCard.tsx   # 快速操作卡片
│   │   └── ui/               # shadcn/ui 组件库
│   │       ├── button.tsx
│   │       ├── input.tsx
│   │       ├── badge.tsx
│   │       └── ...
│   ├── pages/                # 页面组件
│   │   └── mobile/           # 移动端页面
│   │       ├── MobileIndex.tsx       # 主页
│   │       ├── LoginPage.tsx         # 登录页
│   │       ├── ContractsPage.tsx     # 合同列表
│   │       ├── ContractDetailPage.tsx # 合同详情
│   │       ├── OpportunitiesPage.tsx # 商机列表
│   │       ├── OpportunityDetailPage.tsx # 商机详情
│   │       ├── PartnersPage.tsx      # 伙伴列表
│   │       ├── PaymentDetailPage.tsx # 付款详情
│   │       └── ProfilePage.tsx       # 个人中心
│   ├── hooks/                # 自定义 Hooks
│   │   ├── use-mobile.tsx    # 移动端检测
│   │   └── use-toast.ts      # 消息提示
│   ├── lib/                  # 工具库
│   │   └── utils.ts          # 通用工具函数
│   ├── App.tsx               # 应用主组件
│   ├── main.tsx              # 应用入口
│   └── index.css             # 全局样式
├── tailwind.config.ts        # Tailwind 配置
├── vite.config.ts            # Vite 配置
├── tsconfig.json             # TypeScript 配置
└── package.json              # 项目依赖
```

## 🛠️ 开发环境搭建

### 前置要求
- Node.js >= 18.0.0
- npm 或 pnpm 包管理器

### 安装步骤

1. **克隆项目**
   ```bash
   cd frontend-mobile
   ```

2. **安装依赖**
   ```bash
   npm install
   # 或使用 pnpm
   pnpm install
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   # 或使用 pnpm
   pnpm dev
   ```

4. **访问应用**
   ```
   http://localhost:8080
   ```

## 📜 可用脚本

```bash
# 开发环境启动
npm run dev

# 生产环境构建
npm run build

# 开发环境构建
npm run build:dev

# 代码检查
npm run lint

# 预览生产构建
npm run preview
```

## 🎨 设计系统

### 颜色主题
- **主色调**: 深蓝商务色 (`hsl(215, 85%, 25%)`)
- **次要色**: 科技蓝 (`hsl(200, 85%, 55%)`)
- **成功色**: 绿色 (`hsl(120, 60%, 40%)`)
- **警告色**: 橙色 (`hsl(45, 85%, 55%)`)
- **危险色**: 红色 (`hsl(0, 75%, 55%)`)

### 渐变效果
- **主渐变**: `linear-gradient(135deg, 深蓝, 浅蓝)`
- **英雄渐变**: `linear-gradient(135deg, 深蓝, 中蓝)`
- **卡片渐变**: `linear-gradient(145deg, 白色, 浅灰)`

### 阴影系统
- **软阴影**: 轻微的卡片阴影
- **中等阴影**: 弹出层阴影
- **强阴影**: 模态框阴影
- **发光阴影**: 强调元素阴影

## 🏗️ 核心功能

### 1. 导航系统
- **MobileHeader**: 智能头部导航，支持返回、LOGO、搜索功能
- **BottomNavigation**: 底部标签导航，主要页面快速切换

### 2. 页面功能

#### 🏠 主页 (MobileIndex)
- 用户欢迎信息
- 数据统计概览
- 待跟进商机列表
- 快速导航卡片

#### 📋 合同管理 (ContractsPage)
- 合同列表展示
- 实时搜索过滤
- 状态标签显示
- 点击查看详情

#### 📄 合同详情 (ContractDetailPage)
- 基本信息展示
- 相关商机信息
- 详细信息/合同方/付款记录/合同文件四个标签页
- 付款记录可点击查看详情

#### 🎯 商机管理 (OpportunitiesPage)
- 商机列表展示
- 搜索和筛选功能
- 优先级和阶段标签
- 成单概率进度条

#### 📊 商机详情 (OpportunityDetailPage)
- 商机基本信息
- 客户信息展示
- 详细信息/跟进记录两个标签页
- 完整的跟进历史

#### 👥 伙伴管理 (PartnersPage)
- 伙伴列表展示
- 类型筛选 (客户/供应商/合作伙伴)
- 搜索功能
- 点击查看详情

#### 💰 付款详情 (PaymentDetailPage)
- 付款基本信息
- 关联合同信息
- 付款双方信息
- 发票和附件信息

#### 👤 个人中心 (ProfilePage)
- 用户信息展示
- 功能菜单导航
- 退出登录功能

#### 🔐 登录页面 (LoginPage)
- 用户名密码登录
- 验证码验证
- 记住我功能
- 忘记密码链接
- 第三方登录支持

### 3. 组件特性

#### 搜索功能
- 头部集成搜索框
- 点击激活搜索模式
- 实时搜索过滤
- 支持清空和取消

#### 状态管理
- 使用 TanStack Query 管理服务器状态
- React Hook Form 处理表单状态
- 本地状态使用 useState/useReducer

#### 响应式设计
- 移动端优先设计
- Tailwind CSS 响应式类
- 触摸友好的交互元素
- 适配不同屏幕尺寸

## 🔧 开发指南

### 组件开发规范

1. **使用 TypeScript**
   ```tsx
   interface ComponentProps {
     title: string;
     onClick?: () => void;
   }

   export function Component({ title, onClick }: ComponentProps) {
     return <div onClick={onClick}>{title}</div>;
   }
   ```

2. **使用 shadcn/ui 组件**
   ```tsx
   import { Button } from "@/components/ui/button";
   import { Input } from "@/components/ui/input";
   ```

3. **遵循文件命名规范**
   - 组件文件: `PascalCase.tsx`
   - 工具文件: `kebab-case.ts`
   - 页面文件: `PascalCase.tsx`

### 样式开发规范

1. **使用 Tailwind CSS 类**
   ```tsx
   <div className="bg-card rounded-lg p-4 shadow-soft">
     <h2 className="text-lg font-semibold text-foreground">标题</h2>
   </div>
   ```

2. **使用设计系统颜色**
   ```tsx
   // 正确
   <Button className="bg-primary text-primary-foreground">
   
   // 避免
   <Button className="bg-blue-600 text-white">
   ```

3. **响应式设计**
   ```tsx
   <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
   ```

### 路由配置

在 `App.tsx` 中添加新路由:
```tsx
<Route path="/new-page" element={<NewPage />} />
```

### 状态管理

使用 TanStack Query 进行数据获取:
```tsx
import { useQuery } from '@tanstack/react-query';

function useContracts() {
  return useQuery({
    queryKey: ['contracts'],
    queryFn: () => fetchContracts(),
  });
}
```

## 🚀 部署指南

### 构建生产版本
```bash
npm run build
```

### 部署到服务器
1. 构建完成后，`dist/` 目录包含所有静态文件
2. 将 `dist/` 目录内容上传到 Web 服务器
3. 配置服务器支持 SPA 路由 (所有路由指向 `index.html`)

### 环境变量
创建 `.env` 文件配置环境变量:
```env
VITE_API_BASE_URL=https://api.example.com
VITE_APP_VERSION=1.0.0
```


### 代码规范
- 使用 ESLint 检查代码质量
- 遵循 TypeScript 严格模式
- 编写清晰的注释和文档
