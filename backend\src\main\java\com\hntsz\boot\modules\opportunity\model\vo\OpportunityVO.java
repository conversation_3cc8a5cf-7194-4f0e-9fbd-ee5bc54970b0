package com.hntsz.boot.modules.opportunity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hntsz.boot.common.base.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 商机线索视图对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Schema(description = "商机线索视图对象")
public class OpportunityVO extends BaseVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "商机编码")
    private String opportunityCode;

    @Schema(description = "商机名称")
    private String opportunityName;

    @Schema(description = "商机类型")
    private String opportunityType;

    @Schema(description = "商机类型标签")
    private String opportunityTypeLabel;

    @Schema(description = "商机来源")
    private String opportunitySource;

    @Schema(description = "商机来源标签")
    private String opportunitySourceLabel;

    @Schema(description = "关联客户ID")
    private Long partnerId;

    @Schema(description = "客户名称")
    private String partnerName;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    private String contactEmail;

    @Schema(description = "商机阶段")
    private String opportunityStage;

    @Schema(description = "商机阶段标签")
    private String opportunityStageLabel;

    @Schema(description = "成单概率(%)")
    private Integer winProbability;

    @Schema(description = "预估金额")
    private BigDecimal estimatedAmount;

    @Schema(description = "预计成交日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate estimatedCloseDate;

    @Schema(description = "实际成交日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate actualCloseDate;

    @Schema(description = "商机状态")
    private String opportunityStatus;

    @Schema(description = "商机状态标签")
    private String opportunityStatusLabel;

    @Schema(description = "失败原因")
    private String lostReason;

    @Schema(description = "失败原因标签")
    private String lostReasonLabel;

    @Schema(description = "优先级")
    private String priority;

    @Schema(description = "优先级标签")
    private String priorityLabel;

    @Schema(description = "感兴趣的产品/服务")
    private String productInterest;

    @Schema(description = "客户需求描述")
    private String requirements;

    @Schema(description = "竞争对手信息")
    private String competitionInfo;

    @Schema(description = "下一步行动计划")
    private String nextAction;

    @Schema(description = "下次跟进日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate nextFollowDate;

    @Schema(description = "负责人ID")
    private Long responsibleUserId;

    @Schema(description = "负责人姓名")
    private String responsibleUserName;

    @Schema(description = "所属部门ID")
    private Long deptId;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "标签(多个标签用逗号分隔)")
    private String tags;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "跟进记录数量")
    private Integer followCount;

    @Schema(description = "最后跟进时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate lastFollowDate;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}