import axios, {
  type InternalAxiosRequestConfig,
  type AxiosResponse,
} from "axios";
import { Auth } from "./auth";

// 响应结果枚举
enum ResultEnum {
  SUCCESS = 200,
  BIZ_SUCCESS = "00000",
  BIZ_TOKEN_INVALID = "A0230",
  ACCESS_TOKEN_INVALID = 401,
  REFRESH_TOKEN_INVALID = 402,
}

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_API_URL,
  timeout: 50000,
  headers: { "Content-Type": "application/json;charset=utf-8" },
});

// 是否正在刷新token标识
let isRefreshing = false;
// token过期导致的请求等待队列
const waitingQueue: Array<() => void> = [];

/**
 * 请求拦截器
 * 自动添加Authorization头部
 */
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const accessToken = Auth.getAccessToken();
    // 如果Authorization设置为no-auth，则不携带Token
    if (config.headers.Authorization !== "no-auth" && accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    } else {
      delete config.headers.Authorization;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

/**
 * 响应拦截器
 * 处理响应数据和错误状态
 */
service.interceptors.response.use(
  (response: AxiosResponse) => {
    // 如果响应是二进制流，直接返回
    if (response.config.responseType === "blob") {
      return response;
    }

    const { code, data, msg } = response.data;
    if (code === ResultEnum.SUCCESS || code === ResultEnum.BIZ_SUCCESS) {
      return data;
    }

    // 显示错误消息
    showErrorMessage(msg || "系统出错");
    return Promise.reject(new Error(msg || "Error"));
  },
  async (error) => {
    console.error("request error", error);
    const { config, response } = error;

    if (response) {
      const { code, msg } = response.data;
      if (code === ResultEnum.BIZ_TOKEN_INVALID) {
        // Token过期，刷新Token
        return handleTokenRefresh(config);
      } else if (code === ResultEnum.ACCESS_TOKEN_INVALID) {
        // Token过期，刷新Token
        return handleTokenRefresh(config);
      } else if (code === ResultEnum.REFRESH_TOKEN_INVALID) {
        // 刷新Token过期，跳转登录页
        await handleSessionExpired();
        return Promise.reject(new Error(msg || "Error"));
      } else {
        showErrorMessage(msg || "系统出错");
        return Promise.reject(msg);
      }
    }
    return Promise.reject(error.message);
  }
);

/**
 * 刷新Token处理
 * @param config 请求配置
 * @returns Promise
 */
async function handleTokenRefresh(config: InternalAxiosRequestConfig) {
  return new Promise((resolve) => {
    // 封装需要重试的请求
    const retryRequest = () => {
      config.headers.Authorization = `Bearer ${Auth.getAccessToken()}`;
      resolve(service(config));
    };

    waitingQueue.push(retryRequest);

    if (!isRefreshing) {
      isRefreshing = true;
      refreshToken()
        .then(() => {
          // 依次重试队列中所有请求
          waitingQueue.forEach((callback) => callback());
          waitingQueue.length = 0;
        })
        .catch(async (error) => {
          console.error("handleTokenRefresh error", error);
          await handleSessionExpired();
        })
        .finally(() => {
          isRefreshing = false;
        });
    }
  });
}

/**
 * 刷新Token
 * @returns Promise
 */
async function refreshToken() {
  const refreshToken = Auth.getRefreshToken();
  if (!refreshToken) {
    throw new Error("No refresh token");
  }

  try {
    const response = await axios.post(
      `${import.meta.env.VITE_APP_API_URL}/api/v1/auth/refresh-token`,
      null,
      {
        params: { refreshToken },
        headers: { Authorization: "no-auth" },
      }
    );

    const { accessToken, refreshToken: newRefreshToken } = response.data.data;
    Auth.setTokens(accessToken, newRefreshToken, Auth.getRememberMe());
    return response.data.data;
  } catch (error) {
    throw error;
  }
}

/**
 * 处理会话过期
 */
async function handleSessionExpired() {
  showErrorMessage("您的会话已过期，请重新登录");
  Auth.clearAuth();
  // 跳转到登录页
  window.location.href = "/login";
}

/**
 * 显示错误消息
 * @param message 错误消息
 */
function showErrorMessage(message: string) {
  // 这里可以集成具体的Toast组件
  console.error(message);
  // 如果使用了特定的UI库，可以在这里调用相应的提示组件
}

export default service;
