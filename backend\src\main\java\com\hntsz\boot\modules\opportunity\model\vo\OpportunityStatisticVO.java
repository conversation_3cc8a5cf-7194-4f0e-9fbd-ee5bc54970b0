package com.hntsz.boot.modules.opportunity.model.vo;

import com.hntsz.boot.common.base.BaseVO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * 商机线索视图对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Schema(description = "商机线索视图对象")
public class OpportunityStatisticVO extends BaseVO {
    /** 商机总数 */
    @Schema(description = "商机总数")
    private Long totalCount;

    /** 成功商机总数 */
    @Schema(description = "已成交商机总数")
    private Long wonCount;

    /** 失败商机总数 */
    @Schema(description = "已失败商机总数")
    private Long lostCount;

    /** 跟进中商机总数 */
    @Schema(description = "跟进中商机总数")
    private Long activeCount;

    /** 已归档商机总数 */
    @Schema(description = "已归档商机总数")
    private Long archivedCount;

    /** 已取消商机总数 */
    @Schema(description = "已取消商机总数")
    private Long cancelledCount;
}
