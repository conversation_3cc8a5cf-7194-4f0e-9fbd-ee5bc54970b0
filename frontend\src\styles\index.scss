@use "./reset";
@use "./element-plus";

.app-container {
  padding: 15px;
}

.search-container {
  border-radius: 8px !important;
}

// 进度条颜色
#nprogress .bar {
  background-color: var(--el-color-primary);
}

// 全局搜索区域样式
.search-container {
  padding: 18px 16px 0;
  margin-bottom: 16px;
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;

  .search-buttons {
    margin-right: 0;
  }

  .el-form-item {
    margin-bottom: 18px;
  }
}

// 表格区域样式
.data-table {
  margin-bottom: 16px;

  // 表格工具栏区域
  &__toolbar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;

    &--actions,
    &--tools {
      display: flex;
      gap: 8px;
    }
  }

  // 表格内容区域
  &__content {
    margin: 8px 0;
  }

  // 分页区域
  .el-pagination {
    justify-content: flex-end;
    margin-top: 16px;
  }
}
