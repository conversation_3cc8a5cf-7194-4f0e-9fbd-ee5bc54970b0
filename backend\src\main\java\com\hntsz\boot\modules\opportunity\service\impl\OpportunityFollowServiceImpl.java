package com.hntsz.boot.modules.opportunity.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hntsz.boot.common.result.PageResult;
import com.hntsz.boot.modules.opportunity.converter.OpportunityFollowConverter;
import com.hntsz.boot.modules.opportunity.mapper.OpportunityFollowMapper;
import com.hntsz.boot.modules.opportunity.model.entity.OpportunityFollow;
import com.hntsz.boot.modules.opportunity.model.form.OpportunityFollowForm;
import com.hntsz.boot.modules.opportunity.model.query.OpportunityFollowQuery;
import com.hntsz.boot.modules.opportunity.model.vo.OpportunityFollowVO;
import com.hntsz.boot.modules.opportunity.service.OpportunityFollowService;
import com.hntsz.boot.system.enums.DictCodeEnum;
import com.hntsz.boot.system.service.DictItemService;
import com.hntsz.boot.core.security.util.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商机跟进记录服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OpportunityFollowServiceImpl extends ServiceImpl<OpportunityFollowMapper, OpportunityFollow> implements OpportunityFollowService {

    private final OpportunityFollowMapper opportunityFollowMapper;
    private final OpportunityFollowConverter opportunityFollowConverter;
    private final DictItemService dictItemService;

    @Override
    public PageResult<OpportunityFollowVO> getFollowPage(OpportunityFollowQuery queryParams) {
        // 分页查询
        Page<OpportunityFollowVO> page = opportunityFollowMapper.selectFollowPage(
                new Page<>(queryParams.getPageNum(), queryParams.getPageSize()),
                queryParams
        );

        // 填充字典标签
        fillDictLabels(page.getRecords());

        return PageResult.success(page);
    }

    @Override
    public OpportunityFollowVO getFollowById(Long id) {
        OpportunityFollowVO followVO = opportunityFollowMapper.selectFollowById(id);
        if (followVO != null) {
            fillDictLabels(List.of(followVO));
        }
        return followVO;
    }

    @Override
    public OpportunityFollowForm getFollowFormData(Long id) {
        OpportunityFollow entity = this.getById(id);
        if (entity == null) {
            return new OpportunityFollowForm();
        }
        return opportunityFollowConverter.entityToForm(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveFollow(OpportunityFollowForm formData) {
        OpportunityFollow entity = opportunityFollowConverter.formToEntity(formData);
        
        // 设置跟进人为当前用户
        if (entity.getFollowUserId() == null) {
            entity.setFollowUserId(getCurrentUserId());
        }
        
        return this.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFollow(Long id, OpportunityFollowForm formData) {
        OpportunityFollow entity = opportunityFollowConverter.formToEntity(formData);
        entity.setId(id);
        return this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFollows(String ids) {
        if (StrUtil.isBlank(ids)) {
            return false;
        }
        
        List<Long> idList = Arrays.stream(ids.split(","))
                .map(Long::valueOf)
                .collect(Collectors.toList());
        
        return this.removeByIds(idList);
    }

    @Override
    public List<OpportunityFollowVO> getFollowsByOpportunityId(Long opportunityId) {
        List<OpportunityFollowVO> follows = opportunityFollowMapper.selectFollowsByOpportunityId(opportunityId);
        fillDictLabels(follows);
        return follows;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByOpportunityId(Long opportunityId) {
        int deleted = opportunityFollowMapper.deleteByOpportunityId(opportunityId);
        return deleted > 0;
    }

    /**
     * 填充字典标签
     *
     * @param follows 跟进记录列表
     */
    private void fillDictLabels(List<OpportunityFollowVO> follows) {
        if (CollUtil.isEmpty(follows)) {
            return;
        }

        // 获取字典数据
        Map<String, String> followTypeMap = dictItemService.getDictItems(DictCodeEnum.FOLLOW_TYPE.getValue())
                .stream().collect(Collectors.toMap(item -> item.getValue(), item -> item.getLabel()));
        Map<String, String> followResultMap = dictItemService.getDictItems(DictCodeEnum.FOLLOW_RESULT.getValue())
                .stream().collect(Collectors.toMap(item -> item.getValue(), item -> item.getLabel()));

        // 填充标签
        for (OpportunityFollowVO follow : follows) {
            follow.setFollowTypeLabel(followTypeMap.get(follow.getFollowType()));
            follow.setFollowResultLabel(followResultMap.get(follow.getFollowResult()));
        }
    }

    /**
     * 获取当前用户ID
     *
     * @return 用户ID
     */
    private Long getCurrentUserId() {
        return SecurityUtils.getUserId();
    }
}