/**
 * 字典配置文件
 * 统一管理各种状态字典和常量
 */

// 付款状态字典
export const PAYMENT_STATUS_DICT = {
  PENDING: { value: 'pending', label: '待付款', color: 'warning' },
  PAID: { value: 'paid', label: '已付款', color: 'success' },
  PARTIAL: { value: 'partial', label: '部分付款', color: 'info' },
  OVERDUE: { value: 'overdue', label: '已逾期', color: 'destructive' },
  CANCELLED: { value: 'cancelled', label: '已取消', color: 'secondary' }
} as const;

// 发票状态字典
export const INVOICE_STATUS_DICT = {
  NOT_ISSUED: { value: 'not_issued', label: '未开票', color: 'secondary' },
  ISSUED: { value: 'issued', label: '已开票', color: 'info' },
  RECEIVED: { value: 'received', label: '已收票', color: 'success' }
} as const;

// 合同状态字典
export const CONTRACT_STATUS_DICT = {
  DRAFT: { value: 'draft', label: '草稿', color: 'secondary' },
  PENDING: { value: 'pending', label: '待签署', color: 'warning' },
  ACTIVE: { value: 'active', label: '已生效', color: 'success' },
  COMPLETED: { value: 'completed', label: '已完成', color: 'info' },
  TERMINATED: { value: 'terminated', label: '已终止', color: 'destructive' },
  CANCELLED: { value: 'cancelled', label: '已作废', color: 'destructive' }
} as const;

// 字典类型定义
export type PaymentStatusKey = keyof typeof PAYMENT_STATUS_DICT;
export type InvoiceStatusKey = keyof typeof INVOICE_STATUS_DICT;
export type ContractStatusKey = keyof typeof CONTRACT_STATUS_DICT;

export interface DictItem {
  value: string;
  label: string;
  color: 'success' | 'warning' | 'info' | 'destructive' | 'secondary';
}

// 字典工具函数
export class DictUtils {
  /**
   * 获取付款状态标签
   * @param status 付款状态值
   * @returns 状态标签
   */
  static getPaymentStatusLabel(status: string): string {
    const dictItem = Object.values(PAYMENT_STATUS_DICT).find(item => item.value === status);
    return dictItem?.label || status;
  }

  /**
   * 获取付款状态配置
   * @param status 付款状态值
   * @returns 状态配置对象
   */
  static getPaymentStatusConfig(status: string): DictItem | null {
    const dictItem = Object.values(PAYMENT_STATUS_DICT).find(item => item.value === status);
    return dictItem || null;
  }

  /**
   * 获取发票状态标签
   * @param status 发票状态值
   * @returns 状态标签
   */
  static getInvoiceStatusLabel(status: string): string {
    const dictItem = Object.values(INVOICE_STATUS_DICT).find(item => item.value === status);
    return dictItem?.label || status;
  }

  /**
   * 获取发票状态配置
   * @param status 发票状态值
   * @returns 状态配置对象
   */
  static getInvoiceStatusConfig(status: string): DictItem | null {
    const dictItem = Object.values(INVOICE_STATUS_DICT).find(item => item.value === status);
    return dictItem || null;
  }

  /**
   * 获取合同状态标签
   * @param status 合同状态值
   * @returns 状态标签
   */
  static getContractStatusLabel(status: string): string {
    const dictItem = Object.values(CONTRACT_STATUS_DICT).find(item => item.value === status);
    return dictItem?.label || status;
  }

  /**
   * 获取合同状态配置
   * @param status 合同状态值
   * @returns 状态配置对象
   */
  static getContractStatusConfig(status: string): DictItem | null {
    const dictItem = Object.values(CONTRACT_STATUS_DICT).find(item => item.value === status);
    return dictItem || null;
  }

  /**
   * 获取付款状态选项列表
   * @returns 付款状态选项数组
   */
  static getPaymentStatusOptions(): DictItem[] {
    return Object.values(PAYMENT_STATUS_DICT);
  }

  /**
   * 获取发票状态选项列表
   * @returns 发票状态选项数组
   */
  static getInvoiceStatusOptions(): DictItem[] {
    return Object.values(INVOICE_STATUS_DICT);
  }

  /**
   * 获取合同状态选项列表
   * @returns 合同状态选项数组
   */
  static getContractStatusOptions(): DictItem[] {
    return Object.values(CONTRACT_STATUS_DICT);
  }
}

export const PaymentStatus = {
  PENDING: PAYMENT_STATUS_DICT.PENDING.value,
  PAID: PAYMENT_STATUS_DICT.PAID.value,
  PARTIAL: PAYMENT_STATUS_DICT.PARTIAL.value,
  OVERDUE: PAYMENT_STATUS_DICT.OVERDUE.value,
  CANCELLED: PAYMENT_STATUS_DICT.CANCELLED.value
} as const;

export const InvoiceStatus = {
  NOT_ISSUED: INVOICE_STATUS_DICT.NOT_ISSUED.value,
  ISSUED: INVOICE_STATUS_DICT.ISSUED.value,
  RECEIVED: INVOICE_STATUS_DICT.RECEIVED.value
} as const;

export const ContractStatus = {
  DRAFT: CONTRACT_STATUS_DICT.DRAFT.value,
  PENDING: CONTRACT_STATUS_DICT.PENDING.value,
  ACTIVE: CONTRACT_STATUS_DICT.ACTIVE.value,
  COMPLETED: CONTRACT_STATUS_DICT.COMPLETED.value,
  TERMINATED: CONTRACT_STATUS_DICT.TERMINATED.value,
  CANCELLED: CONTRACT_STATUS_DICT.CANCELLED.value
} as const;