package com.hntsz.boot.modules.example.converter;

import org.mapstruct.Mapper;

import com.hntsz.boot.common.base.ICrudConverter;
import com.hntsz.boot.modules.example.model.entity.Example;
import com.hntsz.boot.modules.example.model.form.ExampleForm;
import com.hntsz.boot.modules.example.model.vo.ExampleVO;

/**
 * 示例对象转换器
 *
 * <AUTHOR>
 * @since 2022/7/29
 */
@Mapper(componentModel = "spring")
public interface ExampleConverter extends ICrudConverter<Example, ExampleVO, ExampleVO, ExampleForm, ExampleForm> {


}