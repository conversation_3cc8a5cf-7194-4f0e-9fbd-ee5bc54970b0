package com.hntsz.boot.modules.contract.converter;

import com.hntsz.boot.modules.contract.model.entity.ContractPartnerRelation;
import com.hntsz.boot.modules.contract.model.form.ContractPartnerRelationForm;
import com.hntsz.boot.modules.contract.model.vo.ContractPartnerRelationVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 合同伙伴关联表对象转换器
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Mapper
public interface ContractPartnerRelationConverter {

    ContractPartnerRelationConverter INSTANCE = Mappers.getMapper(ContractPartnerRelationConverter.class);

    /**
     * 实体转换为VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    ContractPartnerRelationVO entityToVO(ContractPartnerRelation entity);

    /**
     * VO转换为实体
     *
     * @param vo VO对象
     * @return 实体对象
     */
    ContractPartnerRelation voToEntity(ContractPartnerRelationVO vo);

    /**
     * 表单转换为实体
     *
     * @param form 表单对象
     * @return 实体对象
     */
    ContractPartnerRelation formToEntity(ContractPartnerRelationForm form);

    /**
     * 实体转换为表单
     *
     * @param entity 实体对象
     * @return 表单对象
     */
    ContractPartnerRelationForm entityToForm(ContractPartnerRelation entity);
}
