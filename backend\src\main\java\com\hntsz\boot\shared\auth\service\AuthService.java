package com.hntsz.boot.shared.auth.service;

import com.hntsz.boot.core.security.model.AuthenticationToken;
import com.hntsz.boot.shared.auth.model.CaptchaInfo;
import com.hntsz.boot.shared.auth.model.OAuthConfig;

/**
 * 认证服务接口
 *
 * <AUTHOR>
 * @since 2.4.0
 */
public interface AuthService {

    /**
     * 登录
     *
     * @param username 用户名
     * @param password 密码
     * @return 登录结果
     */
    AuthenticationToken login(String username, String password);

    /**
     * 登出
     */
    void logout();

    /**
     * 获取验证码
     *
     * @return 验证码
     */
    CaptchaInfo getCaptcha();

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 登录结果
     */
    AuthenticationToken refreshToken(String refreshToken);

    /**
     * 微信小程序登录
     *
     * @param code 微信登录code
     * @return 登录结果
     */
    AuthenticationToken loginByWechat(String code);

    /**
     * 发送短信验证码
     *
     * @param mobile 手机号
     */
    void sendSmsLoginCode(String mobile);

    /**
     * 短信验证码登录
     *
     * @param mobile 手机号
     * @param code   验证码
     * @return 登录结果
     */
    AuthenticationToken loginBySms(String mobile, String code);

    /**
     * OAuth 登录
     *
     * @param provider OAuth 提供商
     * @param code     OAuth 授权码
     * @return 登录结果
     */
    AuthenticationToken loginByOAuth(String provider, String code);

    /**
     * 获取 OAuth 登出URL
     *
     * @return OAuth 登出URL
     */
    String getOAuthLogoutUrl();

    /**
     * 获取 OAuth 配置信息
     *
     * @return OAuth 配置信息
     */
    OAuthConfig getOAuthConfig();
}
