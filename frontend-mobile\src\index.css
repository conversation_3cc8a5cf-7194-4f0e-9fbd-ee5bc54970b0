@tailwind base;
@tailwind components;
@tailwind utilities;

/* 探数者综合管理平台 - 移动端设计系统 */

@layer base {
  :root {
    /* 主背景色 - 浅灰蓝 */
    --background: 210 25% 98%;
    --foreground: 215 25% 15%;

    /* 卡片背景 */
    --card: 0 0% 100%;
    --card-foreground: 215 25% 15%;

    /* 弹出层 */
    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 15%;

    /* 主色调 - 深蓝商务色 */
    --primary: 215 85% 25%;
    --primary-foreground: 0 0% 100%;
    --primary-hover: 215 85% 20%;

    /* 次要色调 - 浅蓝 */
    --secondary: 215 50% 95%;
    --secondary-foreground: 215 85% 25%;

    /* 静默色调 */
    --muted: 215 15% 95%;
    --muted-foreground: 215 10% 45%;

    /* 强调色调 - 科技蓝 */
    --accent: 200 85% 55%;
    --accent-foreground: 0 0% 100%;

    /* 危险色调 */
    --destructive: 0 75% 55%;
    --destructive-foreground: 0 0% 100%;

    /* 成功色调 */
    --success: 120 60% 40%;
    --success-foreground: 0 0% 100%;

    /* 警告色调 */
    --warning: 45 85% 55%;
    --warning-foreground: 0 0% 100%;

    /* 边框和输入框 */
    --border: 215 15% 88%;
    --input: 215 15% 95%;
    --ring: 215 85% 25%;

    /* 渐变色 */
    --gradient-primary: linear-gradient(135deg, hsl(215, 85%, 25%), hsl(215, 85%, 35%));
    --gradient-secondary: linear-gradient(135deg, hsl(200, 85%, 55%), hsl(215, 85%, 45%));
    --gradient-hero: linear-gradient(135deg, hsl(215, 85%, 15%), hsl(215, 85%, 30%));
    --gradient-card: linear-gradient(145deg, hsl(0, 0%, 100%), hsl(215, 15%, 98%));

    /* 阴影 */
    --shadow-soft: 0 2px 8px hsl(215, 25%, 25%, 0.08);
    --shadow-medium: 0 4px 12px hsl(215, 25%, 25%, 0.12);
    --shadow-strong: 0 8px 24px hsl(215, 25%, 25%, 0.16);
    --shadow-glow: 0 0 20px hsl(200, 85%, 55%, 0.3);

    /* 圆角 */
    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}