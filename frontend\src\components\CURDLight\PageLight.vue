<template>
  <div class="app-container">
    <!-- 搜索 -->
    <page-search
      v-if="props.config.searchConfig"
      ref="searchRef"
      :search-config="props.config.searchConfig"
      @query-click="handleQueryClick"
      @reset-click="handleResetClick"
    />

    <!-- 列表 -->
    <page-content
      ref="contentRef"
      :content-config="props.config.contentConfig"
      @add-click="handleAddClick"
      @edit-click="handleEditClick"
      @export-click="handleExportClick"
      @search-click="handleSearchClick"
      @filter-change="handleFilterChange"
      @operat-click="handleOperatClick"
    >
      <template v-for="(_, name) in $slots" :key="name" #[name]="slotData">
        <slot :name="name" v-bind="slotData" />
      </template>
    </page-content>

    <!-- 新增 -->
    <page-modal
      v-if="addModalConfigComputed"
      ref="addModalRef"
      :modal-config="addModalConfigComputed"
      @submit-click="handleSubmitClick"
    />

    <!-- 编辑 -->
    <page-modal
      v-if="editModalConfigComputed"
      ref="editModalRef"
      :modal-config="editModalConfigComputed"
      @submit-click="handleSubmitClick"
    />
  </div>
</template>

<script setup lang="ts">
import type { IObject } from "@/components/CURD/types";
import usePage from "@/components/CURD/usePage";
import type { PageLightProps } from "./types";
import type { IOperateData } from "@/components/CURD/types";

const props = defineProps<{
  config: PageLightProps;
}>();

const emit = defineEmits<{
  addClick: [];
  editClick: [IObject];
  operatClick: [IOperateData];
}>();

const {
  searchRef,
  contentRef,
  addModalRef,
  editModalRef,
  handleQueryClick,
  handleResetClick,
  // handleAddClick,
  // handleEditClick,
  handleSubmitClick,
  handleExportClick,
  handleSearchClick,
  handleFilterChange,
} = usePage();

const addModalConfigComputed = computed(() => {
  if (!props.config.addModalConfig) return null;

  const newConfig = { ...props.config.addModalConfig };
  if (newConfig.dialog && !newConfig.dialog.title) {
    newConfig.dialog = {
      ...newConfig.dialog,
      title: "新增" + props.config.subjectName,
    };
  }
  return newConfig;
});

const editModalConfigComputed = computed(() => {
  if (!props.config.editModalConfig && !props.config.editModalUseAddModal) return null;

  const newConfig = props.config.editModalUseAddModal
    ? props.config.addModalConfig
    : props.config.editModalConfig;

  if (!newConfig) return null;

  if (newConfig.dialog && !newConfig.dialog.title) {
    newConfig.dialog = {
      ...newConfig.dialog,
      title: "编辑" + props.config.subjectName,
    };
  }

  if (props.config.editModalUseAddModal && props.config.editModelSubmit) {
    newConfig.formAction = props.config.editModelSubmit;
  }
  return newConfig;
});

// 新增
async function handleAddClick() {
  if (props.config.addModalConfig) {
    addModalRef.value?.setModalVisible();
  } else {
    emit("addClick");
  }
}
// 编辑
async function handleEditClick(row: IObject) {
  if (!editModalConfigComputed.value) {
    emit("editClick", row);
    return;
  }

  editModalRef.value?.handleDisabled(false);
  editModalRef.value?.setModalVisible();

  if (props.config.editModelFetch) {
    const data = await props.config.editModelFetch(row);
    editModalRef.value?.setFormData(data);
  } else {
    editModalRef.value?.setFormData(row);
  }
}

// 其他操作列
function handleOperatClick(data: IOperateData) {
  emit("operatClick", data);
}

function fetchPageData() {
  contentRef.value?.fetchPageData();
}

defineExpose({ fetchPageData });
</script>
