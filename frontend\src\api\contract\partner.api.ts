import request from "@/utils/request";

const PARTNER_BASE_URL = "/api/v1/partners";

const PartnerAPI = {
  /**
   * 获取业务伙伴分页列表
   *
   * @param queryParams 查询参数
   * @returns 业务伙伴分页列表
   */
  getPage(queryParams: PartnerPageQuery) {
    return request<any, PageResult<PartnerPageVO[]>>({
      url: `${PARTNER_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 获取业务伙伴详情
   *
   * @param id 业务伙伴ID
   * @returns 业务伙伴详情
   */
  getDetail(id: string) {
    return request<any, PartnerVO>({
      url: `${PARTNER_BASE_URL}/${id}`,
      method: "get",
    });
  },

  /**
   * 获取业务伙伴表单数据
   *
   * @param id 业务伙伴ID
   * @returns 业务伙伴表单数据
   */
  getFormData(id: string) {
    return request<any, PartnerForm>({
      url: `${PARTNER_BASE_URL}/${id}/form`,
      method: "get",
    });
  },

  /**
   * 新增业务伙伴
   *
   * @param data 业务伙伴表单数据
   * @returns 操作结果
   */
  create(data: PartnerForm) {
    return request({
      url: PARTNER_BASE_URL,
      method: "post",
      data: data,
    });
  },

  /**
   * 修改业务伙伴
   *
   * @param id 业务伙伴ID
   * @param data 业务伙伴表单数据
   * @returns 操作结果
   */
  update(id: string, data: PartnerForm) {
    return request({
      url: `${PARTNER_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 删除业务伙伴
   *
   * @param ids 业务伙伴ID，多个以英文逗号(,)分割
   * @returns 操作结果
   */
  delete(ids: string) {
    return request({
      url: `${PARTNER_BASE_URL}/${ids}`,
      method: "delete",
    });
  },

  /**
   * 获取业务伙伴选项列表
   *
   * @returns 业务伙伴选项列表
   */
  getOptions() {
    return request<any, OptionType[]>({
      url: `${PARTNER_BASE_URL}/options`,
      method: "get",
    });
  },
};

export default PartnerAPI;

/** 业务伙伴分页查询参数 */
export interface PartnerPageQuery extends PageQuery {
  /** 搜索关键字 */
  keywords?: string;
  /** 伙伴类型 */
  partnerType?: string;
  /** 是否我司或旗下企业 */
  isOurCompany?: boolean;
  /** 状态 */
  status?: string;
  /** 证件类型 */
  certificateType?: string;
}

/** 业务伙伴分页VO */
export interface PartnerPageVO {
  /** 主键ID */
  id: string;
  /** 伙伴名称 */
  partnerName: string;
  /** 伙伴编码 */
  partnerCode?: string;
  /** 是否我司或旗下企业 */
  isOurCompany: boolean;
  /** 伙伴类型 */
  partnerType: string;
  /** 伙伴类型标签 */
  partnerTypeLabel?: string;
  /** 联系人 */
  contactPerson?: string;
  /** 联系电话 */
  contactPhone?: string;
  /** 联系邮箱 */
  contactEmail?: string;
  /** 状态 */
  status: string;
  /** 创建时间 */
  createTime: string;
}

/** 业务伙伴详情VO */
export interface PartnerVO {
  /** 主键ID */
  id: string;
  /** 伙伴名称 */
  partnerName: string;
  /** 伙伴编码 */
  partnerCode?: string;
  /** 是否我司或旗下企业 */
  isOurCompany: boolean;
  /** 伙伴类型 */
  partnerType: string;
  /** 伙伴类型标签 */
  partnerTypeLabel?: string;
  /** 法定代表人 */
  legalRepresentative?: string;
  /** 联系人 */
  contactPerson?: string;
  /** 联系电话 */
  contactPhone?: string;
  /** 联系邮箱 */
  contactEmail?: string;
  /** 地址 */
  address?: string;
  /** 证件类型 */
  certificateType?: string;
  /** 证件类型标签 */
  certificateTypeLabel?: string;
  /** 证件号码 */
  certificateNumber?: string;
  /** 税号 */
  taxNumber?: string;
  /** 开户银行 */
  bankName?: string;
  /** 银行账号 */
  bankAccount?: string;
  /** 状态 */
  status: string;
  /** 备注 */
  remark?: string;
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime: string;
}

/** 业务伙伴表单对象 */
export interface PartnerForm {
  /** 主键ID */
  id?: string;
  /** 伙伴名称 */
  partnerName: string;
  /** 伙伴编码 */
  partnerCode?: string;
  /** 是否我司或旗下企业 */
  isOurCompany?: boolean;
  /** 伙伴类型 */
  partnerType: string;
  /** 法定代表人 */
  legalRepresentative?: string;
  /** 联系人 */
  contactPerson?: string;
  /** 联系电话 */
  contactPhone?: string;
  /** 联系邮箱 */
  contactEmail?: string;
  /** 地址 */
  address?: string;
  /** 证件类型 */
  certificateType?: string;
  /** 证件号码 */
  certificateNumber?: string;
  /** 税号 */
  taxNumber?: string;
  /** 开户银行 */
  bankName?: string;
  /** 银行账号 */
  bankAccount?: string;
  /** 状态 */
  status?: string;
  /** 备注 */
  remark?: string;
}
