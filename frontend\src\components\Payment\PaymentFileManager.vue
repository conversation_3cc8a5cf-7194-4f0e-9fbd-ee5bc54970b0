<template>
  <div class="payment-file-manager">
    <!-- 文件列表表格 -->
    <div class="file-list-section">
      <div class="section-header">
        <h4>已上传文件</h4>
        <el-button type="primary" @click="showUploadDialog = true">
          <el-icon><Plus /></el-icon>
          上传附件
        </el-button>
      </div>
      
      <el-table 
        :data="fileList" 
        border 
        style="width: 100%"
        empty-text="暂无文件"
      >
        <el-table-column prop="fileName" label="文件名" min-width="200">
          <template #default="{ row }">
            <el-link 
              :href="getFileUrl(row.filePath)" 
              target="_blank" 
              type="primary"
              :underline="false"
            >
              <el-icon class="mr-1">
                <Document v-if="row.fileType === 'DOCUMENT'" />
                <Picture v-else-if="row.fileType === 'IMAGE'" />
                <VideoCamera v-else-if="row.fileType === 'VIDEO'" />
                <Microphone v-else-if="row.fileType === 'AUDIO'" />
                <Document v-else />
              </el-icon>
              {{ row.fileName }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="fileType" label="文件类型" width="100">
          <template #default="{ row }">
            <el-tag size="small" :type="getFileTypeTagType(row.fileType)">
              {{ getFileTypeLabel(row.fileType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="fileSize" label="文件大小" width="120">
          <template #default="{ row }">
            {{ formatFileSize(row.fileSize) }}
          </template>
        </el-table-column>
        
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              link
              @click="handlePreviewFile(row)"
              v-if="canPreview(row.fileType)"
            >
              下载
            </el-button>
            <el-button
              type="danger"
              size="small"
              link
              @click="handleDeleteFile(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 文件上传对话框 -->
    <el-dialog 
      v-model="showUploadDialog" 
      title="上传附件" 
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="upload-dialog-content">
        <!-- 切换模式按钮 -->
        <div class="mode-switch">
          <el-radio-group v-model="uploadMode" @change="handleModeChange">
            <el-radio-button value="upload">上传文件</el-radio-button>
            <el-radio-button value="select">选择已有文件</el-radio-button>
          </el-radio-group>
        </div>

        <!-- 上传模式 -->
        <div v-if="uploadMode === 'upload'" class="upload-section">
          <el-upload
            ref="uploadRef"
            class="upload-dragger"
            drag
            multiple
            :auto-upload="false"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :before-upload="validateFile"
            :file-list="uploadFileList"
            accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif,.zip,.rar"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持多文件上传，单个文件大小不超过 20MB<br>
                支持格式：PDF、Word、Excel、PPT、图片、压缩包等
              </div>
            </template>
          </el-upload>

          <!-- 上传进度 -->
          <div v-if="uploading && uploadProgress.length > 0" class="upload-progress">
            <h5>上传进度</h5>
            <div v-for="(progress, index) in uploadProgress" :key="index" class="progress-item">
              <div class="progress-info">
                <span class="file-name">{{ progress.fileName }}</span>
                <span class="progress-text">{{ progress.percent }}%</span>
              </div>
              <el-progress
                :percentage="progress.percent"
                :status="progress.status"
                :stroke-width="6"
              />
            </div>
          </div>
        </div>

        <!-- 选择模式 -->
        <div v-if="uploadMode === 'select'" class="select-section">
          <!-- 搜索区域 -->
          <div class="search-section">
            <el-row :gutter="16">
              <el-col :span="8">
                <el-input
                  v-model="searchKeyword"
                  placeholder="关键字"
                  clearable
                  @input="handleSearch"
                />
              </el-col>
              <el-col :span="6">
                <el-select
                  v-model="searchFileType"
                  placeholder="文件类型"
                  clearable
                  @change="handleSearch"
                >
                  <el-option label="全部" value="" />
                  <el-option label="文档" value="DOCUMENT" />
                  <el-option label="图片" value="IMAGE" />
                  <el-option label="视频" value="VIDEO" />
                  <el-option label="音频" value="AUDIO" />
                  <el-option label="其他" value="OTHER" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-button type="primary" @click="handleSearch">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="handleResetSearch">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-col>
              <el-col :span="4">
                <el-button type="success" @click="handleUploadNew">
                  <el-icon><Upload /></el-icon>
                  上传新文件
                </el-button>
              </el-col>
            </el-row>
          </div>

          <!-- 文件列表表格 -->
          <div class="file-list-section">
            <el-table
              v-loading="loadingFiles"
              :data="availableFiles"
              @selection-change="handleFileSelectionChange"
              max-height="400"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column label="内容" width="100">
                <template #default="{ row }">
                  <el-image
                    v-if="row.fileType === 'IMAGE'"
                    :src="getFileUrl(row.filePath)"
                    :preview-src-list="[getFileUrl(row.filePath)]"
                    style="width: 40px; height: 40px"
                    fit="cover"
                  />
                  <el-icon v-else size="24">
                    <Document />
                  </el-icon>
                </template>
              </el-table-column>
              <el-table-column prop="fileName" label="文件名" min-width="200" show-overflow-tooltip />
              <el-table-column label="文件类型" width="100">
                <template #default="{ row }">
                  <el-tag :type="getFileTypeTagType(row.fileType)">
                    {{ getFileTypeLabel(row.fileType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="文件大小" width="100">
                <template #default="{ row }">
                  {{ formatFileSize(row.fileSize) }}
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-section">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :total="totalFiles"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handlePageSizeChange"
                @current-change="handlePageChange"
              />
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleConfirm"
            :loading="uploading"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage, ElMessageBox, type UploadFile, type UploadFiles } from 'element-plus'
import {
  Plus,
  Document,
  Picture,
  VideoCamera,
  Microphone,
  UploadFilled,
  Search,
  Refresh,
  Upload
} from '@element-plus/icons-vue'
import { paymentAttachmentApi } from '@/api/contract/payment-attachment.api'
import type { PaymentAttachmentVO } from '@/api/contract/payment-attachment.api'
import AttachmentAPI, { AttachmentVO, AttachmentQuery } from '@/api/attachment/attachment.api'
import FileAPI from '@/api/file.api'

interface Props {
  paymentId?: string
}

const props = defineProps<Props>()

// 响应式数据
const fileList = ref<PaymentAttachmentVO[]>([])
const showUploadDialog = ref(false)
const uploadMode = ref<'upload' | 'select'>('upload')
const uploadFileList = ref<UploadFile[]>([])
const selectedAttachments = ref<AttachmentVO[]>([])
const uploading = ref(false)
const uploadRef = ref()

// 上传进度相关
interface UploadProgress {
  fileName: string
  percent: number
  status: 'success' | 'exception' | 'warning' | ''
}
const uploadProgress = ref<UploadProgress[]>([])

// 选择已有文件相关
const searchKeyword = ref('')
const searchFileType = ref('')
const availableFiles = ref<AttachmentVO[]>([])
const loadingFiles = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const totalFiles = ref(0)

// 监听付款ID变化
watch(() => props.paymentId, (newId) => {
  if (newId) {
    loadFileList()
  } else {
    fileList.value = []
  }
}, { immediate: true })

// 监听上传模式变化，加载可用文件列表
watch(uploadMode, (newMode) => {
  if (newMode === 'select') {
    loadAvailableFiles()
  }
})

// 加载文件列表
async function loadFileList() {
  if (!props.paymentId) return
  
  try {
    const data = await paymentAttachmentApi.getList(props.paymentId)
    fileList.value = data
  } catch (error) {
    console.error('加载文件列表失败:', error)
    fileList.value = []
  }
}

// 处理模式切换
function handleModeChange() {
  uploadFileList.value = []
  selectedAttachments.value = []
}

// 处理文件选择
function handleFileChange(_file: UploadFile, files: UploadFiles) {
  uploadFileList.value = files
}

// 处理文件移除
function handleFileRemove(_file: UploadFile, files: UploadFiles) {
  uploadFileList.value = files
}

// 文件验证
function validateFile(file: File): boolean {
  // 检查文件大小 (20MB = 20 * 1024 * 1024 bytes)
  const maxSize = 20 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error(`文件 ${file.name} 大小超过 20MB 限制`)
    return false
  }

  // 检查文件类型
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'application/zip',
    'application/x-rar-compressed'
  ]

  if (!allowedTypes.includes(file.type) && !file.name.match(/\.(pdf|doc|docx|xls|xlsx|ppt|pptx|txt|jpg|jpeg|png|gif|zip|rar)$/i)) {
    ElMessage.error(`文件 ${file.name} 格式不支持`)
    return false
  }

  return false // 阻止自动上传，我们手动处理
}

// 处理取消
function handleCancel() {
  showUploadDialog.value = false
  uploadFileList.value = []
  selectedAttachments.value = []
  uploadProgress.value = []
  uploadMode.value = 'upload'
}

// 处理确认
async function handleConfirm() {
  if (!props.paymentId) {
    ElMessage.error('付款ID不能为空')
    return
  }

  uploading.value = true
  
  try {
    if (uploadMode.value === 'upload') {
      await handleUploadFiles()
    } else {
      await handleSelectFiles()
    }
    
    ElMessage.success('操作成功')
    showUploadDialog.value = false
    await loadFileList()
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  } finally {
    uploading.value = false
    uploadFileList.value = []
    selectedAttachments.value = []
    uploadMode.value = 'upload'
    // 延迟清除进度，让用户看到最终结果
    setTimeout(() => {
      uploadProgress.value = []
    }, 2000)
  }
}

// 处理文件上传
async function handleUploadFiles() {
  if (uploadFileList.value.length === 0) {
    ElMessage.warning('请选择要上传的文件')
    return
  }

  // 初始化进度
  uploadProgress.value = uploadFileList.value.map(file => ({
    fileName: file.name,
    percent: 0,
    status: '' as const
  }))

  const attachmentIds: string[] = []

  try {
    // 逐个上传文件
    for (let i = 0; i < uploadFileList.value.length; i++) {
      const uploadFile = uploadFileList.value[i]
      if (uploadFile.raw) {
        try {
          // 更新进度为上传中
          uploadProgress.value[i].percent = 50
          uploadProgress.value[i].status = ''

          const result = await AttachmentAPI.upload(uploadFile.raw)

          if (result.id) {
            attachmentIds.push(result.id)
            // 上传成功
            uploadProgress.value[i].percent = 100
            uploadProgress.value[i].status = 'success'
          } else {
            throw new Error('上传失败')
          }
        } catch (error) {
          console.error(`文件 ${uploadFile.name} 上传失败:`, error)
          uploadProgress.value[i].percent = 100
          uploadProgress.value[i].status = 'exception'
          ElMessage.error(`文件 ${uploadFile.name} 上传失败`)
        }
      }
    }

    // 批量关联到付款
    if (attachmentIds.length > 0) {
      await paymentAttachmentApi.batchAdd(props.paymentId!, attachmentIds)
    }

    if (attachmentIds.length === uploadFileList.value.length) {
      ElMessage.success(`成功上传 ${attachmentIds.length} 个文件`)
    } else if (attachmentIds.length > 0) {
      ElMessage.warning(`成功上传 ${attachmentIds.length} 个文件，${uploadFileList.value.length - attachmentIds.length} 个文件上传失败`)
    }
  } catch (error) {
    console.error('批量关联文件失败:', error)
    ElMessage.error('文件关联失败')
  }
}

// 处理选择文件
async function handleSelectFiles() {
  if (selectedAttachments.value.length === 0) {
    ElMessage.warning('请选择要关联的文件')
    return
  }

  const attachmentIds = selectedAttachments.value
    .map(item => item.id)
    .filter(id => id) as string[]
  
  if (attachmentIds.length > 0) {
    await paymentAttachmentApi.batchAdd(props.paymentId!, attachmentIds)
  }
}

// 删除文件
async function handleDeleteFile(file: PaymentAttachmentVO) {
  try {
    await ElMessageBox.confirm('确认删除该文件?', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    
    await paymentAttachmentApi.delete(file.paymentId, file.attachmentId)
    ElMessage.success('删除成功')
    await loadFileList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 获取文件URL
function getFileUrl(filePath: string): string {
  return FileAPI.getFullUrl(filePath)
}

// 获取文件类型标签类型
function getFileTypeTagType(fileType: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    'DOCUMENT': 'primary',
    'IMAGE': 'success',
    'VIDEO': 'warning',
    'AUDIO': 'info',
    'OTHER': 'info'
  }
  return typeMap[fileType] || 'primary'
}

// 获取文件类型标签
function getFileTypeLabel(fileType: string): string {
  const labelMap: Record<string, string> = {
    'DOCUMENT': '文档',
    'IMAGE': '图片',
    'VIDEO': '视频',
    'AUDIO': '音频',
    'OTHER': '其他'
  }
  return labelMap[fileType] || '未知'
}

// 格式化文件大小
function formatFileSize(size: number): string {
  if (!size) return '0 B'

  const units = ['B', 'KB', 'MB', 'GB']
  let index = 0
  let fileSize = size

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024
    index++
  }

  return `${fileSize.toFixed(1)} ${units[index]}`
}

// 判断文件是否可以预览
function canPreview(fileType: string): boolean {
  const previewableTypes = ['IMAGE', 'DOCUMENT']
  return previewableTypes.includes(fileType)
}

// 处理文件预览
function handlePreviewFile(file: PaymentAttachmentVO) {
  const fileUrl = getFileUrl(file.filePath)

  if (file.fileType === 'IMAGE') {
    // 图片预览 - 在新窗口打开
    window.open(fileUrl, '_blank')
  } else if (file.fileType === 'DOCUMENT') {
    // 文档预览 - 在新窗口打开
    window.open(fileUrl, '_blank')
  } else {
    // 其他类型直接下载
    const link = document.createElement('a')
    link.href = fileUrl
    link.download = file.fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// 搜索文件
async function handleSearch() {
  await loadAvailableFiles()
}

// 重置搜索
function handleResetSearch() {
  searchKeyword.value = ''
  searchFileType.value = ''
  currentPage.value = 1
  loadAvailableFiles()
}

// 上传新文件
function handleUploadNew() {
  // 这里可以打开文件上传对话框或者切换到上传模式
  uploadMode.value = 'upload'
}

// 文件选择变化
function handleFileSelectionChange(selection: AttachmentVO[]) {
  selectedAttachments.value = selection
}

// 分页大小变化
function handlePageSizeChange(newSize: number) {
  pageSize.value = newSize
  currentPage.value = 1
  loadAvailableFiles()
}

// 页码变化
function handlePageChange(newPage: number) {
  currentPage.value = newPage
  loadAvailableFiles()
}

// 加载可用文件列表
async function loadAvailableFiles() {
  loadingFiles.value = true
  try {
    const params: AttachmentQuery = {
      keywords: searchKeyword.value || undefined
    }

    const response = await AttachmentAPI.getList(params)

    // 确保 response 是数组
    let filteredFiles: AttachmentVO[] = []
    if (Array.isArray(response)) {
      filteredFiles = response
    } else if (response && Array.isArray((response as any).records)) {
      // 如果返回的是分页对象
      filteredFiles = (response as any).records
    } else if (response && Array.isArray((response as any).data)) {
      // 如果返回的是包装对象
      filteredFiles = (response as any).data
    }

    // 按文件类型过滤
    if (searchFileType.value) {
      filteredFiles = filteredFiles.filter(file => file.fileType === searchFileType.value)
    }

    // 计算分页
    totalFiles.value = filteredFiles.length
    const startIndex = (currentPage.value - 1) * pageSize.value
    const endIndex = startIndex + pageSize.value
    availableFiles.value = filteredFiles.slice(startIndex, endIndex)
  } catch (error) {
    console.error('加载文件列表失败:', error)
    // 设置为空数组，避免显示错误
    availableFiles.value = []
    totalFiles.value = 0
  } finally {
    loadingFiles.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  loadFileList
})
</script>

<style scoped>
.payment-file-manager {
  width: 100%;
}

.file-list-section {
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.upload-dialog-content {
  padding: 16px 0;
}

.mode-switch {
  margin-bottom: 24px;
  text-align: center;
}

.upload-section {
  min-height: 300px;
}

.upload-dragger {
  width: 100%;
}

.upload-dragger :deep(.el-upload-dragger) {
  width: 100%;
  height: 200px;
}

.select-section {
  min-height: 300px;
}

.dialog-footer {
  text-align: right;
}

.mr-1 {
  margin-right: 4px;
}

/* 上传进度样式 */
.upload-progress {
  margin-top: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.upload-progress h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.progress-item {
  margin-bottom: 12px;
}

.progress-item:last-child {
  margin-bottom: 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.file-name {
  font-size: 13px;
  color: #606266;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.progress-text {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

/* 选择文件样式 */
.select-section {
  padding: 16px 0;
}

.search-section {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.file-list-section {
  margin-top: 16px;
}

.pagination-section {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}
</style>